/**
 * Excel模板管理服务
 * 专门处理Excel模板的生成、管理和缓存
 */
import { Provide } from '@midwayjs/core';
import { join } from 'path';
import { promises as fs } from 'fs';
import { XlsxUtils } from '../common/xlsxUtils';
import {
  ExcelConfigs,
  type ExcelTemplateConfig,
} from '../config/excel-configs';

// 定义模板类型
export type TemplateType =
  | 'HISTORICAL_ELEMENT'
  | 'MOUNTAIN'
  | 'WATER_SYSTEM'
  | 'RELATIONSHIP';

@Provide()
export class ExcelTemplateService {
  private readonly templateDir = join(process.cwd(), 'public', 'templates');
  private readonly templateCache = new Map<string, Buffer>();

  /**
   * 获取模板配置
   */
  getTemplateConfig(templateType: TemplateType): ExcelTemplateConfig {
    const configMap = {
      HISTORICAL_ELEMENT: ExcelConfigs.HistoricalElement.Import,
      MOUNTAIN: ExcelConfigs.Mountain.Import,
      WATER_SYSTEM: ExcelConfigs.WaterSystem.Import,
      RELATIONSHIP: ExcelConfigs.Relationship.Import,
    };

    const config = configMap[templateType];
    if (!config) {
      throw new Error(`未找到模板配置: ${templateType}`);
    }
    return config;
  }

  /**
   * 生成模板文件
   */
  async generateTemplateFile(
    templateType: TemplateType,
    fileName?: string
  ): Promise<string> {
    const config = this.getTemplateConfig(templateType);
    const templateFileName = fileName || this.getDefaultFileName(templateType);

    try {
      // 检查文件是否已存在
      const filePath = join(this.templateDir, templateFileName);
      await fs.access(filePath);
      return `/public/templates/${templateFileName}`;
    } catch {
      // 文件不存在，生成新的
      return await this.createTemplateFile(config, templateFileName);
    }
  }

  /**
   * 强制重新生成模板文件
   */
  async regenerateTemplateFile(
    templateType: TemplateType,
    fileName?: string
  ): Promise<string> {
    const config = this.getTemplateConfig(templateType);
    const templateFileName = fileName || this.getDefaultFileName(templateType);

    // 清除缓存
    this.templateCache.delete(templateFileName);

    return await this.createTemplateFile(config, templateFileName);
  }

  /**
   * 获取模板Buffer（用于直接下载）
   */
  async getTemplateBuffer(templateType: TemplateType): Promise<Buffer> {
    const config = this.getTemplateConfig(templateType);
    const cacheKey = `buffer_${templateType}`;

    // 检查缓存
    if (this.templateCache.has(cacheKey)) {
      return this.templateCache.get(cacheKey)!;
    }

    // 生成新的Buffer
    const buffer = XlsxUtils.generateTemplate(config);

    // 缓存Buffer（小文件才缓存）
    if (buffer.length < 1024 * 1024) {
      // 小于1MB
      this.templateCache.set(cacheKey, buffer);
    }

    return buffer;
  }

  /**
   * 批量生成所有模板文件
   */
  async generateAllTemplates(): Promise<{ [key: string]: string }> {
    const results: { [key: string]: string } = {};
    const templateTypes: TemplateType[] = [
      'HISTORICAL_ELEMENT',
      'MOUNTAIN',
      'WATER_SYSTEM',
      'RELATIONSHIP',
    ];

    for (const templateType of templateTypes) {
      try {
        const url = await this.generateTemplateFile(templateType);
        results[templateType] = url;
        console.log(`✅ 模板生成成功: ${templateType} -> ${url}`);
      } catch (error) {
        console.error(`❌ 模板生成失败: ${templateType}`, error.message);
        throw error;
      }
    }

    return results;
  }

  /**
   * 清除模板缓存
   */
  clearCache(): void {
    this.templateCache.clear();
    console.log('📄 模板缓存已清除');
  }

  /**
   * 获取缓存状态
   */
  getCacheStatus(): {
    size: number;
    keys: string[];
    totalSize: number;
  } {
    const keys = Array.from(this.templateCache.keys());
    const totalSize = Array.from(this.templateCache.values()).reduce(
      (sum, buffer) => sum + buffer.length,
      0
    );

    return {
      size: this.templateCache.size,
      keys,
      totalSize,
    };
  }

  /**
   * 创建模板文件
   */
  private async createTemplateFile(
    config: ExcelTemplateConfig,
    fileName: string
  ): Promise<string> {
    console.log(`📄 开始生成模板: ${fileName}`);

    try {
      // 生成Excel Buffer
      const buffer = XlsxUtils.generateTemplate(config);

      // 确保目录存在
      await this.ensureDirectoryExists();

      // 写入文件
      const filePath = join(this.templateDir, fileName);
      await fs.writeFile(filePath, buffer);

      console.log(`📄 模板生成完成: ${fileName} (${buffer.length} bytes)`);
      return `/public/templates/${fileName}`;
    } catch (error) {
      console.error(`❌ 模板生成失败: ${fileName}`, error.message);
      throw error;
    }
  }

  /**
   * 确保模板目录存在
   */
  private async ensureDirectoryExists(): Promise<void> {
    try {
      await fs.access(this.templateDir);
    } catch {
      await fs.mkdir(this.templateDir, { recursive: true });
    }
  }

  /**
   * 获取默认文件名
   */
  private getDefaultFileName(templateType: TemplateType): string {
    const fileNames: Record<TemplateType, string> = {
      HISTORICAL_ELEMENT: 'historical_element_import_template.xlsx',
      MOUNTAIN: 'mountain_import_template.xlsx',
      WATER_SYSTEM: 'water_system_import_template.xlsx',
      RELATIONSHIP: 'relationship_import_template.xlsx',
    };

    return fileNames[templateType];
  }

  /**
   * 自定义模板生成（用于特殊需求）
   */
  async generateCustomTemplate(
    config: ExcelTemplateConfig,
    fileName: string
  ): Promise<string> {
    return await this.createTemplateFile(config, fileName);
  }

  /**
   * 验证模板配置
   */
  validateTemplateConfig(config: ExcelTemplateConfig): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!config.title) {
      errors.push('模板标题不能为空');
    }

    if (!config.headers || config.headers.length === 0) {
      errors.push('表头配置不能为空');
    }

    if (config.headers) {
      config.headers.forEach((header, index) => {
        if (!header.label) {
          errors.push(`第${index + 1}个表头的标签不能为空`);
        }
        if (!header.key) {
          errors.push(`第${index + 1}个表头的键名不能为空`);
        }
      });
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * 获取模板统计信息
   */
  async getTemplateStats(): Promise<{
    totalTemplates: number;
    templateTypes: TemplateType[];
    cacheStatus: {
      size: number;
      keys: string[];
      totalSize: number;
    };
    diskUsage: { [key: string]: number };
  }> {
    const templateTypes: TemplateType[] = [
      'HISTORICAL_ELEMENT',
      'MOUNTAIN',
      'WATER_SYSTEM',
      'RELATIONSHIP',
    ];
    const diskUsage: { [key: string]: number } = {};

    // 检查磁盘上的文件大小
    for (const templateType of templateTypes) {
      try {
        const fileName = this.getDefaultFileName(templateType);
        const filePath = join(this.templateDir, fileName);
        const stats = await fs.stat(filePath);
        diskUsage[templateType] = stats.size;
      } catch {
        diskUsage[templateType] = 0;
      }
    }

    return {
      totalTemplates: templateTypes.length,
      templateTypes,
      cacheStatus: this.getCacheStatus(),
      diskUsage,
    };
  }
}
