# 仪表盘 API 文档

## 概述

仪表盘模块提供数据可视化和统计分析功能，包括地图数据展示、统计分析、数据概览等接口，支持多维度的数据查询和展示。

本文档包含两部分API：
1. 公开接口：无需认证，供前端地图和公开数据展示使用
2. 管理端接口：需要管理员权限，提供更详细的数据分析和管理功能

---

## 统一响应格式

### 成功响应
所有接口成功时返回统一格式：
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应
所有接口失败时返回统一格式：
```json
{
  "errCode": 400,  // 错误码
  "msg": "错误信息"  // 错误描述
}
```

---

## 地图数据接口

### 获取地图数据

#### 接口信息

- **URL**: `/public/map/data`
- **方法**: `GET`
- **认证**: 无需认证

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | string | 否 | 数据类型，可选值：mountain、water_system、historical_element |
| regionId | number | 否 | 区域ID，筛选指定区域的数据 |
| typeId | number | 否 | 类型ID，筛选指定类型的历史要素 |

#### 请求示例

```bash
# 获取所有地图数据
curl -X GET "http://localhost:7001/public/map/data"

# 获取指定类型的数据
curl -X GET "http://localhost:7001/public/map/data?type=mountain"

# 获取指定区域的数据
curl -X GET "http://localhost:7001/public/map/data?regionId=1"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "mountains": [
      {
        "id": 1,
        "name": "华山",
        "code": "HS001",
        "longitude": 110.0910,
        "latitude": 34.4880,
        "height": 2154
      }
    ],
    "waterSystems": [
      {
        "id": 1,
        "name": "渭河",
        "code": "WH001",
        "longitude": 108.9633,
        "latitude": 34.2658,
        "lengthArea": "818公里"
      }
    ],
    "historicalElements": [
      {
        "id": 1,
        "name": "大雁塔",
        "code": "DYT001",
        "constructionLongitude": 108.9640,
        "constructionLatitude": 34.2180,
        "constructionTime": "652-01-01T00:00:00.000Z"
      }
    ]
  }
}
```

### 获取实体详情

#### 接口信息

- **URL**: `/public/map/detail`
- **方法**: `GET`
- **认证**: 无需认证

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | string | 是 | 数据类型，可选值：mountain、water_system、historical_element |
| id | number | 是 | 实体ID |

#### 请求示例

```bash
curl -X GET "http://localhost:7001/public/map/detail?type=mountain&id=1"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "entity": {
      "id": 1,
      "name": "华山",
      "code": "HS001",
      "longitude": 110.0910,
      "latitude": 34.4880,
      "height": 2154,
      "historicalRecords": "华山，古称"西岳"，雅称"太华山"，为中国著名的五岳之一。",
      "regionDictId": 1
    },
    "photos": [],
    "relationships": []
  }
}
```

### 获取地图统计数据

#### 接口信息

- **URL**: `/public/map/statistics`
- **方法**: `GET`
- **认证**: 无需认证

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID，筛选特定区域的统计数据 |

#### 请求示例

```bash
curl -X GET "http://localhost:7001/public/map/statistics?regionId=1"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "mountains": {
      "total": 25,
      "byRegion": [
        {
          "regionId": 1,
          "regionName": "关中地区",
          "count": 15
        }
      ]
    },
    "waterSystems": {
      "total": 15,
      "totalLength": 2500,
      "byRegion": [
        {
          "regionId": 1,
          "regionName": "关中地区",
          "count": 10
        }
      ]
    },
    "historicalElements": {
      "total": 50,
      "byType": [
        {
          "typeId": 1,
          "typeName": "佛塔",
          "count": 15
        }
      ],
      "byRegion": [
        {
          "regionId": 1,
          "regionName": "关中地区",
          "count": 35
        }
      ]
    },
    "total": {
      "mountain": 25,
      "waterSystem": 15,
      "historicalElement": 50
    }
  }
}
```

---

## 统计分析接口

### 获取基础统计数据

#### 接口信息

- **URL**: `/public/statistic/basic`
- **方法**: `GET`
- **认证**: 无需认证

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID |
| startTime | string | 否 | 开始时间，ISO日期格式 |
| endTime | string | 否 | 结束时间，ISO日期格式 |

#### 请求示例

```bash
curl -X GET "http://localhost:7001/public/statistic/basic?regionId=1"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "counts": {
      "mountain": 25,
      "waterSystem": 15,
      "historicalElement": 50
    },
    "regionStats": [
      {
        "region": "关中地区",
        "regionId": 1,
        "mountainCount": 15,
        "waterSystemCount": 10,
        "historicalElementCount": 35,
        "total": 60
      }
    ],
    "timelineData": [
      {
        "year": 652,
        "count": 1,
        "elements": [
          {
            "id": 1,
            "name": "大雁塔",
            "type": "historical_element"
          }
        ]
      }
    ]
  }
}
```

### 获取综合统计报告

#### 接口信息

- **URL**: `/public/statistic/comprehensive`
- **方法**: `GET`
- **认证**: 无需认证

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID |

#### 请求示例

```bash
curl -X GET "http://localhost:7001/public/statistic/comprehensive?regionId=1"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "basic": {
      "counts": {
        "mountain": 25,
        "waterSystem": 15,
        "historicalElement": 50
      },
      "regionStats": [],
      "timelineData": []
    },
    "detailed": {
      "mountains": {
        "total": 25,
        "byRegion": []
      },
      "waterSystems": {
        "total": 15,
        "totalLength": 2500,
        "byRegion": []
      },
      "historicalElements": {
        "total": 50,
        "byType": [],
        "byRegion": []
      }
    },
    "summary": {
      "totalEntities": 90,
      "regionCoverage": 3,
      "timeSpan": {
        "earliest": 652,
        "latest": 1644,
        "span": 992
      }
    }
  }
}
```

### 获取时间轴数据

#### 接口信息

- **URL**: `/public/statistic/timeline`
- **方法**: `GET`
- **认证**: 无需认证

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID |

#### 请求示例

```bash
curl -X GET "http://localhost:7001/public/statistic/timeline?regionId=1"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "year": 652,
      "count": 1,
      "elements": [
        {
          "id": 1,
          "name": "大雁塔",
          "type": "historical_element"
        }
      ]
    },
    {
      "year": 707,
      "count": 1,
      "elements": [
        {
          "id": 2,
          "name": "小雁塔",
          "type": "historical_element"
        }
      ]
    }
  ]
}
```

### 获取区域分布统计

#### 接口信息

- **URL**: `/public/statistic/region-distribution`
- **方法**: `GET`
- **认证**: 无需认证

#### 请求示例

```bash
curl -X GET "http://localhost:7001/public/statistic/region-distribution"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "region": "关中地区",
      "regionId": 1,
      "mountainCount": 15,
      "waterSystemCount": 10,
      "historicalElementCount": 35,
      "total": 60
    },
    {
      "region": "陕北地区",
      "regionId": 2,
      "mountainCount": 10,
      "waterSystemCount": 5,
      "historicalElementCount": 15,
      "total": 30
    }
  ]
}
```

### 获取数据概览

#### 接口信息

- **URL**: `/public/statistic/overview`
- **方法**: `GET`
- **认证**: 无需认证

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID |

#### 请求示例

```bash
curl -X GET "http://localhost:7001/public/statistic/overview?regionId=1"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "totalCounts": {
      "mountain": 25,
      "waterSystem": 15,
      "historicalElement": 50
    },
    "regionStats": [
      {
        "region": "关中地区",
        "regionId": 1,
        "mountainCount": 15,
        "waterSystemCount": 10,
        "historicalElementCount": 35,
        "total": 60
      }
    ],
    "timelineData": [
      {
        "year": 652,
        "count": 1,
        "elements": [
          {
            "id": 1,
            "name": "大雁塔",
            "type": "historical_element"
          }
        ]
      }
    ]
  }
}
```

---

## 数据字段说明

### 地图数据字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| mountains | array | 山塬数据列表 |
| waterSystems | array | 水系数据列表 |
| historicalElements | array | 历史要素数据列表 |

### 统计数据字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| counts | object | 各类型数据数量统计 |
| regionStats | array | 区域分布统计 |
| timelineData | array | 时间轴数据 |
| total | object | 总计数据 |

### 时间轴数据字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| year | number | 年份 |
| count | number | 该年份的要素数量 |
| elements | array | 该年份的要素列表 |

---

## 使用场景

1. **地图可视化**: 使用地图数据接口在地图上展示各类地理要素
2. **数据统计**: 使用统计接口展示数据概览和分析报告
3. **时间轴展示**: 使用时间轴数据展示历史发展脉络
4. **区域分析**: 使用区域分布统计分析不同区域的数据分布
5. **综合报告**: 使用综合统计报告生成完整的数据分析报告

---

## 注意事项

1. **认证要求**: 公共接口无需认证，管理端接口需要管理员权限
2. **数据筛选**: 支持按区域、类型、时间范围等多维度筛选
3. **实时数据**: 数据来源于实时数据库，确保数据的准确性
4. **性能优化**: 大数据量查询时建议使用分页或筛选条件
5. **缓存机制**: 部分统计数据可能有缓存，更新可能有延迟
6. **坐标系统**: 地理坐标使用WGS84坐标系统
7. **时间格式**: 时间数据使用ISO 8601格式

---

# 管理端仪表盘 API

## 概述

管理端仪表盘API提供更详细的数据分析和管理功能，包括数据概览、详细统计、数据增长趋势和数据质量报告等接口，帮助管理员全面了解系统数据状态。所有管理端接口都需要管理员权限。

---

## 仪表盘概览接口

### 获取仪表盘概览数据

#### 接口信息

- **URL**: `/admin/dashboard/overview`
- **方法**: `GET`
- **认证**: 需要管理员权限

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID，筛选指定区域的数据 |

#### 请求示例

```bash
# 获取所有区域的概览数据
curl -X GET "http://localhost:7001/admin/dashboard/overview" \
  -H "Authorization: Bearer {token}"

# 获取指定区域的概览数据
curl -X GET "http://localhost:7001/admin/dashboard/overview?regionId=1" \
  -H "Authorization: Bearer {token}"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "statistics": {
      "mountain": 25,
      "waterSystem": 15,
      "historicalElement": 50
    },
    "regionDistribution": [
      {
        "region": "关中地区",
        "regionId": 1,
        "mountainCount": 15,
        "waterSystemCount": 10,
        "historicalElementCount": 35,
        "total": 60
      },
      {
        "region": "陕北地区",
        "regionId": 2,
        "mountainCount": 10,
        "waterSystemCount": 5,
        "historicalElementCount": 15,
        "total": 30
      }
    ],
    "recentData": {
      "mountains": [
        {
          "id": 1,
          "name": "华山",
          "code": "HS001",
          "createdAt": "2024-01-15T10:00:00.000Z"
        }
      ],
      "waterSystems": [
        {
          "id": 1,
          "name": "渭河",
          "code": "WH001",
          "createdAt": "2024-01-15T10:00:00.000Z"
        }
      ],
      "historicalElements": [
        {
          "id": 1,
          "name": "大雁塔",
          "code": "DYT001",
          "createdAt": "2024-01-15T10:00:00.000Z"
        }
      ]
    }
  }
}
```

---

## 详细统计接口

### 获取详细统计数据

#### 接口信息

- **URL**: `/admin/dashboard/statistics`
- **方法**: `GET`
- **认证**: 需要管理员权限

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID，筛选指定区域的数据 |
| startTime | string | 否 | 开始时间，ISO日期格式 |
| endTime | string | 否 | 结束时间，ISO日期格式 |

#### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/dashboard/statistics?regionId=1" \
  -H "Authorization: Bearer {token}"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "basic": {
      "counts": {
        "mountain": 25,
        "waterSystem": 15,
        "historicalElement": 50
      },
      "regionStats": [],
      "timelineData": []
    },
    "detailed": {
      "mountains": {
        "total": 25,
        "byRegion": [
          {
            "regionId": 1,
            "regionName": "关中地区",
            "count": 15
          }
        ]
      },
      "waterSystems": {
        "total": 15,
        "totalLength": 2500,
        "byRegion": [
          {
            "regionId": 1,
            "regionName": "关中地区",
            "count": 10
          }
        ]
      },
      "historicalElements": {
        "total": 50,
        "byType": [
          {
            "typeId": 1,
            "typeName": "佛塔",
            "count": 15
          }
        ],
        "byRegion": [
          {
            "regionId": 1,
            "regionName": "关中地区",
            "count": 35
          }
        ]
      }
    },
    "summary": {
      "totalEntities": 90,
      "regionCoverage": 3,
      "timeSpan": {
        "earliest": 652,
        "latest": 1644,
        "span": 992
      }
    }
  }
}
```

---

## 数据增长趋势接口

### 获取数据增长趋势

#### 接口信息

- **URL**: `/admin/dashboard/growth-trend`
- **方法**: `GET`
- **认证**: 需要管理员权限

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| period | string | 否 | 时间周期，可选值：week（最近7天）、month（最近30天）、year（最近12个月），默认为month |

#### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/dashboard/growth-trend?period=month" \
  -H "Authorization: Bearer {token}"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "mountains": [
      {
        "date": "2024-01-01",
        "count": 2
      },
      {
        "date": "2024-01-02",
        "count": 3
      }
    ],
    "waterSystems": [
      {
        "date": "2024-01-01",
        "count": 1
      },
      {
        "date": "2024-01-02",
        "count": 2
      }
    ],
    "historicalElements": [
      {
        "date": "2024-01-01",
        "count": 5
      },
      {
        "date": "2024-01-02",
        "count": 3
      }
    ]
  }
}
```

---

## 数据质量报告接口

### 获取数据质量报告

#### 接口信息

- **URL**: `/admin/dashboard/data-quality`
- **方法**: `GET`
- **认证**: 需要管理员权限

#### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/dashboard/data-quality" \
  -H "Authorization: Bearer {token}"
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "completeness": {
      "mountain": 95,
      "waterSystem": 90,
      "historicalElement": 85
    },
    "missingFields": {
      "mountain": [
        { "field": "height", "count": 5 },
        { "field": "historicalRecords", "count": 10 }
      ],
      "waterSystem": [
        { "field": "lengthArea", "count": 8 },
        { "field": "historicalRecords", "count": 12 }
      ],
      "historicalElement": [
        { "field": "constructionTime", "count": 15 },
        { "field": "description", "count": 20 }
      ]
    },
    "dataDistribution": {
      "byRegion": [
        { "region": "关中地区", "count": 120, "percentage": 40 },
        { "region": "陕北地区", "count": 90, "percentage": 30 },
        { "region": "陕南地区", "count": 90, "percentage": 30 }
      ],
      "byType": [
        { "type": "山塬", "count": 100, "percentage": 33.3 },
        { "type": "水系", "count": 80, "percentage": 26.7 },
        { "type": "历史要素", "count": 120, "percentage": 40 }
      ]
    }
  }
}
```

---

## 管理端数据字段说明

### 概览数据字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| statistics | object | 各类型数据数量统计 |
| regionDistribution | array | 区域分布统计 |
| recentData | object | 最近添加的数据 |

### 详细统计数据字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| basic | object | 基础统计数据 |
| detailed | object | 详细统计数据 |
| summary | object | 统计数据摘要 |

### 增长趋势数据字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| mountains | array | 山塬数据增长趋势 |
| waterSystems | array | 水系数据增长趋势 |
| historicalElements | array | 历史要素数据增长趋势 |

### 数据质量报告字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| completeness | object | 数据完整性百分比 |
| missingFields | object | 缺失字段统计 |
| dataDistribution | object | 数据分布情况 |
4. **性能优化**: 大数据量查询时建议使用分页或筛选条件
5. **缓存机制**: 部分统计数据可能有缓存，更新可能有延迟
6. **坐标系统**: 地理坐标使用WGS84坐标系统
7. **时间格式**: 时间数据使用ISO 8601格式
