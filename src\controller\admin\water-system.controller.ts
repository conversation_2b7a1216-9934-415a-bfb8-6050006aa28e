import {
  Controller,
  Post,
  Put,
  Del,
  Get,
  Body,
  Param,
  Query,
  Inject,
  Files,
} from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { UploadFileInfo, UploadMiddleware } from '@midwayjs/busboy';
import { JwtMiddleware } from '../../middleware/jwt.middleware';
import { AuthMiddleware } from '../../middleware/auth.middleware';
import { WaterSystemService } from '../../service/water-system.service';
import { ExcelService } from '../../service/excel.service';
import { PageQueryDTO, BatchDeleteDTO } from '../../dto/common.dto';
import {
  CreateWaterSystemDTO,
  UpdateWaterSystemDTO,
} from '../../dto/entity.dto';
import { promises as fs } from 'fs';
import { Context as KoaContext } from '@midwayjs/koa';

/**
 * 水系管理控制器
 */
@Controller('/admin/water-system', {
  middleware: [JwtMiddleware, AuthMiddleware],
})
export class AdminWaterSystemController {
  @Inject()
  waterSystemService: WaterSystemService;

  @Inject()
  excelService: ExcelService;

  @Inject()
  ctx: KoaContext;

  /**
   * 创建水系
   */
  @Post('/')
  @Validate()
  async create(@Body() createDto: CreateWaterSystemDTO) {
    const data = await this.waterSystemService.createWaterSystem(createDto);
    return data;
  }

  /**
   * 更新水系
   */
  @Put('/:id')
  @Validate()
  async update(
    @Param('id') id: number,
    @Body() updateDto: UpdateWaterSystemDTO
  ) {
    const data = await this.waterSystemService.updateWaterSystem(id, updateDto);
    return data;
  }

  /**
   * 删除水系
   */
  @Del('/:id')
  async delete(
    @Param('id') id: number,
    @Query('deletePhotos') deletePhotos?: string
  ) {
    // 将字符串参数转换为布尔值，默认为false（保留照片，外键设置为NULL）
    const shouldDeletePhotos = deletePhotos === 'true';
    await this.waterSystemService.deleteWaterSystem(id, shouldDeletePhotos);
    return {
      message: '删除成功',
      deletedPhotos: shouldDeletePhotos,
    };
  }

  /**
   * 批量删除水系
   */
  @Del('/batch-delete')
  @Validate()
  async batchDelete(@Body() batchDeleteDto: BatchDeleteDTO) {
    await this.waterSystemService.batchDeleteWaterSystems(
      batchDeleteDto.ids,
      batchDeleteDto.deletePhotos
    );
    return {
      message: '批量删除成功',
      deletedCount: batchDeleteDto.ids.length,
      deletedPhotos: batchDeleteDto.deletePhotos,
    };
  }

  /**
   * 获取水系列表
   */
  @Get('/')
  @Validate()
  async getList(@Query() query: PageQueryDTO & { regionId?: number }) {
    const data = await this.waterSystemService.findList(query);
    return data;
  }

  /**
   * 获取水系详情
   */
  @Get('/:id')
  async getDetail(@Param('id') id: number) {
    // 验证ID参数
    if (!id || isNaN(Number(id))) {
      throw new Error('无效的水系ID');
    }
    const data = await this.waterSystemService.findById(Number(id));
    if (!data) {
      throw new Error('水系不存在');
    }
    return data;
  }

  /**
   * 批量导入水系
   */
  @Post('/batch-import')
  @Validate()
  async batchImport(@Body() data: { waterSystems: CreateWaterSystemDTO[] }) {
    await this.waterSystemService.batchImportWaterSystems(data.waterSystems);
    return { message: '批量导入成功' };
  }

  /**
   * 获取水系统计
   */
  @Get('/statistics/overview')
  async getStatistics(@Query('regionId') regionId?: number) {
    const data = await this.waterSystemService.getStatistics(regionId);
    return data;
  }

  /**
   * 根据区域获取水系
   */
  @Get('/by-region/:regionId')
  async getByRegion(@Param('regionId') regionId: number) {
    // 验证regionId参数
    if (!regionId || isNaN(Number(regionId))) {
      throw new Error('无效的区域ID');
    }
    const data = await this.waterSystemService.findByRegion(Number(regionId));
    return data;
  }

  /**
   * 根据长度/面积查询
   */
  @Get('/by-length-area')
  async getByLengthArea(@Query('keyword') keyword?: string) {
    const data = await this.waterSystemService.findByLengthArea(keyword);
    return data;
  }

  /**
   * 导出水系数据到Excel
   */
  @Get('/export/excel')
  async exportToExcel(@Query('regionId') regionId?: number) {
    const whereConditions: any = {};
    if (regionId) {
      whereConditions.regionDictId = regionId;
    }

    const result = await this.waterSystemService.findAll({
      query: whereConditions,
    });

    const excelBuffer = await this.excelService.exportWaterSystemToExcel(
      result.list
    );

    this.ctx.set(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
    this.ctx.set(
      'Content-Disposition',
      'attachment; filename=water-systems.xlsx'
    );
    return excelBuffer;
  }

  /**
   * 获取导入模板
   */
  @Get('/template/download')
  async downloadTemplate() {
    console.log('🌊 获取水系导入模板');

    try {
      const buffer = await this.excelService.generateWaterSystemTemplate();
      return {
        downloadUrl: '/public/templates/water_system_import_template.xlsx',
        filename: '水系导入模板.xlsx',
        description: '点击链接下载Excel导入模板，包含字段说明和示例数据',
        buffer: buffer.toString('base64'),
      };
    } catch (error) {
      console.error('🌊 获取导入模板失败:', error);
      throw new Error(`获取模板失败: ${error.message}`);
    }
  }

  /**
   * 批量导入水系数据
   */
  @Post('/import/execute', { middleware: [UploadMiddleware] })
  @Validate()
  async executeImport(@Files() files: UploadFileInfo[]) {
    if (!files || files.length === 0) {
      throw new Error('请选择要导入的Excel文件');
    }

    const file = files[0];

    // 验证文件类型
    if (!this.isExcelFile(file.filename)) {
      throw new Error('请上传Excel文件（.xlsx或.xls格式）');
    }

    // 验证文件大小（限制为10MB）
    const fileSize = await this.getFileSize(file);
    if (fileSize > 10 * 1024 * 1024) {
      throw new Error('文件大小不能超过10MB');
    }

    try {
      // 解析Excel文件
      const parseResult = await this.excelService.parseWaterSystemExcel(
        file.data as string
      );

      if (!parseResult.success || !parseResult.data) {
        throw new Error('文件解析失败或数据为空');
      }

      // 批量导入数据
      await this.waterSystemService.batchImportWaterSystems(parseResult.data);

      return {
        success: true,
        message: '导入成功',
        totalRows: parseResult.totalRows,
        validRows: parseResult.validRows,
        importedCount: parseResult.data.length,
      };
    } catch (error) {
      throw new Error(`导入失败: ${error.message}`);
    } finally {
      // 清理临时文件
      try {
        if (typeof file.data === 'string') {
          await fs.unlink(file.data);
        }
      } catch (cleanupError) {
        console.warn('清理临时文件失败:', cleanupError);
      }
    }
  }

  /**
   * 验证Excel文件预览（不实际导入）
   */
  @Post('/import/preview', { middleware: [UploadMiddleware] })
  @Validate()
  async previewImport(@Files() files: UploadFileInfo[]) {
    if (!files || files.length === 0) {
      throw new Error('请选择要上传的Excel文件');
    }

    const file = files[0];

    // 验证文件类型
    if (!this.isExcelFile(file.filename)) {
      throw new Error('请上传Excel文件（.xlsx或.xls格式）');
    }

    try {
      // 解析Excel文件
      const parseResult = await this.excelService.parseWaterSystemExcel(
        file.data as string
      );

      return {
        success: parseResult.success,
        message: parseResult.success ? '文件格式正确' : '文件存在错误',
        errors: parseResult.errors,
        totalRows: parseResult.totalRows || 0,
        validRows: parseResult.validRows || 0,
        preview: parseResult.data?.slice(0, 5), // 只返回前5条数据作为预览
      };
    } catch (error) {
      return {
        success: false,
        message: `文件解析失败: ${error.message}`,
        totalRows: 0,
        validRows: 0,
      };
    } finally {
      // 清理临时文件
      try {
        if (typeof file.data === 'string') {
          await fs.unlink(file.data);
        }
      } catch (cleanupError) {
        console.warn('清理临时文件失败:', cleanupError);
      }
    }
  }

  /**
   * 验证是否为Excel文件
   */
  private isExcelFile(filename: string): boolean {
    const extension = filename.toLowerCase().split('.').pop();
    return extension === 'xlsx' || extension === 'xls';
  }

  /**
   * 获取文件大小
   */
  private async getFileSize(file: UploadFileInfo): Promise<number> {
    if (typeof file.data === 'string') {
      const stats = await fs.stat(file.data);
      return stats.size;
    }
    return 0;
  }
}
