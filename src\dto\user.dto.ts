import { Rule, RuleType } from '@midwayjs/validate';
import { PageQueryDTO } from './common.dto';

/**
 * 用户登录DTO
 */
export class LoginDTO {
  @Rule(RuleType.string().required().min(3).max(50))
  username: string;

  @Rule(RuleType.string().required().min(6).max(50))
  password: string;
}

/**
 * 用户创建DTO
 */
export class CreateUserDTO {
  @Rule(RuleType.string().required().min(3).max(50))
  username: string;

  @Rule(RuleType.string().required().min(6).max(50))
  password: string;

  @Rule(RuleType.string().optional().allow(null).max(100))
  nickname?: string;

  @Rule(RuleType.string().optional().allow(null).email().max(255))
  email?: string;

  @Rule(RuleType.string().optional().allow(null).max(255))
  avatar?: string;

  @Rule(RuleType.string().valid('admin', 'user').default('user'))
  role?: string = 'user';

  @Rule(RuleType.boolean().default(true))
  isActive?: boolean = true;
}

/**
 * 用户更新DTO
 */
export class UpdateUserDTO {
  @Rule(RuleType.string().optional().allow(null).min(3).max(50))
  username?: string;

  @Rule(RuleType.string().optional().allow(null).min(6).max(50))
  password?: string;

  @Rule(RuleType.string().optional().allow(null).max(100))
  nickname?: string;

  @Rule(RuleType.string().optional().allow(null).email().max(255))
  email?: string;

  @Rule(RuleType.string().optional().allow(null).max(255))
  avatar?: string;

  @Rule(RuleType.string().valid('admin', 'user').optional().allow(null))
  role?: string;

  @Rule(RuleType.boolean().optional().allow(null))
  isActive?: boolean;
}

/**
 * 用户响应DTO
 */
export class UserResponseDTO {
  id: number;
  username: string;
  nickname?: string;
  email?: string;
  avatar?: string;
  role: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;

  constructor(user: any) {
    this.id = user.id;
    this.username = user.username;
    this.nickname = user.nickname;
    this.email = user.email;
    this.avatar = user.avatar;
    this.role = user.role;
    this.isActive = user.isActive;
    this.createdAt = user.createdAt;
    this.updatedAt = user.updatedAt;
  }
}

/**
 * 用户查询DTO
 */
export class UserQueryDTO extends PageQueryDTO {
  @Rule(RuleType.string().optional().allow('').allow(null))
  keyword?: string;

  @Rule(RuleType.string().valid('admin', 'user').optional().allow(null))
  role?: string;

  @Rule(RuleType.boolean().optional().allow(null))
  isActive?: boolean;
}

/**
 * 重置密码DTO
 */
export class ResetPasswordDTO {
  @Rule(RuleType.string().required().min(6).max(50))
  newPassword: string;
}

/**
 * 修改密码DTO
 */
export class ChangePasswordDTO {
  @Rule(RuleType.string().required().min(6).max(50))
  oldPassword: string;

  @Rule(RuleType.string().required().min(6).max(50))
  newPassword: string;
}

/**
 * 登录响应DTO
 */
export class LoginResponseDTO {
  token: string;
  user: UserResponseDTO;

  constructor(token: string, user: any) {
    this.token = token;
    this.user = new UserResponseDTO(user);
  }
}
