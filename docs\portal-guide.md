# 智慧营建系统门户使用说明

## 系统概述

智慧营建系统是一个专门用于管理和展示关中地区地理文化数据的综合性平台。系统集成了山塬、水系、历史要素等多种地理文化信息，为用户提供直观的数据查询、地图展示和统计分析功能。

## 系统特色

### 🗺️ 地理信息可视化
- **地图展示**: 基于地理坐标的交互式地图展示
- **多层数据**: 支持山塬、水系、历史要素的分层显示
- **空间分析**: 提供区域分布和空间关系分析

### 📊 数据统计分析
- **实时统计**: 各类地理要素的数量统计
- **时间轴分析**: 历史要素按年代的分布情况
- **区域分析**: 按行政区域的数据分布统计

### 🔍 智能搜索功能
- **关键词搜索**: 支持名称、编号的模糊搜索
- **分类筛选**: 按类型、区域、古城等维度筛选
- **高级搜索**: 支持多条件组合查询

### 📱 多端适配
- **响应式设计**: 支持PC、平板、手机等多种设备
- **API接口**: 提供标准REST API供第三方系统集成

## 主要功能模块

### 1. 地图浏览
**功能描述**: 在交互式地图上查看各类地理文化要素的分布情况

**使用方法**:
- 访问地图页面，系统自动加载所有地理要素
- 使用图层控制器选择要显示的要素类型
- 点击地图标记查看详细信息
- 使用缩放和平移功能浏览不同区域

**API接口**:
```
GET /openapi/map/cultural-elements
GET /openapi/map/data (兼容旧版本)
```

### 2. 数据查询
**功能描述**: 通过列表形式查询和浏览各类地理文化要素

**查询类型**:
- **山塬查询**: 查看山塬的高度、位置等信息
- **水系查询**: 查看河流、湖泊的长度、面积等信息
- **历史要素查询**: 查看古建筑、遗址的历史信息

**使用方法**:
- 选择要查询的数据类型
- 设置筛选条件（区域、类型等）
- 浏览查询结果列表
- 点击条目查看详细信息

**API接口**:
```
GET /openapi/mountain/list
GET /openapi/water-system/list
GET /openapi/historical-element/list
```

### 3. 详情查看
**功能描述**: 查看单个地理文化要素的详细信息

**包含信息**:
- 基本信息（名称、编号、位置等）
- 历史文献记载
- 相关照片
- 关联关系

**使用方法**:
- 从列表或地图点击进入详情页
- 浏览基本信息和历史记载
- 查看相关照片和文档
- 了解与其他要素的关联关系

**API接口**:
```
GET /openapi/mountain/{id}
GET /openapi/water-system/{id}
GET /openapi/historical-element/{id}
```

### 4. 统计分析
**功能描述**: 查看系统数据的统计分析结果

**统计维度**:
- **数量统计**: 各类要素的总数统计
- **区域分布**: 按行政区域的分布情况
- **时间分析**: 历史要素按建造年代的分布
- **关系分析**: 要素间关联关系的统计

**使用方法**:
- 访问统计页面查看总体数据
- 选择时间范围进行时间轴分析
- 选择区域查看区域分布
- 查看图表和可视化结果

**API接口**:
```
GET /openapi/statistic
GET /openapi/relationship/statistics
```

### 5. 字典查询
**功能描述**: 查询系统中的字典数据，如区域、类型等分类信息

**字典类型**:
- **区域字典**: 行政区域划分信息
- **类型字典**: 各类要素的分类信息
- **关系字典**: 要素间关系类型定义

**使用方法**:
- 查询区域列表了解行政区域划分
- 查询类型列表了解要素分类
- 查询关系类型了解关联关系定义

**API接口**:
```
GET /openapi/region-dict/all
GET /openapi/type-dict/all
GET /openapi/relationship-dict/all
```

## 数据说明

### 山塬数据
- **定义**: 关中地区的山地和塬地地形
- **属性**: 名称、编号、经纬度、高度、历史记载
- **用途**: 地形分析、历史地理研究

### 水系数据
- **定义**: 关中地区的河流、湖泊等水体
- **属性**: 名称、编号、经纬度、长度/面积、历史记载
- **用途**: 水文分析、历史水利研究

### 历史要素数据
- **定义**: 关中地区的历史建筑、遗址等文化要素
- **属性**: 名称、编号、位置、建造年代、历史记载
- **用途**: 文化遗产保护、历史研究

### 关联关系
- **定义**: 各类地理文化要素之间的关联关系
- **类型**: 空间关系、历史关系、功能关系等
- **用途**: 关系分析、综合研究

## 使用建议

### 1. 初次使用
- 建议先浏览地图了解数据分布概况
- 查看统计页面了解数据规模
- 熟悉各类要素的基本信息

### 2. 研究使用
- 利用搜索功能快速定位目标要素
- 通过关联关系发现要素间的联系
- 结合时间轴分析历史演变过程

### 3. 数据集成
- 使用API接口获取结构化数据
- 参考API文档了解数据格式
- 注意接口的访问频率限制

## 技术支持

### 系统要求
- **浏览器**: Chrome 70+, Firefox 65+, Safari 12+, Edge 79+
- **网络**: 建议使用稳定的网络连接
- **分辨率**: 最低支持1024x768分辨率

### 常见问题
1. **地图加载缓慢**: 检查网络连接，清除浏览器缓存
2. **搜索无结果**: 检查搜索关键词，尝试使用更通用的词汇
3. **详情页面空白**: 刷新页面或检查数据是否存在

### 联系方式
- **技术支持**: 请联系系统管理员
- **数据问题**: 请联系数据管理部门
- **功能建议**: 欢迎提交功能改进建议

## 更新日志

### v1.0.0 (2025-10-09)
- 系统正式发布
- 支持山塬、水系、历史要素数据管理
- 提供地图展示和统计分析功能
- 实现多端适配和API接口

---

**文档版本**: v1.0.0  
**更新时间**: 2025-10-09  
**适用系统**: 智慧营建系统 v1.0.0
