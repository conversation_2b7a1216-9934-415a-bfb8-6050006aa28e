import { Provide } from '@midwayjs/core';
import { Op } from 'sequelize';
import { Relationship } from '../entity/relationship.entity';
import { CulturalElement } from '../entity/cultural-element.entity';
import { AncientCityDict } from '../entity/ancient-city-dict.entity';
import { RelationshipDict } from '../entity/relationship-dict.entity';
import { PageResponseDTO } from '../dto/common.dto';
import {
  CreateRelationshipDTO,
  UpdateRelationshipDTO,
  RelationshipQueryDTO,
  RelationshipResponseDTO,
} from '../dto/relationship.dto';

@Provide()
export class RelationshipService {
  /**
   * 创建关系
   */
  async createRelationship(
    createDto: CreateRelationshipDTO
  ): Promise<RelationshipResponseDTO> {
    // 验证源要素是否存在
    await this.validateEntity(
      createDto.sourceEntityType,
      createDto.sourceEntityId
    );

    // 验证目标要素是否存在
    await this.validateEntity(
      createDto.targetEntityType,
      createDto.targetEntityId
    );

    // 验证关系字典是否存在
    if (createDto.relationDictId) {
      const relationDict = await RelationshipDict.findByPk(
        createDto.relationDictId
      );
      if (!relationDict) {
        throw new Error('关系类型不存在');
      }
    }

    // 创建关系
    const relationship = await Relationship.create({
      relationDictId: createDto.relationDictId,
      parentRelationshipId: createDto.parentRelationshipId,
      sourceEntityType: createDto.sourceEntityType,
      sourceEntityId: createDto.sourceEntityId,
      targetEntityType: createDto.targetEntityType,
      targetEntityId: createDto.targetEntityId,
      direction: createDto.direction,
      description: createDto.description,
      historicalRecord: createDto.historicalRecord,
      sort: createDto.sort || 0,
      status: 1,
    });

    return this.buildRelationshipResponse(relationship);
  }

  /**
   * 更新关系
   */
  async updateRelationship(
    id: number,
    updateDto: UpdateRelationshipDTO
  ): Promise<RelationshipResponseDTO> {
    const relationship = await Relationship.findByPk(id);
    if (!relationship) {
      throw new Error('关系不存在');
    }

    // 验证更新的数据
    if (updateDto.sourceEntityType && updateDto.sourceEntityId) {
      await this.validateEntity(
        updateDto.sourceEntityType,
        updateDto.sourceEntityId
      );
    }

    if (updateDto.targetEntityType && updateDto.targetEntityId) {
      await this.validateEntity(
        updateDto.targetEntityType,
        updateDto.targetEntityId
      );
    }

    if (updateDto.relationDictId) {
      const relationDict = await RelationshipDict.findByPk(
        updateDto.relationDictId
      );
      if (!relationDict) {
        throw new Error('关系类型不存在');
      }
    }

    // 更新关系
    await relationship.update(updateDto);

    return this.buildRelationshipResponse(relationship);
  }

  /**
   * 删除关系
   */
  async deleteRelationship(id: number): Promise<void> {
    const relationship = await Relationship.findByPk(id);
    if (!relationship) {
      throw new Error('关系不存在');
    }

    await relationship.update({ status: 0 });
  }

  /**
   * 获取关系详情
   */
  async getRelationshipById(id: number): Promise<RelationshipResponseDTO> {
    const relationship = await Relationship.findByPk(id, {
      include: [
        {
          model: RelationshipDict,
          as: 'relationDict',
          attributes: ['id', 'relationName', 'relationCode'],
        },
      ],
    });

    if (!relationship) {
      throw new Error('关系不存在');
    }

    return this.buildRelationshipResponse(relationship);
  }

  /**
   * 分页查询关系
   */
  async getRelationships(
    queryDto: RelationshipQueryDTO
  ): Promise<PageResponseDTO<RelationshipResponseDTO>> {
    return this.getRelationshipPage(queryDto);
  }

  /**
   * 分页查询关系（别名方法，保持兼容性）
   */
  async getRelationshipPage(
    queryDto: RelationshipQueryDTO
  ): Promise<PageResponseDTO<RelationshipResponseDTO>> {
    const { page = 1, pageSize = 10, keyword, ...filters } = queryDto;
    const offset = (page - 1) * pageSize;

    // 构建查询条件
    const whereCondition: any = { status: 1 };

    if (keyword) {
      whereCondition[Op.or] = [
        { direction: { [Op.like]: `%${keyword}%` } },
        { description: { [Op.like]: `%${keyword}%` } },
        { historicalRecord: { [Op.like]: `%${keyword}%` } },
      ];
    }

    // 添加其他过滤条件
    Object.keys(filters).forEach(key => {
      if (filters[key] !== undefined && filters[key] !== null) {
        whereCondition[key] = filters[key];
      }
    });

    const { rows, count } = await Relationship.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: RelationshipDict,
          as: 'relationDict',
          attributes: ['id', 'relationName', 'relationCode'],
        },
      ],
      offset,
      limit: pageSize,
      order: [
        ['sort', 'ASC'],
        ['createdAt', 'DESC'],
      ],
    });

    const list = await Promise.all(
      rows.map(row => this.buildRelationshipResponse(row))
    );

    return {
      list,
      total: count,
      page,
      pageSize,
      totalPages: Math.ceil(count / pageSize),
    };
  }

  /**
   * 验证实体是否存在
   */
  private async validateEntity(
    entityType: string,
    entityId: number
  ): Promise<void> {
    let entity;

    switch (entityType) {
      case 'ancient_city':
        entity = await AncientCityDict.findByPk(entityId);
        break;
      case 'cultural_element':
        entity = await CulturalElement.findByPk(entityId);
        break;
      default:
        throw new Error(`不支持的实体类型: ${entityType}`);
    }

    if (!entity) {
      throw new Error(`${entityType} 实体不存在: ${entityId}`);
    }
  }

  /**
   * 构建关系响应数据
   */
  private async buildRelationshipResponse(
    relationship: Relationship
  ): Promise<RelationshipResponseDTO> {
    const result = new RelationshipResponseDTO(relationship.toJSON());

    // 获取源实体信息
    result.sourceElement = await this.getEntityInfo(
      relationship.sourceEntityType,
      relationship.sourceEntityId
    );

    // 获取目标实体信息
    result.targetElement = await this.getEntityInfo(
      relationship.targetEntityType,
      relationship.targetEntityId
    );

    return result;
  }

  /**
   * 获取实体信息
   */
  private async getEntityInfo(
    entityType: string,
    entityId: number
  ): Promise<any> {
    switch (entityType) {
      case 'ancient_city':
        return await AncientCityDict.findByPk(entityId, {
          attributes: ['id', 'cityName', 'cityCode'],
        });
      case 'cultural_element':
        return await CulturalElement.findByPk(entityId, {
          attributes: ['id', 'name', 'code'],
        });
      default:
        return null;
    }
  }

  /**
   * 获取关系列表（不分页）
   */
  async getRelationshipList(filters?: any): Promise<RelationshipResponseDTO[]> {
    const whereCondition = { status: 1, ...filters };

    const relationships = await Relationship.findAll({
      where: whereCondition,
      include: [
        {
          model: RelationshipDict,
          as: 'relationDict',
          attributes: ['id', 'relationName', 'relationCode'],
        },
      ],
      order: [
        ['sort', 'ASC'],
        ['createdAt', 'DESC'],
      ],
    });

    return await Promise.all(
      relationships.map(relationship =>
        this.buildRelationshipResponse(relationship)
      )
    );
  }

  /**
   * 根据要素获取关联关系
   */
  async getRelationsByElement(
    elementType: string,
    elementId: number
  ): Promise<RelationshipResponseDTO[]> {
    // 转换为新的实体类型
    let dbElementType: string;
    switch (elementType) {
      case 'mountain':
      case 'water_system':
      case 'historical_element':
        dbElementType = 'cultural_element';
        break;
      case 'ancient_city':
        dbElementType = 'ancient_city';
        break;
      default:
        dbElementType = elementType;
    }

    const relationships = await Relationship.findAll({
      where: {
        [Op.or]: [
          { sourceEntityType: dbElementType, sourceEntityId: elementId },
          { targetEntityType: dbElementType, targetEntityId: elementId },
        ],
        status: 1,
      },
      include: [
        {
          model: RelationshipDict,
          as: 'relationDict',
          attributes: ['id', 'relationName', 'relationCode'],
        },
      ],
      order: [
        ['sort', 'ASC'],
        ['createdAt', 'DESC'],
      ],
    });

    return await Promise.all(
      relationships.map(relationship =>
        this.buildRelationshipResponse(relationship)
      )
    );
  }

  /**
   * 批量创建关系
   */
  async batchCreateRelationships(
    relationships: CreateRelationshipDTO[]
  ): Promise<RelationshipResponseDTO[]> {
    const results: RelationshipResponseDTO[] = [];

    for (const relationshipDto of relationships) {
      try {
        const result = await this.createRelationship(relationshipDto);
        results.push(result);
      } catch (error) {
        console.error('批量创建关系失败:', error);
        throw error;
      }
    }

    return results;
  }

  /**
   * 批量更新状态
   */
  async batchUpdateStatus(ids: number[], status: number): Promise<void> {
    await Relationship.update(
      { status },
      {
        where: {
          id: { [Op.in]: ids },
        },
      }
    );
  }

  /**
   * 获取关系统计
   */
  async getRelationshipStatistics(filters?: any): Promise<any> {
    const whereCondition = { status: 1, ...filters };

    const total = await Relationship.count({ where: whereCondition });

    // 按源实体类型统计
    const bySourceEntityType = await Relationship.findAll({
      where: whereCondition,
      attributes: [
        'sourceEntityType',
        [Relationship.sequelize.fn('COUNT', '*'), 'count'],
      ],
      group: ['sourceEntityType'],
      raw: true,
    });

    // 按目标实体类型统计
    const byTargetEntityType = await Relationship.findAll({
      where: whereCondition,
      attributes: [
        'targetEntityType',
        [Relationship.sequelize.fn('COUNT', '*'), 'count'],
      ],
      group: ['targetEntityType'],
      raw: true,
    });

    // 按关系类型统计
    const byRelationType = await Relationship.findAll({
      where: whereCondition,
      attributes: [
        'relationDictId',
        [Relationship.sequelize.fn('COUNT', '*'), 'count'],
      ],
      include: [
        {
          model: RelationshipDict,
          as: 'relationDict',
          attributes: ['relationName'],
        },
      ],
      group: ['relationDictId', 'relationDict.id'],
      raw: true,
    });

    return {
      total,
      bySourceEntityType: bySourceEntityType.map((item: any) => ({
        sourceEntityType: item.sourceEntityType,
        count: parseInt(item.count),
      })),
      byTargetEntityType: byTargetEntityType.map((item: any) => ({
        targetEntityType: item.targetEntityType,
        count: parseInt(item.count),
      })),
      byRelationType: byRelationType.map((item: any) => ({
        relationId: item.relationDictId,
        relationName: item['relationDict.relationName'],
        count: parseInt(item.count),
      })),
    };
  }

  /**
   * 获取网络图数据
   */
  async getNetworkGraphData(filters?: any): Promise<any> {
    // 暂时返回空的网络图数据
    return {
      nodes: [],
      links: [],
      categories: ['ancient_city', 'cultural_element'],
    };
  }
}
