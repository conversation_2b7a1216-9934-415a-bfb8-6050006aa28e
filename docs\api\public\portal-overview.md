# 门户概览接口详细说明

## 接口概述
门户概览接口提供门户首页所需的综合数据，包括统计信息、最新数据和概览信息。

## 1. 门户概览数据

### 接口地址
`GET /openapi/portal/overview`

### 功能说明
获取门户首页的综合概览数据，包括：
- 各类数据的统计数量
- 最新添加的山塬、水系、历史要素
- 区域分布统计
- 时间轴数据

### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| regionId | number | 否 | - | 区域ID，用于筛选特定区域的数据 |

### 响应数据结构
```json
{
  "errCode": 0,
  "data": {
    "statistics": {
      "mountain": 150,
      "waterSystem": 80,
      "historicalElement": 200,
      "user": 50,
      "typeDict": 25,
      "regionDict": 12,
      "relationshipDict": 30
    },
    "regionStats": [
      {
        "region": "西安市",
        "regionId": 1,
        "mountainCount": 50,
        "waterSystemCount": 30,
        "historicalElementCount": 80,
        "total": 160
      }
    ],
    "timelineData": [
      {
        "year": 618,
        "elements": [
          {
            "id": 1,
            "name": "大雁塔",
            "type": "historicalElement"
          }
        ]
      }
    ],
    "recentMountains": [
      {
        "id": 1,
        "name": "华山",
        "code": "HS001",
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "recentWaterSystems": [
      {
        "id": 1,
        "name": "渭河",
        "code": "WH001",
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "recentHistoricalElements": [
      {
        "id": 1,
        "name": "大雁塔",
        "code": "DYT001",
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ]
  },
  "msg": "OK"
}
```

### 字段说明

#### statistics 统计数据
| 字段名 | 类型 | 说明 |
|--------|------|------|
| mountain | number | 山塬总数 |
| waterSystem | number | 水系总数 |
| historicalElement | number | 历史要素总数 |
| user | number | 用户总数 |
| typeDict | number | 类型字典总数 |
| regionDict | number | 区域字典总数 |
| relationshipDict | number | 关系字典总数 |

#### regionStats 区域分布统计
| 字段名 | 类型 | 说明 |
|--------|------|------|
| region | string | 区域名称 |
| regionId | number | 区域ID |
| mountainCount | number | 该区域山塬数量 |
| waterSystemCount | number | 该区域水系数量 |
| historicalElementCount | number | 该区域历史要素数量 |
| total | number | 该区域总数量 |

#### timelineData 时间轴数据
| 字段名 | 类型 | 说明 |
|--------|------|------|
| year | number | 年份 |
| elements | array | 该年份的历史要素列表 |

#### recent* 最新数据
每个最新数据项包含：
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 实体ID |
| name | string | 实体名称 |
| code | string | 实体编码 |
| createdAt | string | 创建时间（ISO格式） |

### 使用示例

#### 获取全部概览数据
```javascript
fetch('/openapi/portal/overview')
  .then(response => response.json())
  .then(data => {
    if (data.errCode === 0) {
      const { statistics, regionStats, timelineData, recentMountains } = data.data;
      
      // 显示统计数据
      console.log('山塬总数:', statistics.mountain);
      console.log('水系总数:', statistics.waterSystem);
      console.log('历史要素总数:', statistics.historicalElement);
      
      // 显示区域分布
      regionStats.forEach(region => {
        console.log(`${region.region}: ${region.total}个实体`);
      });
      
      // 显示最新山塬
      recentMountains.forEach(mountain => {
        console.log(`最新山塬: ${mountain.name} (${mountain.code})`);
      });
    }
  })
  .catch(error => {
    console.error('获取概览数据失败:', error);
  });
```

#### 获取特定区域概览数据
```javascript
const regionId = 1; // 西安市
fetch(`/openapi/portal/overview?regionId=${regionId}`)
  .then(response => response.json())
  .then(data => {
    if (data.errCode === 0) {
      console.log('西安市概览数据:', data.data);
    }
  });
```

### 注意事项

1. **数据实时性**：概览数据每次请求都会实时计算，确保数据的准确性。

2. **区域筛选**：当提供regionId参数时，所有统计数据和最新数据都会按该区域进行筛选。

3. **最新数据数量**：每个类型的最新数据默认返回5条，按创建时间倒序排列。

4. **时间轴数据**：时间轴数据主要基于历史要素的建造时间生成，按年份分组。

5. **性能考虑**：由于需要统计大量数据，建议在前端适当缓存概览数据，避免频繁请求。
