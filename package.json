{"name": "zhi-hui-ying-jian-service", "version": "1.0.0", "description": "", "private": true, "dependencies": {"@midwayjs/bootstrap": "^3.12.0", "@midwayjs/busboy": "^3.20.13", "@midwayjs/core": "^3.12.0", "@midwayjs/info": "^3.12.0", "@midwayjs/jwt": "^3.20.12", "@midwayjs/koa": "^3.12.0", "@midwayjs/logger": "^3.1.0", "@midwayjs/redis": "^3.20.11", "@midwayjs/sequelize": "^3.20.12", "@midwayjs/static-file": "^3.20.12", "@midwayjs/upload": "^3.20.12", "@midwayjs/validate": "^3.12.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "exceljs": "^4.4.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.3", "redis": "^4.7.1", "sequelize": "^6.37.7", "sequelize-typescript": "^2.1.6", "uuid": "^11.1.0", "xlsx": "^0.18.5", "xlsx-style": "^0.8.13"}, "devDependencies": {"@midwayjs/mock": "^3.12.0", "@types/jest": "^29.2.0", "@types/node": "14", "cross-env": "^6.0.0", "jest": "^29.2.2", "mwts": "^1.3.0", "mwtsc": "^1.4.0", "ts-jest": "^29.0.3", "typescript": "~4.8.0"}, "engines": {"node": ">=12.0.0"}, "scripts": {"start": "NODE_ENV=production node ./bootstrap.js", "dev": "cross-env NODE_ENV=local mwtsc --watch --run @midwayjs/mock/app.js", "test": "cross-env NODE_ENV=unittest jest", "cov": "jest --coverage", "lint": "mwts check", "lint:fix": "mwts fix", "ci": "npm run cov", "build": "mwtsc --cleanOutDir"}, "repository": {"type": "git", "url": ""}, "author": "anonymous", "license": "MIT"}