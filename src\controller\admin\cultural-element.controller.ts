import {
  Controller,
  Post,
  Get,
  Put,
  Del,
  Body,
  Param,
  Query,
  Inject,
  Files,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Validate } from '@midwayjs/validate';
import { CulturalElementService } from '../../service/cultural-element.service';
import { UploadService } from '../../service/upload.service';
import {
  CreateCulturalElementDTO,
  UpdateCulturalElementDTO,
  CulturalElementQueryDTO,
  BatchCreateCulturalElementDTO,
} from '../../dto/cultural-element.dto';
// import { PageQueryDTO } from '../../dto/common.dto';
import { ApiResponse } from '../../common/api-response';

@Controller('/admin/cultural-element')
export class AdminCulturalElementController {
  @Inject()
  ctx: Context;

  @Inject()
  culturalElementService: CulturalElementService;

  @Inject()
  uploadService: UploadService;

  /**
   * 创建文化要素
   */
  @Post('/')
  @Validate()
  async create(@Body() dto: CreateCulturalElementDTO) {
    try {
      const result = await this.culturalElementService.createCulturalElement(
        dto
      );
      return ApiResponse.success(result, '创建成功');
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 更新文化要素
   */
  @Put('/:id')
  @Validate()
  async update(@Param('id') id: number, @Body() dto: UpdateCulturalElementDTO) {
    try {
      const result = await this.culturalElementService.updateCulturalElement(
        id,
        dto
      );
      return ApiResponse.success(result, '更新成功');
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 删除文化要素
   */
  @Del('/:id')
  async delete(@Param('id') id: number) {
    try {
      await this.culturalElementService.deleteCulturalElement(id);
      return ApiResponse.success(null, '删除成功');
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 根据ID获取文化要素详情
   */
  @Get('/:id')
  async getById(@Param('id') id: number) {
    try {
      const result = await this.culturalElementService.findCulturalElementById(
        id
      );
      if (!result) {
        return ApiResponse.error('文化要素不存在');
      }
      return ApiResponse.success(result);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 分页查询文化要素
   */
  @Get('/')
  @Validate()
  async getByPage(@Query() query: CulturalElementQueryDTO) {
    try {
      const result = await this.culturalElementService.findByPage(query);
      return ApiResponse.success(result);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 按类型查询文化要素
   */
  @Get('/type/:typeDictId')
  async getByType(@Param('typeDictId') typeDictId: number) {
    try {
      const result = await this.culturalElementService.findByType(typeDictId);
      return ApiResponse.success(result);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 按区域查询文化要素
   */
  @Get('/region/:regionDictId')
  async getByRegion(@Param('regionDictId') regionDictId: number) {
    try {
      const result = await this.culturalElementService.findByRegion(
        regionDictId
      );
      return ApiResponse.success(result);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 按古城查询文化要素
   */
  @Get('/ancient-city/:ancientCityId')
  async getByAncientCity(@Param('ancientCityId') ancientCityId: number) {
    try {
      const result = await this.culturalElementService.findByAncientCity(
        ancientCityId
      );
      return ApiResponse.success(result);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 获取统计信息
   */
  @Get('/statistics')
  async getStatistics() {
    try {
      const result = await this.culturalElementService.getStatistics();
      return ApiResponse.success(result);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 批量创建文化要素
   */
  @Post('/batch')
  @Validate()
  async batchCreate(@Body() dto: BatchCreateCulturalElementDTO) {
    try {
      const result =
        await this.culturalElementService.batchCreateCulturalElements(
          dto.elements
        );
      return ApiResponse.success(result, `成功创建${result.length}个文化要素`);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 导出文化要素数据到Excel
   */
  @Post('/export')
  @Validate()
  async exportToExcel(@Body() query: CulturalElementQueryDTO) {
    try {
      // 获取所有符合条件的数据（不分页）
      const allQuery = { ...query, page: 1, pageSize: 10000 };
      const result = await this.culturalElementService.findByPage(allQuery);

      return ApiResponse.success({
        message: 'Excel导出功能正在开发中',
        count: result.total,
      });
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 下载导入模板
   */
  @Get('/template')
  async downloadTemplate() {
    try {
      return ApiResponse.success({
        message: 'Excel模板下载功能正在开发中',
      });
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 预览导入数据
   */
  @Post('/import/preview')
  async previewImport(@Files() files) {
    try {
      if (!files || !files.length) {
        return ApiResponse.error('请选择要上传的文件');
      }

      const file = files[0];
      if (!file.filename.endsWith('.xlsx') && !file.filename.endsWith('.xls')) {
        return ApiResponse.error('请上传Excel文件');
      }

      return ApiResponse.success({
        message: 'Excel导入预览功能正在开发中',
        filename: file.filename,
      });
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 执行导入
   */
  @Post('/import/execute')
  async executeImport(@Files() files) {
    try {
      if (!files || !files.length) {
        return ApiResponse.error('请选择要上传的文件');
      }

      const file = files[0];
      if (!file.filename.endsWith('.xlsx') && !file.filename.endsWith('.xls')) {
        return ApiResponse.error('请上传Excel文件');
      }

      return ApiResponse.success({
        message: 'Excel导入执行功能正在开发中',
        filename: file.filename,
      });
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }
}
