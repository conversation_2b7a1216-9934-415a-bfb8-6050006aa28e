import { Provide, Scope, Scope<PERSON>num, Config, Inject } from '@midwayjs/core';
import { RegionDict } from '../entity/region-dict.entity';
import { TypeDict } from '../entity/type-dict.entity';
import { RelationshipDict } from '../entity/relationship-dict.entity';
import { AncientCityDict } from '../entity/ancient-city-dict.entity';
import { User } from '../entity/user.entity';
import { HistoricalElement } from '../entity/historical-element.entity';
import { Mountain } from '../entity/mountain.entity';
import { WaterSystem } from '../entity/water-system.entity';
import { promises as fs } from 'fs';
import { join } from 'path';
import { ExcelService } from './excel.service';
import {
  INITIAL_REGION_DATA,
  INITIAL_ANCIENT_CITY_DATA,
  INITIAL_TYPE_DATA,
  INITIAL_RELATIONSHIP_DATA,
  INITIAL_USER_DATA,
  INIT_CONFIG,
} from '../data/initial-data';

@Scope(ScopeEnum.Request, { allowDowngrade: true })
@Provide()
export class InitService {
  @Config('upload')
  uploadConfig: any;

  @Inject()
  excelService: ExcelService;
  /**
   * 初始化数据库数据
   */
  async initializeData() {
    console.log('📋 开始检查并初始化数据库数据...');

    // 分别检查并初始化各个核心数据表
    await this.checkAndInitRegionDict();
    await this.checkAndInitAncientCityDict();
    await this.checkAndInitTypeDict();
    await this.checkAndInitRelationshipDict();
    await this.checkAndInitDefaultUser();

    console.log('📋 数据库数据检查和初始化完成');
  }

  /**
   * 初始化Excel模板文件
   */
  async initializeExcelTemplates() {
    console.log('📄 开始初始化Excel模板文件...');

    try {
      // 使用新的模板服务
      const { ExcelTemplateService } = await import('./excel-template.service');
      const templateService = new ExcelTemplateService();

      // 生成所有模板
      const results = await templateService.generateAllTemplates();

      console.log('📄 Excel模板文件初始化完成');
      console.log('📄 生成的模板:', Object.keys(results).length, '个');

      // 显示统计信息
      const stats = await templateService.getTemplateStats();
      console.log('📄 模板统计:', {
        总数: stats.totalTemplates,
        缓存大小: stats.cacheStatus.totalSize,
        磁盘使用: Object.values(stats.diskUsage).reduce((a, b) => a + b, 0),
      });
    } catch (error) {
      console.error('❌ Excel模板文件初始化失败:', error.message);
      console.error('❌ 错误堆栈:', error.stack);
      throw error;
    }
  }

  /**
   * 初始化上传目录
   */
  async initializeUploadDirectories() {
    console.log('📁 开始检查并初始化上传目录...');

    try {
      // 获取上传路径配置
      const baseUploadDir = this.uploadConfig.useProjectPath
        ? join(process.cwd(), 'public', 'uploads')
        : this.uploadConfig.uploadPath;

      // 确保基础上传目录存在
      await this.ensureDirectoryExists(baseUploadDir);

      console.log('📁 上传目录初始化完成');
    } catch (error) {
      console.error('❌ 上传目录初始化失败:', error.message);
      throw error;
    }
  }

  /**
   * 确保目录存在，如果不存在则创建
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      // 检查目录是否存在
      await fs.access(dirPath);
      console.log(`✅ 目录已存在: ${dirPath}`);
    } catch (error) {
      // 目录不存在，创建目录
      try {
        await fs.mkdir(dirPath, { recursive: true });
        console.log(`✅ 创建目录成功: ${dirPath}`);
      } catch (createError) {
        console.error(`❌ 创建目录失败: ${dirPath}`, createError);
        throw new Error(`无法创建上传目录: ${dirPath}`);
      }
    }
  }

  /**
   * 检查并初始化区域字典
   */
  private async checkAndInitRegionDict() {
    const regionCount = await RegionDict.count();
    if (regionCount === 0) {
      console.log('📋 检测到区域字典数据缺失，开始初始化...');
      await this.initRegionDict();
    } else {
      console.log('📋 区域字典数据正常');
    }
  }

  /**
   * 检查并初始化古城字典
   */
  private async checkAndInitAncientCityDict() {
    const ancientCityCount = await AncientCityDict.count();
    if (ancientCityCount === 0) {
      console.log('📋 检测到古城字典数据缺失，开始初始化...');
      await this.initAncientCityDict();
    } else {
      console.log('📋 古城字典数据正常');
    }
  }

  /**
   * 检查并初始化类型字典
   */
  private async checkAndInitTypeDict() {
    const typeCount = await TypeDict.count();
    if (typeCount === 0) {
      console.log('📋 检测到类型字典数据缺失，开始初始化...');
      await this.initTypeDict();
    } else {
      console.log('📋 类型字典数据正常');
    }
  }

  /**
   * 检查并初始化关系字典
   */
  private async checkAndInitRelationshipDict() {
    const relationCount = await RelationshipDict.count();
    if (relationCount === 0) {
      console.log('📋 检测到关系字典数据缺失，开始初始化...');
      await this.initRelationshipDict();
    } else {
      console.log('📋 关系字典数据正常');
    }
  }

  /**
   * 检查并初始化默认用户
   */
  private async checkAndInitDefaultUser() {
    // 检查是否存在管理员用户
    const adminUser = await User.findOne({
      where: { username: 'admin' } as any,
    });

    if (!adminUser) {
      console.log('📋 检测到管理员用户缺失，开始创建...');
      await this.initDefaultUser();
    } else {
      console.log('📋 管理员用户存在');
      // 检查管理员用户的角色是否正确
      if (adminUser.role !== 'admin') {
        console.log('📋 检测到管理员用户角色异常，正在修复...');
        await adminUser.update({ role: 'admin' });
        console.log('  ✓ 管理员用户角色已修复');
      }
      // 检查管理员用户是否被禁用
      if (!adminUser.isActive) {
        console.log('📋 检测到管理员用户被禁用，正在启用...');
        await adminUser.update({ isActive: true });
        console.log('  ✓ 管理员用户已启用');
      }
    }
  }

  /**
   * 验证核心数据完整性
   */
  async validateCoreData(): Promise<{
    isValid: boolean;
    issues: string[];
    fixedIssues: string[];
  }> {
    const issues: string[] = [];
    const fixedIssues: string[] = [];

    console.log('📋 开始验证核心数据完整性...');

    // 检查管理员用户
    const adminUser = await User.findOne({
      where: { username: 'admin' } as any,
    });

    if (!adminUser) {
      issues.push('管理员用户不存在');
      try {
        await this.initDefaultUser();
        fixedIssues.push('已创建管理员用户');
      } catch (error) {
        issues.push(`创建管理员用户失败: ${error.message}`);
      }
    } else {
      if (adminUser.role !== 'admin') {
        issues.push('管理员用户角色异常');
        try {
          await adminUser.update({ role: 'admin' });
          fixedIssues.push('已修复管理员用户角色');
        } catch (error) {
          issues.push(`修复管理员用户角色失败: ${error.message}`);
        }
      }
      if (!adminUser.isActive) {
        issues.push('管理员用户被禁用');
        try {
          await adminUser.update({ isActive: true });
          fixedIssues.push('已启用管理员用户');
        } catch (error) {
          issues.push(`启用管理员用户失败: ${error.message}`);
        }
      }
    }

    // 检查基础字典数据
    const regionCount = await RegionDict.count();
    if (regionCount === 0) {
      issues.push('区域字典数据缺失');
      try {
        await this.initRegionDict();
        fixedIssues.push('已初始化区域字典数据');
      } catch (error) {
        issues.push(`初始化区域字典失败: ${error.message}`);
      }
    }

    const typeCount = await TypeDict.count();
    if (typeCount === 0) {
      issues.push('类型字典数据缺失');
      try {
        await this.initTypeDict();
        fixedIssues.push('已初始化类型字典数据');
      } catch (error) {
        issues.push(`初始化类型字典失败: ${error.message}`);
      }
    }

    const relationCount = await RelationshipDict.count();
    if (relationCount === 0) {
      issues.push('关系字典数据缺失');
      try {
        await this.initRelationshipDict();
        fixedIssues.push('已初始化关系字典数据');
      } catch (error) {
        issues.push(`初始化关系字典失败: ${error.message}`);
      }
    }

    const isValid = issues.length === 0;
    console.log(
      `📋 核心数据完整性验证完成，发现 ${issues.length} 个问题，修复 ${fixedIssues.length} 个问题`
    );

    return {
      isValid,
      issues,
      fixedIssues,
    };
  }

  /**
   * 初始化区域字典
   */
  private async initRegionDict() {
    if (INIT_CONFIG.verbose) {
      console.log(
        `  📋 准备初始化 ${INITIAL_REGION_DATA.length} 条区域字典数据...`
      );
    }

    await RegionDict.bulkCreate(INITIAL_REGION_DATA);

    if (INIT_CONFIG.verbose) {
      console.log('  ✓ 区域字典初始化完成');
      INITIAL_REGION_DATA.forEach(region => {
        console.log(`    - ${region.regionName} (${region.regionCode})`);
      });
    } else {
      console.log('  ✓ 区域字典初始化完成');
    }
  }

  /**
   * 初始化古城字典
   */
  private async initAncientCityDict() {
    if (INIT_CONFIG.verbose) {
      console.log(
        `  📋 准备初始化 ${INITIAL_ANCIENT_CITY_DATA.length} 条古城字典数据...`
      );
    }

    await AncientCityDict.bulkCreate(INITIAL_ANCIENT_CITY_DATA);

    if (INIT_CONFIG.verbose) {
      console.log('  ✓ 古城字典初始化完成');
      INITIAL_ANCIENT_CITY_DATA.forEach(city => {
        console.log(`    - ${city.cityName} (${city.cityCode})`);
      });
    } else {
      console.log('  ✓ 古城字典初始化完成');
    }
  }

  /**
   * 初始化类型字典
   */
  private async initTypeDict() {
    if (INIT_CONFIG.verbose) {
      console.log(
        `  📋 准备初始化 ${INITIAL_TYPE_DATA.length} 条类型字典数据...`
      );
    }

    // 第一步：创建所有父级类型（基础类别）
    const parentTypes = INITIAL_TYPE_DATA.filter(type => !type.parentTypeCode);
    const createdParents = await TypeDict.bulkCreate(parentTypes);

    if (INIT_CONFIG.verbose) {
      console.log('  ✓ 基础类别创建完成:');
      createdParents.forEach(type => {
        console.log(`    - ${type.typeName} (${type.typeCode})`);
      });
    }

    // 第二步：创建子类型，设置正确的parentId
    const childTypes = INITIAL_TYPE_DATA.filter(type => type.parentTypeCode);

    // 创建父类型代码到ID的映射
    const parentTypeMap = new Map();
    createdParents.forEach(parent => {
      parentTypeMap.set(parent.typeCode, parent.id);
    });

    // 为子类型设置正确的parentId
    const childTypesWithParentId = childTypes.map(child => {
      const parentId = parentTypeMap.get(child.parentTypeCode);
      if (!parentId) {
        throw new Error(`找不到父类型: ${child.parentTypeCode}`);
      }

      const { parentTypeCode, ...childData } = child;
      return {
        ...childData,
        parentId,
      };
    });

    const createdChildren = await TypeDict.bulkCreate(childTypesWithParentId);

    if (INIT_CONFIG.verbose) {
      console.log('  ✓ 子类型创建完成:');
      createdChildren.forEach(type => {
        const parentType = createdParents.find(p => p.id === type.parentId);
        console.log(
          `    - ${type.typeName} (${type.typeCode}) -> ${parentType?.typeName}`
        );
      });
    }

    console.log('  ✓ 类型字典初始化完成');
    console.log(
      `    总计: ${createdParents.length} 个基础类别, ${createdChildren.length} 个子类型`
    );
  }

  /**
   * 初始化关系字典
   */
  private async initRelationshipDict() {
    if (INIT_CONFIG.verbose) {
      console.log(
        `  📋 准备初始化 ${INITIAL_RELATIONSHIP_DATA.length} 条关系字典数据...`
      );
    }

    await RelationshipDict.bulkCreate(INITIAL_RELATIONSHIP_DATA);

    if (INIT_CONFIG.verbose) {
      console.log('  ✓ 关系字典初始化完成');
      INITIAL_RELATIONSHIP_DATA.forEach(relation => {
        console.log(
          `    - ${relation.relationName} (${relation.relationCode})`
        );
      });
    } else {
      console.log('  ✓ 关系字典初始化完成');
    }
  }

  /**
   * 初始化默认用户
   */
  private async initDefaultUser() {
    if (INIT_CONFIG.verbose) {
      console.log('  📋 准备创建默认管理员用户...');
    }

    // 创建默认管理员用户，密码会在实体setter中自动加密
    await User.create(INITIAL_USER_DATA as any);

    console.log(
      `  ✓ 默认管理员用户创建完成 (用户名: ${INITIAL_USER_DATA.username}, 密码: ${INITIAL_USER_DATA.password})`
    );

    if (INIT_CONFIG.verbose) {
      console.log(`    - 角色: ${INITIAL_USER_DATA.role}`);
      console.log(`    - 昵称: ${INITIAL_USER_DATA.nickname}`);
      console.log(
        `    - 状态: ${INITIAL_USER_DATA.isActive ? '启用' : '禁用'}`
      );
    }
  }

  /**
   * 更新现有数据的类型关联
   * 将现有的历史要素、山塬、水系数据关联到对应的基础类型
   */
  async updateExistingDataTypeAssociation() {
    console.log('📋 开始更新现有数据的类型关联...');

    try {
      // 获取基础类型ID
      const historicalElementType = await TypeDict.findOne({
        where: { typeCode: 'TYPE_HISTORICAL_ELEMENT' },
      });
      const mountainType = await TypeDict.findOne({
        where: { typeCode: 'TYPE_MOUNTAIN' },
      });
      const waterSystemType = await TypeDict.findOne({
        where: { typeCode: 'TYPE_WATER_SYSTEM' },
      });

      if (!historicalElementType || !mountainType || !waterSystemType) {
        throw new Error('基础类型未找到，请先初始化类型字典');
      }

      // 更新历史要素数据
      const historicalElementCount = await HistoricalElement.update(
        { typeDictId: historicalElementType.id },
        { where: { typeDictId: null } }
      );

      // 更新山塬数据
      const mountainCount = await Mountain.update(
        { typeDictId: mountainType.id },
        { where: { typeDictId: null } }
      );

      // 更新水系数据
      const waterSystemCount = await WaterSystem.update(
        { typeDictId: waterSystemType.id },
        { where: { typeDictId: null } }
      );

      console.log('📋 现有数据类型关联更新完成:');
      console.log(`  ✓ 历史要素: ${historicalElementCount[0]} 条记录`);
      console.log(`  ✓ 山塬: ${mountainCount[0]} 条记录`);
      console.log(`  ✓ 水系: ${waterSystemCount[0]} 条记录`);

      return {
        historicalElementCount: historicalElementCount[0],
        mountainCount: mountainCount[0],
        waterSystemCount: waterSystemCount[0],
      };
    } catch (error) {
      console.error('❌ 更新现有数据类型关联失败:', error.message);
      throw error;
    }
  }

  /**
   * 完整的数据初始化（包括更新现有数据）
   */
  async fullInitialization() {
    console.log('📋 开始完整的数据初始化...');

    // 1. 初始化基础数据
    await this.initializeData();

    // 2. 更新现有数据的类型关联
    await this.updateExistingDataTypeAssociation();

    // 3. 初始化Excel模板
    await this.initializeExcelTemplates();

    // 4. 初始化上传目录
    await this.initializeUploadDirectories();

    console.log('📋 完整的数据初始化完成');
  }
}
