# 智慧营建系统 API 文档

## 概述

智慧营建系统提供完整的 RESTful API 接口，用于管理和展示关中地区的山塬、水系、历史要素等地理文化数据。

## 基础信息

- **基础URL**: `http://localhost:7001`
- **API版本**: v1.0
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证方式

系统采用 JWT (JSON Web Token) 认证方式：

1. 通过登录接口获取 token
2. 在请求头中添加：`Authorization: Bearer <token>`
3. 管理端接口需要认证，公共接口无需认证

## 统一响应格式

**注意**: 新架构已移除统一的ResponseDTO包装，直接返回业务数据。

### 成功响应
```json
// 直接返回业务数据
{
  "id": 1,
  "name": "华山",
  "longitude": 110.0910,
  "latitude": 34.4880
}

// 或分页数据
{
  "data": [...],
  "total": 100,
  "page": 1,
  "pageSize": 10
}
```

### 错误响应
```json
{
  "message": "错误信息",
  "statusCode": 400,
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/xxx"
}
```

## 分页响应格式

```json
{
  "data": [],
  "total": 100,
  "page": 1,
  "pageSize": 10
}
```

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未认证或认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突（如重复创建） |
| 500 | 服务器内部错误 |

## API 模块

### 🌐 门户数字化公共接口 (无需认证)
- **[门户数字化接口总览](./public/README.md)** - 完整的门户数字化接口文档
- **[门户概览接口](./public/portal-overview.md)** - 门户首页概览数据
- **[数字化统计接口](./public/statistics-digital.md)** - 统计分析和数据可视化
- **[关系网络接口](./public/relationship-network.md)** - 关系网络和知识图谱
- **[统一搜索接口](./public/search-unified.md)** - 跨类型统一搜索
- **[地图数据接口](./public/map-data.md)** - 地图可视化数据
- **[字典数据接口](./public/dictionary-data.md)** - 字典数据管理
- **[历史要素接口](./public/historical-element.md)** - 历史要素详细数据

### 🔐 管理端接口 (需要认证)
- [用户管理接口](./user-management.md) - 用户认证、登录、权限管理
- [仪表盘接口](./admin/dashboard.md) - 管理端数据概览、统计分析、数据质量报告
- [统计接口概览](./admin/statistics-overview.md) - 统一的统计分析接口说明
- [山塬管理接口](./admin/mountain-management.md) - 山塬数据管理
- [山塬导入接口](./admin/mountain-import.md) - 山塬Excel批量导入
- [水系管理接口](./admin/water-system-management.md) - 水系数据管理
- [水系导入接口](./admin/water-system-import.md) - 水系Excel批量导入
- [历史要素管理接口](./admin/historical-element-management.md) - 历史要素管理
- [历史要素导入接口](./admin/historical-element-import.md) - 历史要素Excel批量导入
- [字典管理接口](./dictionary-management.md) - 字典数据管理
- [系统管理接口](./admin/system.md) - 系统管理功能

### 📁 通用接口
- [资源管理接口](./resource-management.md) - 文件上传功能

## 模块说明

### 仪表盘模块
提供数据可视化和统计分析功能，包括：
- 地图数据展示：山塬、水系、历史要素的地理位置信息
- 统计分析：各类数据的数量统计和分布分析
- 时间轴数据：历史要素按建造时间的发展脉络
- 数据概览：综合性的数据统计报告

### 用户管理模块
提供用户认证和权限管理功能，包括：
- 管理员登录：用户名密码认证
- 用户信息：获取当前用户信息
- Token管理：Token刷新和退出登录
- 权限控制：基于角色的权限验证

### 资源管理模块
提供文件上传和管理功能，包括：
- 单文件上传：支持图片文件上传
- 多文件上传：批量文件上传
- 文件验证：文件类型和大小验证
- 静态文件访问：上传文件的访问路径

### 字典管理模块
提供系统字典数据管理，包括：
- 区域字典：地理区域的层级管理
- 类型字典：历史要素类型分类
- 关系字典：要素间关系类型定义
- 树形结构：支持父子级关系

### 山塬管理模块
提供山塬数据的完整管理，包括：
- 基础信息：名称、编号、坐标、高度
- 历史记录：相关历史文献记载
- 区域关联：所属区域信息
- 统计分析：按区域和高度范围的统计分析
- 批量操作：Excel批量导入功能

### 水系管理模块
提供水系数据的完整管理，包括：
- 基础信息：名称、编号、坐标、长度面积
- 历史记录：相关历史文献记载
- 区域关联：所属区域信息
- 统计分析：按区域和长度面积类型的统计分析
- 批量操作：Excel批量导入功能

### 历史要素管理模块
提供历史文化要素的完整管理，包括：
- 基础信息：名称、编号、坐标、建造时间
- 类型分类：按历史要素类型分类
- 位置描述：详细的位置信息描述
- 时间轴：按建造时间的历史发展脉络
- 统计分析：按类型、区域和时期的统计分析
- 批量操作：Excel批量导入功能

## 快速开始

### 1. 获取地图数据
```bash
curl -X GET "http://localhost:7001/public/map/data"
```

### 2. 用户登录
```bash
curl -X POST "http://localhost:7001/admin/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

### 3. 创建山塬数据
```bash
curl -X POST "http://localhost:7001/admin/mountain" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "华山",
    "code": "HS001",
    "longitude": 110.0910,
    "latitude": 34.4880,
    "height": 2154,
    "regionDictId": 1
  }'
```

### 4. 获取统计数据
```bash
curl -X GET "http://localhost:7001/public/statistic/basic"
```

### 5. 文件上传
```bash
curl -X POST "http://localhost:7001/api/upload/file" \
  -H "Authorization: Bearer <token>" \
  -F "file=@/path/to/image.jpg"
```

### 6. 创建字典数据
```bash
curl -X POST "http://localhost:7001/admin/dictionary/region" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "regionCode": "GUANZHONG",
    "regionName": "关中地区",
    "status": 1
  }'
```

## 注意事项

1. **时间格式**: 统一使用 ISO 8601 格式 (`YYYY-MM-DDTHH:mm:ss.sssZ`)
2. **坐标系统**: 使用 WGS84 坐标系统
3. **文件上传**: 支持的图片格式为 jpg, jpeg, png, gif, bmp, webp
4. **请求限制**: 单次上传文件大小限制为 50MB
5. **分页参数**: page 从 1 开始，pageSize 最大为 100

## 错误处理

### 常见错误及解决方案

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| 用户名或密码错误 | 登录凭据无效 | 检查用户名和密码 |
| 认证令牌无效或已过期 | Token 失效 | 重新登录获取新 token |
| 权限不足 | 非管理员用户访问管理接口 | 使用管理员账户登录 |
| 数据不存在 | 请求的资源不存在 | 检查资源 ID 是否正确 |
| 参数验证失败 | 请求参数不符合要求 | 检查参数格式和必填项 |
