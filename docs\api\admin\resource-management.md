# 资源管理 API 文档

## 概述

资源管理模块提供完整的文件上传和照片管理功能，支持图片文件的上传、存储和数据库记录管理。包括：

- 文件上传（支持配置化存储路径）
- 照片记录管理（与山塬、水系、历史要素关联）
- 批量操作支持
- 统计信息查询

---

## 统一响应格式

### 成功响应
所有接口成功时返回统一格式：
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应
所有接口失败时返回统一格式：
```json
{
  "errCode": 400,  // 错误码
  "msg": "错误信息"  // 错误描述
}
```

---

## 上传照片文件

### 接口信息

- **URL**: `/admin/photo/upload`
- **方法**: `POST`
- **认证**: 需要认证
- **Content-Type**: `multipart/form-data`

### 请求头

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 是 | Bearer {token} |
| Content-Type | string | 是 | multipart/form-data |

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | file | 是 | 要上传的文件 |

### 查询参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| photoName | string | 否 | 照片名称（默认使用文件名），支持URL编码的中文字符 |
| entityType | string | 否 | 关联实体类型（mountain/waterSystem/historicalElement） |
| entityId | number | 否 | 关联实体ID |

### 表单字段参数

也可以通过表单字段传递参数（查询参数优先级更高）：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| photoName | string | 否 | 照片名称（默认使用文件名） |
| entityType | string | 否 | 关联实体类型 |
| entityId | number | 否 | 关联实体ID |

### 文件限制

- **支持格式**: .jpg, .jpeg, .png, .gif, .bmp, .webp
- **文件大小**: 最大50MB
- **数量限制**: 单次上传1个文件

### 中文文件名处理

- **自动解码**: 接口会自动对URL编码的中文字符进行解码
- **编码格式**: 支持UTF-8编码的中文字符
- **示例**: `%E4%BB%93%E9%A2%89%E5%BA%99.png` 会被解码为 `仓颉庙.png`
- **兼容性**: 同时支持查询参数和表单字段传递文件名

### 请求示例

#### 基本上传
```bash
curl -X POST "http://localhost:7001/admin/photo/upload" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -F "file=@/path/to/image.jpg"
```

#### 上传并关联实体
```bash
curl -X POST "http://localhost:7001/admin/photo/upload?photoName=山塬照片&entityType=mountain&entityId=1" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -F "file=@/path/to/mountain.jpg"
```

#### 上传中文文件名（URL编码）
```bash
# 中文文件名需要进行URL编码
curl -X POST "http://localhost:7001/admin/photo/upload?photoName=%E4%BB%93%E9%A2%89%E5%BA%99.png" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -F "file=@/path/to/image.png"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "url": "/uploads/2025/08/27/uuid-filename.png",
    "photoId": 123,
    "filename": "test-photo.png",
    "size": 66
  }
}
```

**注意**:
- 文件名会自动添加UUID前缀避免冲突
- 自动创建照片记录并关联到指定实体

---

## 删除照片文件

### 接口信息

- **URL**: `/admin/photo/{id}/file`
- **方法**: `DELETE`
- **认证**: 需要认证

### 请求头

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 是 | Bearer {token} |

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 照片ID |

### 请求示例

```bash
curl -X DELETE "http://localhost:7001/admin/photo/123/file" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### 响应示例

#### 成功响应
```json
{
  "message": "照片删除成功"
}
```

#### 错误响应
```json
{
  "message": "照片记录不存在",
  "statusCode": 400,
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/admin/photo/123/file"
}
```

**注意**:
- 删除操作会同时删除物理文件和数据库记录
- 如果物理文件删除失败，仍会删除数据库记录
- 删除操作不可逆，请谨慎操作

---

## 错误处理

### 常见错误

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 请选择要上传的文件 | 未提供文件 |
| 400 | 不支持的文件类型，仅支持图片文件 | 文件格式不支持 |
| 400 | 文件大小超过限制（50MB） | 文件过大 |
| 400 | 照片记录不存在 | 指定的照片ID不存在 |
| 400 | 不支持的实体类型 | entityType参数值无效 |
| 401 | 未提供认证令牌 | 缺少Authorization头 |
| 401 | 认证令牌无效或已过期 | Token无效 |
| 403 | 权限不足，需要管理员权限 | 非管理员用户访问 |
| 500 | 文件上传失败 | 服务器内部错误 |
| 500 | 删除文件失败 | 物理文件删除失败 |

### 错误响应示例

```json
{
  "errCode": 400,
  "msg": "不支持的文件类型，仅支持图片文件"
}
```

---

## 文件访问

### 静态文件访问

上传成功后，文件可通过返回的URL直接访问：

#### 开发环境（项目内路径）
```
http://localhost:7001/public/uploads/2024/01/15/uuid-filename.jpg
```

#### 生产环境（外部路径）
```
http://localhost:7001/uploads/2024/01/15/uuid-filename.jpg
```

### URL结构说明

- **基础路径**:
  - 开发环境: `/public/uploads/`
  - 生产环境: `/uploads/`
- **日期目录**: `YYYY/MM/DD/`
- **文件名**: `UUID.扩展名`（使用UUID确保唯一性）

---

## 使用说明

### 前端集成示例

#### JavaScript/Fetch

```javascript
const uploadFile = async (file) => {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await fetch('/api/upload/file', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });
  
  const result = await response.json();
  return result;
};
```

#### jQuery

```javascript
$('#fileInput').change(function() {
  const file = this.files[0];
  const formData = new FormData();
  formData.append('file', file);
  
  $.ajax({
    url: '/api/upload/file',
    type: 'POST',
    data: formData,
    headers: {
      'Authorization': 'Bearer ' + token
    },
    processData: false,
    contentType: false,
    success: function(result) {
      console.log('上传成功:', result.data.url);
    },
    error: function(xhr) {
      console.error('上传失败:', xhr.responseJSON.msg);
    }
  });
});
```

### 注意事项

1. **认证要求**: 所有上传接口都需要有效的JWT Token
2. **文件格式**: 仅支持图片格式文件
3. **文件大小**: 单个文件最大50MB
4. **存储路径**: 文件按日期目录存储，便于管理
5. **文件命名**: 自动添加时间戳避免文件名冲突
6. **错误处理**: 上传前会验证文件类型和大小
7. **批量上传**: 支持多文件同时上传，失败时会返回具体错误信息

### 最佳实践

1. **文件验证**: 前端也应进行文件类型和大小验证
2. **进度显示**: 大文件上传时建议显示上传进度
3. **错误处理**: 妥善处理各种上传错误情况
4. **文件预览**: 上传前可提供文件预览功能
5. **重试机制**: 网络不稳定时可实现重试机制
6. **照片记录**: 建议在上传时同时创建照片记录，便于后续管理
7. **存储配置**: 生产环境建议配置外部存储路径，避免代码更新时文件丢失

---

## 照片管理 API

### 创建照片记录

- **URL**: `/admin/photo`
- **方法**: `POST`
- **认证**: 需要认证

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 照片名称 |
| url | string | 是 | 照片URL路径 |
| mountainId | number | 否 | 关联的山塬ID |
| waterSystemId | number | 否 | 关联的水系ID |
| historicalElementId | number | 否 | 关联的历史要素ID |

#### 请求示例

```json
{
  "name": "照片名称",
  "url": "/uploads/2024/01/15/image_uuid.jpg",
  "mountainId": 1,
  "waterSystemId": null,
  "historicalElementId": null
}
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 123,
    "name": "照片名称",
    "url": "/uploads/2024/01/15/image_uuid.jpg",
    "mountainId": 1,
    "waterSystemId": null,
    "historicalElementId": null,
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

### 更新照片记录

- **URL**: `/admin/photo/{id}`
- **方法**: `PUT`
- **认证**: 需要认证

#### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 照片ID |

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 否 | 照片名称 |
| url | string | 否 | 照片URL路径 |
| mountainId | number | 否 | 关联的山塬ID |
| waterSystemId | number | 否 | 关联的水系ID |
| historicalElementId | number | 否 | 关联的历史要素ID |

### 删除照片记录

- **URL**: `/admin/photo/{id}`
- **方法**: `DELETE`
- **认证**: 需要认证

#### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 照片ID |

### 获取照片列表

- **URL**: `/admin/photo`
- **方法**: `GET`
- **认证**: 需要认证

#### 查询参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码（默认1） |
| pageSize | number | 否 | 每页数量（默认10） |
| keyword | string | 否 | 搜索关键词 |

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 123,
        "name": "照片名称",
        "url": "/uploads/2024/01/15/image_uuid.jpg",
        "mountainId": 1,
        "mountain": {
          "id": 1,
          "name": "某山塬"
        },
        "waterSystemId": null,
        "historicalElementId": null,
        "createdAt": "2024-01-15T10:30:00.000Z"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10
  }
}
```

### 获取照片详情

- **URL**: `/admin/photo/{id}`
- **方法**: `GET`
- **认证**: 需要认证

#### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 照片ID |

### 根据实体获取照片

- **URL**: `/admin/photo/entity/{entityType}/{entityId}`
- **方法**: `GET`
- **认证**: 需要认证

#### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| entityType | string | 是 | 实体类型（mountain/waterSystem/historicalElement） |
| entityId | number | 是 | 实体ID |

#### 请求示例

```bash
GET /admin/photo/entity/mountain/1
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 123,
      "name": "山塬照片1",
      "url": "/uploads/2024/01/15/mountain1.jpg",
      "mountainId": 1,
      "createdAt": "2024-01-15T10:30:00.000Z"
    },
    {
      "id": 124,
      "name": "山塬照片2",
      "url": "/uploads/2024/01/15/mountain2.jpg",
      "mountainId": 1,
      "createdAt": "2024-01-15T11:00:00.000Z"
    }
  ]
}
```

### 批量创建照片记录

- **URL**: `/admin/photo/batch`
- **方法**: `POST`
- **认证**: 需要认证

#### 请求参数

```json
{
  "photos": [
    {
      "name": "照片1",
      "url": "/uploads/2024/01/15/image1.jpg",
      "mountainId": 1
    },
    {
      "name": "照片2",
      "url": "/uploads/2024/01/15/image2.jpg",
      "mountainId": 1
    }
  ]
}
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "message": "批量创建成功",
    "count": 2,
    "data": [
      {
        "id": 125,
        "name": "照片1",
        "url": "/uploads/2024/01/15/image1.jpg",
        "mountainId": 1
      },
      {
        "id": 126,
        "name": "照片2",
        "url": "/uploads/2024/01/15/image2.jpg",
        "mountainId": 1
      }
    ]
  }
}
```

### 批量删除照片记录

- **URL**: `/admin/photo/batch`
- **方法**: `DELETE`
- **认证**: 需要认证

#### 请求参数

```json
{
  "ids": [1, 2, 3]
}
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "message": "批量删除成功",
    "count": 3
  }
}
```

### 批量关联照片

- **URL**: `/admin/photo/associate/{entityType}/{entityId}`
- **方法**: `POST`
- **认证**: 需要认证

#### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| entityType | string | 是 | 实体类型（mountain/waterSystem/historicalElement） |
| entityId | number | 是 | 实体ID |

#### 请求参数

```json
{
  "photoIds": [1, 2, 3]
}
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "message": "关联成功",
    "count": 3,
    "data": [
      {
        "id": 1,
        "name": "照片1",
        "url": "/uploads/2024/01/15/image1.jpg",
        "mountainId": 1
      },
      {
        "id": 2,
        "name": "照片2",
        "url": "/uploads/2024/01/15/image2.jpg",
        "mountainId": 1
      }
    ]
  }
}
```

### 取消照片关联

- **URL**: `/admin/photo/disassociate`
- **方法**: `POST`
- **认证**: 需要认证

#### 请求参数

```json
{
  "photoIds": [1, 2, 3]
}
```

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "message": "取消关联成功",
    "count": 3,
    "data": [
      {
        "id": 1,
        "name": "照片1",
        "url": "/uploads/2024/01/15/image1.jpg",
        "mountainId": null,
        "waterSystemId": null,
        "historicalElementId": null
      }
    ]
  }
}
```

### 获取照片统计

- **URL**: `/admin/photo/statistics/overview`
- **方法**: `GET`
- **认证**: 需要认证

#### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "total": 150,
    "mountainPhotos": 60,
    "waterSystemPhotos": 45,
    "historicalElementPhotos": 30,
    "unassignedPhotos": 15
  }
}
```

---

## 配置说明

### 环境变量配置

在 `.env` 文件中配置以下参数：

```bash
# 文件上传存储路径（生产环境建议使用绝对路径）
UPLOAD_PATH=/data/uploads

# 开发环境可以使用相对路径
# UPLOAD_PATH=./uploads
```

### 存储路径说明

1. **开发环境**：默认使用项目内的 `public/uploads` 目录
2. **生产环境**：建议配置 `UPLOAD_PATH` 环境变量指向独立存储目录
3. **自动创建**：系统会自动检查并创建不存在的目录路径
4. **文件访问**：
   - 项目内路径：`http://domain/public/uploads/...`
   - 外部路径：`http://domain/uploads/...`

### 目录结构

```
uploads/
├── 2024/
│   ├── 01/
│   │   ├── 15/
│   │   │   ├── uuid1.jpg
│   │   │   └── uuid2.png
│   │   └── 16/
│   └── 02/
└── 2025/
```

---

## 使用流程

### 1. 基本文件上传

```javascript
// 仅上传文件，不创建数据库记录
const formData = new FormData();
formData.append('file', file);

const response = await fetch('/api/upload/file', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});
```

### 2. 上传并创建照片记录

```javascript
// 上传文件并自动创建照片记录
const formData = new FormData();
formData.append('file', file);

const response = await fetch('/api/upload/file?createPhoto=true&photoName=照片名称', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});
```

### 3. 上传并关联实体

```javascript
// 上传文件并关联到山塬
const formData = new FormData();
formData.append('file', file);

const response = await fetch('/api/upload/file?createPhoto=true&entityType=mountain&entityId=1', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});
```

### 4. 后续管理照片

```javascript
// 获取山塬的所有照片
const photos = await fetch('/admin/photo/entity/mountain/1', {
  headers: { 'Authorization': `Bearer ${token}` }
});

// 批量关联照片到水系
await fetch('/admin/photo/associate/waterSystem/2', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ photoIds: [1, 2, 3] })
});
```

---

## 注意事项

1. **文件安全**：只允许上传图片格式文件
2. **大小限制**：单个文件最大50MB
3. **存储配置**：生产环境务必配置外部存储路径
4. **路径处理**：系统会自动检查并创建不存在的目录路径
5. **权限控制**：所有管理接口都需要认证
6. **关联约束**：一张照片只能关联一个实体（山塬、水系或历史要素）
7. **文件命名**：系统自动生成UUID文件名，避免冲突
8. **目录结构**：按年/月/日自动创建目录结构
9. **错误处理**：目录创建失败时会抛出明确的错误信息
