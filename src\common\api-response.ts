/**
 * 统一的API响应格式
 */
export class ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  code?: number;
  timestamp: string;

  constructor(success: boolean, message: string, data?: T, code?: number) {
    this.success = success;
    this.message = message;
    this.data = data;
    this.code = code;
    this.timestamp = new Date().toISOString();
  }

  /**
   * 成功响应
   */
  static success<T>(data?: T, message = '操作成功'): ApiResponse<T> {
    return new ApiResponse(true, message, data, 200);
  }

  /**
   * 错误响应
   */
  static error<T>(message = '操作失败', data?: T, code = 500): ApiResponse<T> {
    return new ApiResponse(false, message, data, code);
  }

  /**
   * 未授权响应
   */
  static unauthorized(message = '未授权访问'): ApiResponse {
    return new ApiResponse(false, message, null, 401);
  }

  /**
   * 禁止访问响应
   */
  static forbidden(message = '禁止访问'): ApiResponse {
    return new ApiResponse(false, message, null, 403);
  }

  /**
   * 资源未找到响应
   */
  static notFound(message = '资源未找到'): ApiResponse {
    return new ApiResponse(false, message, null, 404);
  }

  /**
   * 参数验证错误响应
   */
  static validationError(message = '参数验证失败', data?: any): ApiResponse {
    return new ApiResponse(false, message, data, 400);
  }
}
