# 字典数据接口详细说明

## 接口概述
字典数据接口提供系统中各类字典数据，包括区域字典、类型字典等，用于前端筛选、展示和数据关联。

## 1. 区域字典接口

### 1.1 获取区域字典列表
获取所有区域字典数据的平铺列表。

**接口地址：** `GET /openapi/region-dict/all`

**响应示例：**
```json
{
  "errCode": 0,
  "data": [
    {
      "id": 1,
      "regionName": "陕西省",
      "regionCode": "SX",
      "parentId": null,
      "level": 1,
      "sort": 1,
      "status": 1,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    {
      "id": 2,
      "regionName": "西安市",
      "regionCode": "XA",
      "parentId": 1,
      "level": 2,
      "sort": 1,
      "status": 1,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    {
      "id": 3,
      "regionName": "雁塔区",
      "regionCode": "YT",
      "parentId": 2,
      "level": 3,
      "sort": 1,
      "status": 1,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "msg": "OK"
}
```

### 1.2 获取区域字典树形结构
获取区域字典的层级树形结构数据。

**接口地址：** `GET /openapi/region-dict/tree`

**响应示例：**
```json
{
  "errCode": 0,
  "data": [
    {
      "id": 1,
      "regionName": "陕西省",
      "regionCode": "SX",
      "parentId": null,
      "level": 1,
      "sort": 1,
      "status": 1,
      "children": [
        {
          "id": 2,
          "regionName": "西安市",
          "regionCode": "XA",
          "parentId": 1,
          "level": 2,
          "sort": 1,
          "status": 1,
          "children": [
            {
              "id": 3,
              "regionName": "雁塔区",
              "regionCode": "YT",
              "parentId": 2,
              "level": 3,
              "sort": 1,
              "status": 1,
              "children": []
            },
            {
              "id": 4,
              "regionName": "碑林区",
              "regionCode": "BL",
              "parentId": 2,
              "level": 3,
              "sort": 2,
              "status": 1,
              "children": []
            }
          ]
        },
        {
          "id": 5,
          "regionName": "宝鸡市",
          "regionCode": "BJ",
          "parentId": 1,
          "level": 2,
          "sort": 2,
          "status": 1,
          "children": []
        }
      ]
    }
  ],
  "msg": "OK"
}
```

## 2. 类型字典接口

### 2.1 获取类型字典列表
获取所有类型字典数据的平铺列表。

**接口地址：** `GET /openapi/type-dict/all`

**响应示例：**
```json
{
  "errCode": 0,
  "data": [
    {
      "id": 1,
      "typeName": "名山",
      "typeCode": "FAMOUS_MOUNTAIN",
      "category": "mountain",
      "parentId": null,
      "level": 1,
      "sort": 1,
      "status": 1,
      "description": "著名的山峰",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    {
      "id": 2,
      "typeName": "普通山峰",
      "typeCode": "NORMAL_MOUNTAIN",
      "category": "mountain",
      "parentId": 1,
      "level": 2,
      "sort": 1,
      "status": 1,
      "description": "一般的山峰",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    {
      "id": 3,
      "typeName": "河流",
      "typeCode": "RIVER",
      "category": "waterSystem",
      "parentId": null,
      "level": 1,
      "sort": 1,
      "status": 1,
      "description": "自然河流",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    {
      "id": 4,
      "typeName": "古建筑",
      "typeCode": "ANCIENT_BUILDING",
      "category": "historicalElement",
      "parentId": null,
      "level": 1,
      "sort": 1,
      "status": 1,
      "description": "历史古建筑",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "msg": "OK"
}
```

### 2.2 获取类型字典树形结构
获取类型字典的层级树形结构数据。

**接口地址：** `GET /openapi/type-dict/tree`

**响应示例：**
```json
{
  "errCode": 0,
  "data": [
    {
      "id": 1,
      "typeName": "名山",
      "typeCode": "FAMOUS_MOUNTAIN",
      "category": "mountain",
      "parentId": null,
      "level": 1,
      "sort": 1,
      "status": 1,
      "description": "著名的山峰",
      "children": [
        {
          "id": 2,
          "typeName": "五岳",
          "typeCode": "FIVE_MOUNTAINS",
          "category": "mountain",
          "parentId": 1,
          "level": 2,
          "sort": 1,
          "status": 1,
          "description": "中国五大名山",
          "children": []
        }
      ]
    },
    {
      "id": 3,
      "typeName": "水系",
      "typeCode": "WATER_SYSTEM",
      "category": "waterSystem",
      "parentId": null,
      "level": 1,
      "sort": 2,
      "status": 1,
      "description": "水系相关",
      "children": [
        {
          "id": 4,
          "typeName": "河流",
          "typeCode": "RIVER",
          "category": "waterSystem",
          "parentId": 3,
          "level": 2,
          "sort": 1,
          "status": 1,
          "description": "自然河流",
          "children": []
        },
        {
          "id": 5,
          "typeName": "湖泊",
          "typeCode": "LAKE",
          "category": "waterSystem",
          "parentId": 3,
          "level": 2,
          "sort": 2,
          "status": 1,
          "description": "天然湖泊",
          "children": []
        }
      ]
    }
  ],
  "msg": "OK"
}
```

## 3. 字段说明

### 3.1 区域字典字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 区域ID |
| regionName | string | 区域名称 |
| regionCode | string | 区域编码 |
| parentId | number | 父级区域ID，null表示顶级 |
| level | number | 层级，1为省级，2为市级，3为区县级 |
| sort | number | 排序号 |
| status | number | 状态，1=启用，0=禁用 |
| children | array | 子级区域列表（仅树形结构返回） |

### 3.2 类型字典字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 类型ID |
| typeName | string | 类型名称 |
| typeCode | string | 类型编码 |
| category | string | 所属分类：mountain/waterSystem/historicalElement |
| parentId | number | 父级类型ID，null表示顶级 |
| level | number | 层级，1为一级分类，2为二级分类 |
| sort | number | 排序号 |
| status | number | 状态，1=启用，0=禁用 |
| description | string | 类型描述 |
| children | array | 子级类型列表（仅树形结构返回） |

## 4. 使用示例

### 4.1 创建区域选择器
```javascript
// 创建级联区域选择器
async function createRegionSelector() {
  try {
    const response = await fetch('/openapi/region-dict/tree');
    const data = await response.json();
    
    if (data.errCode === 0) {
      const regions = data.data;
      
      // 创建省级选择器
      const provinceSelect = document.getElementById('province-select');
      provinceSelect.innerHTML = '<option value="">请选择省份</option>';
      
      regions.forEach(province => {
        const option = document.createElement('option');
        option.value = province.id;
        option.textContent = province.regionName;
        option.dataset.children = JSON.stringify(province.children);
        provinceSelect.appendChild(option);
      });
      
      // 绑定省份变化事件
      provinceSelect.addEventListener('change', function() {
        updateCitySelector(this.selectedOptions[0]);
      });
    }
  } catch (error) {
    console.error('加载区域数据失败:', error);
  }
}

// 更新市级选择器
function updateCitySelector(provinceOption) {
  const citySelect = document.getElementById('city-select');
  const districtSelect = document.getElementById('district-select');
  
  // 清空下级选择器
  citySelect.innerHTML = '<option value="">请选择城市</option>';
  districtSelect.innerHTML = '<option value="">请选择区县</option>';
  
  if (provinceOption && provinceOption.dataset.children) {
    const cities = JSON.parse(provinceOption.dataset.children);
    
    cities.forEach(city => {
      const option = document.createElement('option');
      option.value = city.id;
      option.textContent = city.regionName;
      option.dataset.children = JSON.stringify(city.children);
      citySelect.appendChild(option);
    });
    
    // 绑定城市变化事件
    citySelect.addEventListener('change', function() {
      updateDistrictSelector(this.selectedOptions[0]);
    });
  }
}

// 更新区县选择器
function updateDistrictSelector(cityOption) {
  const districtSelect = document.getElementById('district-select');
  districtSelect.innerHTML = '<option value="">请选择区县</option>';
  
  if (cityOption && cityOption.dataset.children) {
    const districts = JSON.parse(cityOption.dataset.children);
    
    districts.forEach(district => {
      const option = document.createElement('option');
      option.value = district.id;
      option.textContent = district.regionName;
      districtSelect.appendChild(option);
    });
  }
}
```

### 4.2 创建类型筛选器
```javascript
// 创建按分类的类型筛选器
async function createTypeFilter() {
  try {
    const response = await fetch('/openapi/type-dict/all');
    const data = await response.json();
    
    if (data.errCode === 0) {
      const types = data.data;
      
      // 按分类分组
      const groupedTypes = {
        mountain: [],
        waterSystem: [],
        historicalElement: []
      };
      
      types.forEach(type => {
        if (groupedTypes[type.category]) {
          groupedTypes[type.category].push(type);
        }
      });
      
      // 创建筛选器HTML
      const filterHTML = Object.keys(groupedTypes).map(category => {
        const categoryName = getCategoryName(category);
        const categoryTypes = groupedTypes[category];
        
        return `
          <div class="type-filter-group">
            <h4>${categoryName}</h4>
            <div class="type-options">
              ${categoryTypes.map(type => `
                <label>
                  <input type="checkbox" value="${type.id}" data-category="${category}">
                  ${type.typeName}
                </label>
              `).join('')}
            </div>
          </div>
        `;
      }).join('');
      
      document.getElementById('type-filter').innerHTML = filterHTML;
      
      // 绑定筛选事件
      const checkboxes = document.querySelectorAll('#type-filter input[type="checkbox"]');
      checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', handleTypeFilterChange);
      });
    }
  } catch (error) {
    console.error('加载类型数据失败:', error);
  }
}

function getCategoryName(category) {
  const names = {
    mountain: '山塬',
    waterSystem: '水系',
    historicalElement: '历史要素'
  };
  return names[category] || category;
}

function handleTypeFilterChange() {
  const selectedTypes = Array.from(document.querySelectorAll('#type-filter input:checked'))
    .map(cb => cb.value);
  
  console.log('选中的类型ID:', selectedTypes);
  
  // 触发数据筛选
  filterDataByTypes(selectedTypes);
}
```

### 4.3 创建面包屑导航
```javascript
// 根据区域ID创建面包屑导航
async function createBreadcrumb(regionId) {
  try {
    const response = await fetch('/openapi/region-dict/all');
    const data = await response.json();
    
    if (data.errCode === 0) {
      const regions = data.data;
      const breadcrumbPath = buildBreadcrumbPath(regions, regionId);
      
      const breadcrumbHTML = breadcrumbPath.map((region, index) => {
        const isLast = index === breadcrumbPath.length - 1;
        return `
          <span class="breadcrumb-item ${isLast ? 'active' : ''}">
            ${isLast ? region.regionName : `<a href="#" data-region-id="${region.id}">${region.regionName}</a>`}
          </span>
        `;
      }).join(' > ');
      
      document.getElementById('breadcrumb').innerHTML = breadcrumbHTML;
      
      // 绑定面包屑点击事件
      const breadcrumbLinks = document.querySelectorAll('#breadcrumb a');
      breadcrumbLinks.forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          const regionId = this.dataset.regionId;
          navigateToRegion(regionId);
        });
      });
    }
  } catch (error) {
    console.error('创建面包屑失败:', error);
  }
}

// 构建面包屑路径
function buildBreadcrumbPath(regions, targetId) {
  const regionMap = new Map();
  regions.forEach(region => {
    regionMap.set(region.id, region);
  });
  
  const path = [];
  let currentId = targetId;
  
  while (currentId) {
    const region = regionMap.get(currentId);
    if (region) {
      path.unshift(region);
      currentId = region.parentId;
    } else {
      break;
    }
  }
  
  return path;
}
```

### 4.4 字典数据缓存
```javascript
// 字典数据缓存管理
class DictionaryCache {
  constructor() {
    this.cache = new Map();
    this.expireTime = 30 * 60 * 1000; // 30分钟过期
  }
  
  async getRegionDict() {
    const cacheKey = 'region-dict';
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.expireTime) {
      return cached.data;
    }
    
    try {
      const response = await fetch('/openapi/region-dict/all');
      const data = await response.json();
      
      if (data.errCode === 0) {
        this.cache.set(cacheKey, {
          data: data.data,
          timestamp: Date.now()
        });
        return data.data;
      }
    } catch (error) {
      console.error('获取区域字典失败:', error);
      return cached ? cached.data : [];
    }
  }
  
  async getTypeDict() {
    const cacheKey = 'type-dict';
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.expireTime) {
      return cached.data;
    }
    
    try {
      const response = await fetch('/openapi/type-dict/all');
      const data = await response.json();
      
      if (data.errCode === 0) {
        this.cache.set(cacheKey, {
          data: data.data,
          timestamp: Date.now()
        });
        return data.data;
      }
    } catch (error) {
      console.error('获取类型字典失败:', error);
      return cached ? cached.data : [];
    }
  }
  
  clearCache() {
    this.cache.clear();
  }
}

// 使用示例
const dictCache = new DictionaryCache();

// 获取区域字典
dictCache.getRegionDict().then(regions => {
  console.log('区域字典:', regions);
});

// 获取类型字典
dictCache.getTypeDict().then(types => {
  console.log('类型字典:', types);
});
```

## 5. 最佳实践

### 5.1 数据缓存
- 字典数据相对稳定，建议在前端进行缓存
- 设置合理的缓存过期时间（建议30分钟）
- 提供手动刷新缓存的功能

### 5.2 用户体验
- 使用级联选择器提供良好的区域选择体验
- 提供搜索功能快速定位字典项
- 显示面包屑导航帮助用户了解当前位置

### 5.3 性能优化
- 避免频繁请求字典接口
- 使用树形结构减少数据传输量
- 实现懒加载减少初始加载时间

### 5.4 错误处理
- 提供字典数据加载失败的降级方案
- 显示友好的错误提示信息
- 支持重试机制
