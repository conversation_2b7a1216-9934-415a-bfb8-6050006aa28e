# 文化要素统一接口变更文档

## 概述

本文档描述了文化要素数据库重构后的API接口变更，包括新增的统一接口和即将废弃的旧接口。前端需要根据此文档进行相应的调整。

## 变更时间线

- **当前阶段**: 新旧接口并存，旧接口标记为 `@deprecated`
- **测试阶段**: 前端逐步迁移到新接口
- **清理阶段**: 删除旧接口和相关代码

## 新增统一接口

### 重要说明：接口权限区分

- **管理端接口** (`/admin/*`): 需要管理员认证，用于后台管理系统
- **公开接口** (`/api/*`): 无需认证，用于前端门户网站
- **兼容接口** (`/openapi/*`): 无需认证，用于地图服务等

### 1. 管理端文化要素接口

**基础路径**: `/admin/cultural-element`
**权限要求**: 需要管理员认证

#### 1.1 CRUD操作

```typescript
// 创建文化要素
POST /admin/cultural-element
Body: CreateCulturalElementDTO

// 更新文化要素
PUT /admin/cultural-element/:id
Body: UpdateCulturalElementDTO

// 删除文化要素
DELETE /admin/cultural-element/:id

// 获取文化要素详情
GET /admin/cultural-element/:id

// 分页查询文化要素
GET /admin/cultural-element
Query: CulturalElementQueryDTO
```

#### 1.2 查询接口

```typescript
// 按类型查询
GET /admin/cultural-element/type/:typeDictId

// 按区域查询
GET /admin/cultural-element/region/:regionDictId

// 按古城查询
GET /admin/cultural-element/ancient-city/:ancientCityId

// 获取统计信息
GET /admin/cultural-element/statistics
```

#### 1.3 批量操作

```typescript
// 批量创建
POST /admin/cultural-element/batch
Body: BatchCreateCulturalElementDTO

// 导出Excel
POST /admin/cultural-element/export
Body: CulturalElementQueryDTO

// 下载导入模板
GET /admin/cultural-element/template

// 预览导入数据
POST /admin/cultural-element/import/preview
Files: Excel文件

// 执行导入
POST /admin/cultural-element/import/execute
Files: Excel文件
```

### 2. 公开文化要素接口

**基础路径**: `/api/cultural-element`
**权限要求**: 无需认证，用于前端门户网站

#### 2.1 基础查询

```typescript
// 获取文化要素详情
GET /api/cultural-element/:id

// 分页查询文化要素
GET /api/cultural-element
Query: CulturalElementQueryDTO

// 按类型查询
GET /api/cultural-element/type/:typeDictId

// 按区域查询
GET /api/cultural-element/region/:regionDictId

// 按古城查询
GET /api/cultural-element/ancient-city/:ancientCityId
```

#### 2.2 统计和分组

```typescript
// 获取统计信息（公共门户使用）
GET /api/cultural-element/statistics

// 按类型分组
GET /api/cultural-element/grouped-by-type

// 按区域分组
GET /api/cultural-element/grouped-by-region

// 按古城分组
GET /api/cultural-element/grouped-by-ancient-city
```

**⚠️ 重要提醒**:
- **前端门户网站**应该使用 `/api/cultural-element/statistics` 接口（无需认证）
- **后台管理系统**应该使用 `/admin/cultural-element/statistics` 接口（需要管理员认证）

#### 2.3 搜索和推荐

```typescript
// 根据编号查询
GET /api/cultural-element/code/:code

// 获取所有文化要素
GET /api/cultural-element/all

// 搜索文化要素
GET /api/cultural-element/search
Query: { keyword?: string; page?: number; pageSize?: number }

// 获取热门文化要素
GET /api/cultural-element/popular
Query: { limit?: number }

// 获取推荐文化要素
GET /api/cultural-element/recommended
Query: { limit?: number }
```

### 3. 统一地图接口

**基础路径**: `/openapi/map`

```typescript
// 获取统一的文化要素地图数据（新版本）
GET /openapi/map/cultural-elements
Query: MapDataQueryDTO

// 获取传统分类地图数据（兼容旧版本）
GET /openapi/map/data
Query: MapDataQueryDTO
```

## 数据结构变更

### 1. 统一的文化要素数据结构

```typescript
interface CulturalElementResponseDTO {
  id: number;
  name: string;
  code: string;
  typeDictId?: number;
  ancientCityId?: number;
  regionDictId: number;
  longitude?: number;
  latitude?: number;
  height?: number;
  lengthArea?: string;
  locationDescription?: string;
  constructionYear?: number;
  historicalRecords?: string;
  createdAt: Date;
  updatedAt: Date;
  
  // 关联数据
  typeName?: string;
  regionName?: string;
  ancientCityName?: string;
}
```

### 2. 查询参数结构

```typescript
interface CulturalElementQueryDTO {
  page?: number;
  pageSize?: number;
  name?: string;
  code?: string;
  typeDictId?: number;
  regionDictId?: number;
  ancientCityId?: number;
  constructionYear?: number;
}
```

### 3. 统计数据结构

```typescript
interface CulturalElementStatistics {
  total: number;
  byType: Array<{ typeName: string; count: number }>;
  byRegion: Array<{ regionName: string; count: number }>;
  byAncientCity: Array<{ cityName: string; count: number }>;
  byConstructionYear: Array<{ year: number; count: number }>;
}
```

## 即将废弃的接口

### 1. 历史要素接口 (将废弃)

```typescript
// 管理端
/admin/historical-element/*

// 公开接口
/openapi/historical-element/*
```

### 2. 山塬接口 (将废弃)

```typescript
// 管理端
/admin/mountain/*

// 公开接口
/openapi/mountain/*
```

### 3. 水系接口 (将废弃)

```typescript
// 管理端
/admin/water-system/*

// 公开接口
/openapi/water-system/*
```

## 前端迁移指南

### 1. 立即可以开始的工作

1. **新增页面开发**: 直接使用新的统一接口
2. **数据展示组件**: 适配新的数据结构
3. **搜索功能**: 使用新的搜索接口

### 2. 渐进式迁移步骤

#### 步骤1: 创建适配层
```typescript
// 创建适配器函数，将旧接口调用转换为新接口
const adaptOldToNew = {
  // 历史要素 -> 文化要素
  getHistoricalElements: (params) => 
    getCulturalElements({ ...params, typeDictId: HISTORICAL_TYPE_IDS }),
  
  // 山塬 -> 文化要素  
  getMountains: (params) => 
    getCulturalElements({ ...params, typeDictId: MOUNTAIN_TYPE_IDS }),
    
  // 水系 -> 文化要素
  getWaterSystems: (params) => 
    getCulturalElements({ ...params, typeDictId: WATER_SYSTEM_TYPE_IDS }),
};
```

#### 步骤2: 更新数据处理逻辑
```typescript
// 旧的数据结构处理
const processOldData = (mountains, waterSystems, historicalElements) => {
  // 分别处理三种类型的数据
};

// 新的统一数据结构处理
const processNewData = (culturalElements) => {
  // 统一处理所有文化要素数据
  const grouped = groupBy(culturalElements, 'typeName');
  return {
    mountains: grouped['山塬'] || [],
    waterSystems: grouped['水系'] || [],
    historicalElements: grouped['历史要素'] || [],
  };
};
```

#### 步骤3: 更新地图组件
```typescript
// 旧的地图数据获取
const getMapData = async () => {
  const [mountains, waterSystems, historicalElements] = await Promise.all([
    api.getMountains(),
    api.getWaterSystems(), 
    api.getHistoricalElements(),
  ]);
  return { mountains, waterSystems, historicalElements };
};

// 新的统一地图数据获取
const getMapData = async () => {
  const response = await api.getCulturalElementMapData();
  return response.data;
};
```

### 3. 类型映射关系

```typescript
// 类型ID映射（需要根据实际数据调整）
const TYPE_MAPPING = {
  HISTORICAL_ELEMENTS: [1, 2, 3, 4, 5], // 历史要素相关类型ID
  MOUNTAINS: [6, 7, 8], // 山塬相关类型ID  
  WATER_SYSTEMS: [9, 10, 11], // 水系相关类型ID
};
```

## 测试建议

### 1. 接口测试
- 使用新接口获取数据，验证数据结构正确性
- 对比新旧接口返回的数据，确保数据一致性
- 测试分页、搜索、筛选等功能

### 2. 功能测试
- 地图显示功能
- 数据列表展示
- 搜索和筛选功能
- 统计图表显示

### 3. 性能测试
- 对比新旧接口的响应时间
- 测试大数据量下的性能表现

## 注意事项

1. **向后兼容**: 在迁移期间，旧接口仍然可用
2. **数据一致性**: 新旧接口返回的数据应该保持一致
3. **错误处理**: 新接口使用统一的错误响应格式
4. **缓存策略**: 可能需要更新前端缓存策略
5. **权限控制**: 新接口的权限控制可能有所调整

## 响应格式变更

### 1. 统一响应格式

所有新接口都使用统一的响应格式：

```typescript
interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  code?: number;
  timestamp: string;
}
```

### 2. 分页响应格式

```typescript
interface PageResponseDTO<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}
```

## 错误处理变更

### 1. 错误响应格式

```typescript
// 新的错误响应格式
{
  "success": false,
  "message": "具体错误信息",
  "code": 400,
  "timestamp": "2025-10-08T12:00:00.000Z"
}
```

### 2. 常见错误码

- `400`: 参数验证失败
- `401`: 未授权访问
- `403`: 禁止访问
- `404`: 资源未找到
- `500`: 服务器内部错误

## 迁移时间表

| 阶段 | 时间 | 任务 | 状态 |
|------|------|------|------|
| 第一阶段 | 已完成 | 数据库结构重构 | ✅ |
| 第二阶段 | 已完成 | 核心业务逻辑实现 | ✅ |
| 第三阶段 | 已完成 | 接口层实现 | ✅ |
| 第四阶段 | 进行中 | 前端接口迁移 | 🔄 |
| 第五阶段 | 待定 | 数据迁移 | ⏳ |
| 第六阶段 | 待定 | 旧接口清理 | ⏳ |

## 联系方式

如有疑问，请联系后端开发团队进行确认和支持。

---

**文档版本**: v1.0
**更新时间**: 2025-10-08
**状态**: 待前端确认
