/*
 * @Description: 数据初始化
 * @Date: 2025-01-06 14:12:26
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-09-02 20:14:24
 */
import { App, Autoload, Init, Inject, Scope, ScopeEnum } from '@midwayjs/core';
import * as koa from '@midwayjs/koa';
import { InitService } from '../service/init.service';
import { CacheService } from '../service/cache.service';

@Autoload()
@Scope(ScopeEnum.Singleton)
export class AutoloadListener {
  @App('koa')
  app: koa.Application;

  @Inject()
  initService: InitService;

  @Inject()
  cacheService: CacheService;

  @Init()
  async init() {
    // 只在主进程初始化
    const env = this.app.getEnv();
    console.log('🌍 当前环境: ', env);

    if (
      typeof process.env.NODE_APP_INSTANCE === 'undefined' ||
      process.env.NODE_APP_INSTANCE === '0'
    ) {
      console.log('🚀 开始系统初始化...');

      try {
        // 初始化上传目录
        await this.initService.initializeUploadDirectories();

        // 初始化数据库数据
        await this.initService.initializeData();

        // 初始化Excel模板文件
        await this.initService.initializeExcelTemplates();

        // 初始化字典缓存
        await this.cacheService.loadDictionaryCache();

        console.log('✅ 系统初始化完成');
      } catch (error) {
        console.error('❌ 系统初始化失败:', error);
        throw error;
      }
    }
  }
}
