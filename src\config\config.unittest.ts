/**
 * @description 测试环境配置，在zpl环境中使用
 */
import { MidwayConfig } from '@midwayjs/core';

export default {
  // Sequelize 数据库配置
  sequelize: {
    dataSource: {
      default: {
        dialect: 'mysql',
        host: 'rm-bp1d644h1cj0k7y97vo.mysql.rds.aliyuncs.com',
        port: 3306,
        username: 'zpl',
        password: 'zpl1qaz!QAZ',
        database: 'zhi_hui_ying_jian',
        // 模型设置
        define: {
          timestamps: true,
          createdAt: 'createdAt',
          updatedAt: 'updatedAt',
          // 禁用驼峰转换
          underscored: false,
          charset: 'utf8mb4',
          collate: 'utf8mb4_bin',
        },
        // 时区设置
        timezone: '+08:00',
        // 连接池配置
        pool: {
          max: 5,
          min: 0,
          idle: 10000,
        },
        entities: ['entity'],
      },
    },
  },
  // Redis 配置
  redis: {
    clients: {
      default: {
        port: 6379,
        host: '127.0.0.1',
        password: '',
        db: 0,
      },
      dictionary: {
        port: 6379,
        host: '127.0.0.1',
        password: '',
        db: 1,
      },
    },
  },
  // 上传文件存储路径配置
  upload: {
    uploadPath: '/projects/html/zhi-hui-ying-jian-web/uploads',
    // 文件访问基础URL
    baseUrl: 'http://zhyj.xdpb.top/uploads',
  },
} as MidwayConfig;
