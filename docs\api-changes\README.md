# 文化要素统一接口变更文档

## 📋 文档概览

本目录包含了文化要素数据库重构后的所有接口变更文档，旨在帮助前端团队顺利完成接口迁移。

## 📚 文档列表

### 1. 主要文档

| 文档 | 描述 | 适用对象 |
|------|------|----------|
| [cultural-element-api-migration.md](./cultural-element-api-migration.md) | 详细的接口变更文档，包含新旧接口对比、数据结构变更等 | 前端开发者、项目经理 |
| [interface-mapping-table.md](./interface-mapping-table.md) | 新旧接口快速对照表，便于查找对应关系 | 前端开发者 |
| [frontend-migration-examples.md](./frontend-migration-examples.md) | 前端代码迁移示例，包含React、状态管理等 | 前端开发者 |
| [api-testing-guide.md](./api-testing-guide.md) | API接口测试指南，包含curl命令和测试用例 | 前端开发者、测试人员 |

### 2. 测试工具

| 工具 | 描述 | 访问方式 |
|------|------|----------|
| [在线API测试页面](../../public/test-api.html) | 浏览器中直接测试新接口的工具页面 | http://127.0.0.1:7001/test-api.html |

## ⚠️ 重要：接口权限区分

在开始使用新接口之前，请务必了解接口的权限区分：

| 接口类型 | 路径前缀 | 权限要求 | 使用场景 | 示例 |
|----------|----------|----------|----------|------|
| **公开接口** | `/api/*` | 无需认证 | 前端门户网站 | `/api/cultural-element/statistics` |
| **管理端接口** | `/admin/*` | 需要管理员认证 | 后台管理系统 | `/admin/cultural-element/statistics` |
| **兼容接口** | `/openapi/*` | 无需认证 | 地图服务等 | `/openapi/map/cultural-elements` |

**统计接口特别说明**：
- 前端门户网站请使用：`GET /api/cultural-element/statistics` （无需认证）
- 后台管理系统请使用：`GET /admin/cultural-element/statistics` （需要认证）

## 🚀 快速开始

### 1. 了解变更
1. 阅读 [接口变更文档](./cultural-element-api-migration.md) 了解整体变更
2. 查看 [接口对照表](./interface-mapping-table.md) 找到对应的新接口

### 2. 测试新接口
1. 访问 [在线测试页面](http://127.0.0.1:7001/test-api.html)
2. 或参考 [API测试指南](./api-testing-guide.md) 使用curl命令测试

### 3. 迁移代码
1. 参考 [前端迁移示例](./frontend-migration-examples.md)
2. 逐步替换旧接口调用

## 📊 接口变更概览

### 新增统一接口

```
管理端接口：
├── /admin/cultural-element          # CRUD操作
├── /admin/cultural-element/statistics    # 统计信息
├── /admin/cultural-element/batch         # 批量操作
└── /admin/cultural-element/export        # 导入导出

公开接口：
├── /api/cultural-element               # 基础查询
├── /api/cultural-element/statistics    # 统计信息
├── /api/cultural-element/search        # 搜索功能
└── /api/cultural-element/grouped-*     # 分组查询

地图接口：
└── /openapi/map/cultural-elements      # 统一地图数据
```

### 即将废弃的接口

```
历史要素：/admin/historical-element/*  →  /admin/cultural-element
山塬接口：/admin/mountain/*           →  /admin/cultural-element  
水系接口：/admin/water-system/*       →  /admin/cultural-element
```

## 🔄 迁移策略

### 阶段1: 新功能使用新接口
- 所有新开发的功能直接使用新接口
- 避免在新功能中使用旧接口

### 阶段2: 现有功能逐步迁移
- 创建适配层，逐步替换旧接口调用
- 优先迁移核心功能（地图、列表、搜索）
- 后续迁移管理功能（导入导出、批量操作）

### 阶段3: 清理旧代码
- 确认新接口稳定后，清理旧接口调用
- 删除适配层代码
- 更新相关文档

## 📋 类型映射

```typescript
// 类型ID映射关系（示例，请根据实际数据调整）
const TYPE_MAPPING = {
  // 历史要素
  HISTORICAL_ELEMENTS: [1, 2, 3, 4, 5],
  
  // 自然要素（山塬、水系等）
  NATURAL_ELEMENTS: [6, 7, 8, 9, 10, 11],
  
  // 历史人物
  HISTORICAL_FIGURES: [12, 13, 14, 15, 16]
};
```

## 🔧 开发环境

- **服务地址**: http://127.0.0.1:7001
- **测试页面**: http://127.0.0.1:7001/test-api.html
- **API前缀**: 
  - 管理端: `/admin`
  - 公开接口: `/api` 或 `/openapi`

## 📞 支持与反馈

### 遇到问题？

1. **接口问题**: 查看 [API测试指南](./api-testing-guide.md)
2. **迁移问题**: 参考 [前端迁移示例](./frontend-migration-examples.md)
3. **数据结构问题**: 查看 [接口变更文档](./cultural-element-api-migration.md)

### 反馈渠道

- 技术问题：联系后端开发团队
- 文档问题：提交文档改进建议
- 功能建议：参与需求讨论

## ✅ 检查清单

### 前端开发者检查清单

- [ ] 已阅读接口变更文档
- [ ] 已测试新接口功能
- [ ] 已了解数据结构变更
- [ ] 已制定迁移计划
- [ ] 已开始代码迁移
- [ ] 已完成功能测试
- [ ] 已更新相关文档

### 项目经理检查清单

- [ ] 已了解变更范围
- [ ] 已评估迁移工作量
- [ ] 已制定迁移时间表
- [ ] 已安排测试资源
- [ ] 已协调前后端配合
- [ ] 已准备上线计划

## 📈 进度跟踪

当前状态：**接口层实现完成，等待前端测试**

- ✅ 数据库结构重构
- ✅ 核心业务逻辑实现
- ✅ 接口层实现
- 🔄 前端接口迁移（当前阶段）
- ⏳ 数据迁移
- ⏳ 兼容性处理
- ⏳ 清理和优化

---

**最后更新**: 2025-10-08  
**文档版本**: v1.0  
**状态**: 待前端确认
