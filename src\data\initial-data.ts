/**
 * 系统初始化数据配置
 */

/**
 * 区域字典初始数据
 */
export const INITIAL_REGION_DATA = [
  {
    regionCode: 'REGION_XIAN',
    regionName: '西安',
    parentId: null,
    status: 1,
    sort: 1,
    regionDesc: '西安市及其周边区域',
  },
  {
    regionCode: 'REGION_BAISHUI',
    regionName: '白水',
    parentId: null,
    status: 1,
    sort: 2,
    regionDesc: '白水县及其周边区域',
  },
];

/**
 * 古城字典初始数据
 */
export const INITIAL_ANCIENT_CITY_DATA = [
  {
    cityCode: 'CITY_CHANGAN',
    cityName: '长安',
    parentId: null,
    status: 1,
    sort: 1,
    cityDesc: '古代长安城，现西安市',
    establishedYear: -1100, // 公元前1100年左右
    locationDesc: '关中平原中部，渭河南岸',
    longitude: 108.9398,
    latitude: 34.3416,
  },
  {
    cityCode: 'CITY_LUOYANG',
    cityName: '洛阳',
    parentId: null,
    status: 1,
    sort: 2,
    cityDesc: '古代洛阳城，河南洛阳',
    establishedYear: -1600, // 公元前1600年左右
    locationDesc: '河南省西部，洛河之阳',
    longitude: 112.454,
    latitude: 34.6197,
  },
  {
    cityCode: 'CITY_KAIFENG',
    cityName: '开封',
    parentId: null,
    status: 1,
    sort: 3,
    cityDesc: '古代开封城，河南开封',
    establishedYear: -364, // 公元前364年
    locationDesc: '河南省东部，黄河南岸',
    longitude: 114.3075,
    latitude: 34.7975,
  },
  {
    cityCode: 'CITY_BEIJING',
    cityName: '北京',
    parentId: null,
    status: 1,
    sort: 4,
    cityDesc: '古代北京城，现北京市',
    establishedYear: -1045, // 公元前1045年左右
    locationDesc: '华北平原北部，燕山南麓',
    longitude: 116.4074,
    latitude: 39.9042,
  },
  {
    cityCode: 'CITY_NANJING',
    cityName: '南京',
    parentId: null,
    status: 1,
    sort: 5,
    cityDesc: '古代南京城，江苏南京',
    establishedYear: -495, // 公元前495年
    locationDesc: '长江下游，紫金山脚下',
    longitude: 118.7969,
    latitude: 32.0603,
  },
];

/**
 * 类型字典初始数据
 * 重新设计为：历史要素、自然要素、历史人物三大类
 */
export const INITIAL_TYPE_DATA = [
  // 一级分类
  {
    typeCode: 'TYPE_HISTORICAL_ELEMENT',
    typeName: '历史要素',
    parentId: null,
    status: 1,
    sort: 1,
    typeDesc: '历史建筑、遗址等人文要素类型',
  },
  {
    typeCode: 'TYPE_NATURAL_ELEMENT',
    typeName: '自然要素',
    parentId: null,
    status: 1,
    sort: 2,
    typeDesc: '山峰、水系等自然地理要素类型',
  },
  {
    typeCode: 'TYPE_HISTORICAL_FIGURE',
    typeName: '历史人物',
    parentId: null,
    status: 1,
    sort: 3,
    typeDesc: '历史人物相关要素类型',
  },

  // 历史要素子类型
  {
    typeCode: 'TYPE_ROAD_STREET',
    typeName: '道路街巷',
    parentId: null, // 将在初始化时设置为历史要素的ID
    status: 1,
    sort: 11,
    typeDesc: '古代道路、街道、巷弄等交通要素',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_SHRINE_TEMPLE',
    typeName: '祠庙坛壝',
    parentId: null, // 将在初始化时设置为历史要素的ID
    status: 1,
    sort: 12,
    typeDesc: '祠堂、庙宇、祭坛、社稷等祭祀建筑',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_PAVILION_TOWER',
    typeName: '亭台楼阁',
    parentId: null,
    status: 1,
    sort: 13,
    typeDesc: '亭台、楼阁、观景建筑等',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_CITY_GATE',
    typeName: '城墙门楼',
    parentId: null,
    status: 1,
    sort: 14,
    typeDesc: '城墙、城门、门楼等防御建筑',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_BUDDHIST_TAOIST',
    typeName: '佛寺道观',
    parentId: null,
    status: 1,
    sort: 15,
    typeDesc: '佛教寺庙、道教观宇等宗教建筑',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_PALACE_BUILDING',
    typeName: '宫殿建筑',
    parentId: null,
    status: 1,
    sort: 16,
    typeDesc: '宫殿、官署、衙门等官方建筑',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_ACADEMY_SCHOOL',
    typeName: '学宫书院',
    parentId: null,
    status: 1,
    sort: 17,
    typeDesc: '学校、书院、文庙等教育建筑',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_BRIDGE_FERRY',
    typeName: '桥梁津渡',
    parentId: null,
    status: 1,
    sort: 18,
    typeDesc: '桥梁、渡口、津渡等交通设施',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_MARKET_SHOP',
    typeName: '街署公廨',
    parentId: null,
    status: 1,
    sort: 19,
    typeDesc: '市场、商铺、公共建筑等',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_ANCIENT_CITY',
    typeName: '故城遗址',
    parentId: null,
    status: 1,
    sort: 20,
    typeDesc: '古代城池遗址、废弃城址等',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_TOMB_MAUSOLEUM',
    typeName: '陵墓古冢',
    parentId: null,
    status: 1,
    sort: 21,
    typeDesc: '陵墓、古冢、墓葬群等',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },

  // 自然要素子类型
  {
    typeCode: 'TYPE_MOUNTAIN',
    typeName: '山塬',
    parentId: null,
    status: 1,
    sort: 31,
    typeDesc: '山峰、山塬、丘陵等地形要素',
    parentTypeCode: 'TYPE_NATURAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_WATER_SYSTEM',
    typeName: '水系',
    parentId: null,
    status: 1,
    sort: 32,
    typeDesc: '河流、湖泊、池塘等水系要素',
    parentTypeCode: 'TYPE_NATURAL_ELEMENT',
  },
];

/**
 * 关系字典初始数据
 */
export const INITIAL_RELATIONSHIP_DATA = [
  {
    relationCode: 'RELATION_LOCATION',
    relationName: '选址关联',
    parentId: null,
    status: 1,
    sort: 1,
    relationDesc: '山塬与城镇、建筑的选址依赖关系',
  },
  {
    relationCode: 'RELATION_VISUAL',
    relationName: '视线关联',
    parentId: null,
    status: 1,
    sort: 2,
    relationDesc: '不同要素间的视觉联系关系',
  },
  {
    relationCode: 'RELATION_EMOTION',
    relationName: '情感关联',
    parentId: null,
    status: 1,
    sort: 3,
    relationDesc: '人文要素与地理环境的情感纽带关系',
  },
  {
    relationCode: 'RELATION_ECOLOGY',
    relationName: '生态关联',
    parentId: null,
    status: 1,
    sort: 4,
    relationDesc: '自然要素间的生态依存关系',
  },
  {
    relationCode: 'RELATION_LANDSCAPE',
    relationName: '景观关联',
    parentId: null,
    status: 1,
    sort: 5,
    relationDesc: '要素间的景观构成与美学关系',
  },
  {
    relationCode: 'RELATION_SPATIAL',
    relationName: '空间关联',
    parentId: null,
    status: 1,
    sort: 6,
    relationDesc: '要素间的空间位置关系',
  },
  {
    relationCode: 'RELATION_HISTORICAL',
    relationName: '历史关联',
    parentId: null,
    status: 1,
    sort: 7,
    relationDesc: '要素间的历史文化关联',
  },
];

/**
 * 默认用户数据
 */
export const INITIAL_USER_DATA = {
  username: 'admin',
  password: 'admin123',
  role: 'admin',
  nickname: '系统管理员',
  isActive: true,
};

/**
 * 数据初始化配置
 */
export const INIT_CONFIG = {
  // 是否在初始化时显示详细日志
  verbose: true,
  // 是否强制重新初始化（清空现有数据）
  forceReinit: false,
  // 批量插入的批次大小
  batchSize: 100,
};
