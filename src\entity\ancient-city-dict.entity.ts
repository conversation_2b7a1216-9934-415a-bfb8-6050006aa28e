import {
  Table,
  Column,
  DataType,
  HasMany,
  BelongsTo,
  ForeignKey,
  Model,
} from 'sequelize-typescript';

export interface AncientCityDictAttributes {
  /** ID */
  id: number;
  /** 古城编码 */
  cityCode: string;
  /** 古城名称 */
  cityName: string;
  /** 父级古城ID */
  parentId?: number;
  /** 状态 */
  status: number;
  /** 排序号 */
  sort?: number;
  /** 古城描述 */
  cityDesc?: string;
  /** 建立时间（年份，支持公元前） */
  establishedYear?: number;
  /** 地理位置描述 */
  locationDesc?: string;
  /** 经度 */
  longitude?: number;
  /** 纬度 */
  latitude?: number;
}

/**
 * 古城字典表模型
 */
@Table({
  tableName: 'ancient_city_dict',
  comment: '古城字典表',
})
export class AncientCityDict
  extends Model<AncientCityDictAttributes>
  implements AncientCityDictAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    unique: true,
    comment: '古城编码（唯一）',
    field: 'city_code',
  })
  cityCode: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '古城名称',
    field: 'city_name',
  })
  cityName: string;

  @ForeignKey(() => AncientCityDict)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '父级古城ID',
    field: 'parent_id',
    onDelete: 'CASCADE',
  })
  parentId: number;

  @Column({
    type: DataType.TINYINT,
    allowNull: false,
    defaultValue: 1,
    comment: '状态（1启用，0禁用）',
  })
  status: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '排序号',
  })
  sort: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '古城描述',
    field: 'city_desc',
  })
  cityDesc: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '建立年份（支持公元前，负数表示公元前）',
    field: 'established_year',
  })
  establishedYear: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '地理位置描述',
    field: 'location_desc',
  })
  locationDesc: string;

  @Column({
    type: DataType.DECIMAL(10, 6),
    allowNull: true,
    comment: '古城中心经度',
  })
  longitude: number;

  @Column({
    type: DataType.DECIMAL(10, 6),
    allowNull: true,
    comment: '古城中心纬度',
  })
  latitude: number;

  // 自关联关系
  @BelongsTo(() => AncientCityDict, 'parentId')
  parent: AncientCityDict;

  @HasMany(() => AncientCityDict, {
    foreignKey: 'parentId',
    onDelete: 'CASCADE',
    hooks: true,
  })
  children: AncientCityDict[];
}
