import { Provide, Inject } from '@midwayjs/core';
import { ModelCtor } from 'sequelize-typescript';
import { Op } from 'sequelize';
import { Mountain } from '../entity/mountain.entity';
import { PageQueryDTO, PageResponseDTO } from '../dto/common.dto';
import { CreateMountainDTO, UpdateMountainDTO } from '../dto/entity.dto';
import { CacheService } from './cache.service';
import { PhotoService } from './photo.service';
import { BaseService } from '../common/BaseService';
import { RegionDict } from '../entity';

@Provide()
export class MountainService extends BaseService<Mountain> {
  @Inject()
  cacheService: CacheService;

  @Inject()
  photoService: PhotoService;

  constructor() {
    super('山塬');
  }

  protected getModel(): ModelCtor<Mountain> {
    return Mountain;
  }

  /**
   * 创建山塬（业务逻辑封装）
   */
  async createMountain(createDto: CreateMountainDTO): Promise<Mountain> {
    await this.validateMountainData(createDto);
    return await this.create(createDto as any);
  }

  /**
   * 更新山塬（业务逻辑封装）
   */
  async updateMountain(
    id: number,
    updateDto: UpdateMountainDTO
  ): Promise<Mountain> {
    await this.validateMountainData(updateDto, id);
    await this.update({ id }, updateDto as any);
    return (await this.findById(id)) as Mountain;
  }

  /**
   * 删除山塬（业务逻辑封装）
   * @param id 山塬ID
   * @param deletePhotos 是否同时删除关联的照片，默认为false（外键设置为SET NULL）
   */
  async deleteMountain(id: number, deletePhotos = false): Promise<void> {
    // 检查山塬是否存在
    const mountain = await this.findById(id);
    if (!mountain) {
      throw new Error('山塬不存在');
    }

    try {
      // 如果用户指定要删除照片，则先删除关联的照片记录
      if (deletePhotos) {
        await this.deleteRelatedPhotos(id);
      }

      // 删除山塬本身
      // 如果deletePhotos为false，外键会被设置为NULL（需要数据库层面配置ON DELETE SET NULL）
      await this.delete({ id });
    } catch (error) {
      throw new Error(`删除山塬失败: ${error.message}`);
    }
  }

  /**
   * 删除关联的照片记录
   * @param mountainId 山塬ID
   */
  private async deleteRelatedPhotos(mountainId: number): Promise<void> {
    try {
      // 获取关联的照片记录
      const photos = await this.photoService.findAll({
        query: { mountainId },
      });

      // 删除照片记录
      for (const photo of photos.list) {
        await this.photoService.delete({ id: photo.id });
      }
    } catch (error) {
      throw new Error(`删除关联照片失败: ${error.message}`);
    }
  }

  /**
   * 批量删除山塬
   * @param ids 山塬ID数组
   * @param deletePhotos 是否同时删除关联的照片，默认为false
   */
  async batchDeleteMountains(
    ids: number[],
    deletePhotos = false
  ): Promise<void> {
    if (!ids || ids.length === 0) {
      throw new Error('请提供要删除的山塬ID');
    }

    // 检查所有山塬是否存在
    const mountains = await this.findAll({
      query: { id: { [Op.in]: ids } },
    });

    const existingIds = mountains.list.map(m => m.id);
    const notFoundIds = ids.filter(id => !existingIds.includes(id));

    if (notFoundIds.length > 0) {
      throw new Error(`以下山塬不存在: ${notFoundIds.join(', ')}`);
    }

    try {
      // 如果用户指定要删除照片，则先删除关联的照片记录
      if (deletePhotos) {
        for (const id of ids) {
          await this.deleteRelatedPhotos(id);
        }
      }

      // 批量删除山塬
      await this.delete({ id: { [Op.in]: ids } });
    } catch (error) {
      throw new Error(`批量删除山塬失败: ${error.message}`);
    }
  }

  /**
   * 分页查询山塬列表
   */
  async findList(
    query: PageQueryDTO & { regionId?: number }
  ): Promise<PageResponseDTO<Mountain>> {
    const { page, pageSize, keyword, regionId } = query;
    const offset = (page - 1) * pageSize;

    const whereConditions: any = {};

    if (keyword) {
      whereConditions.name = { [Symbol.for('like')]: `%${keyword}%` };
    }

    if (regionId) {
      whereConditions.regionDictId = regionId;
    }

    const result = await this.findAll({
      query: whereConditions,
      offset,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: RegionDict,
          as: 'regionDict',
          attributes: ['id', 'regionName', 'regionCode'],
        },
      ],
    });

    return new PageResponseDTO(result.list, result.total || 0, page, pageSize);
  }

  /**
   * 根据编号查找山塬
   */
  async findByCode(code: string): Promise<Mountain | null> {
    const result = await this.findAll({
      query: { code } as any,
      limit: 1,
    });
    return result.list.length > 0 ? result.list[0] : null;
  }

  /**
   * 根据区域获取山塬列表
   */
  async findByRegion(regionId: number): Promise<Mountain[]> {
    // 验证regionId参数
    if (!regionId || isNaN(Number(regionId))) {
      throw new Error('无效的区域ID');
    }

    const result = await this.findAll({
      query: { regionDictId: Number(regionId) },
      include: [
        {
          model: RegionDict,
          as: 'regionDict',
          attributes: ['id', 'regionName', 'regionCode'],
        },
      ],
    });
    return result.list;
  }

  /**
   * 根据高度范围查询山塬
   */
  async findByHeightRange(
    minHeight?: number,
    maxHeight?: number
  ): Promise<Mountain[]> {
    const whereConditions: any = {};

    if (minHeight !== undefined) {
      whereConditions.height = { [Symbol.for('gte')]: minHeight };
    }

    if (maxHeight !== undefined) {
      if (whereConditions.height) {
        whereConditions.height[Symbol.for('lte')] = maxHeight;
      } else {
        whereConditions.height = { [Symbol.for('lte')]: maxHeight };
      }
    }

    const result = await this.findAll({
      query: whereConditions,
      include: [
        {
          model: RegionDict,
          as: 'regionDict',
          attributes: ['id', 'regionName', 'regionCode'],
        },
      ],
    });
    return result.list;
  }

  /**
   * 获取山塬统计信息
   */
  async getStatistics(regionId?: number): Promise<{
    total: number;
    byRegion: Array<{ regionId: number; regionName: string; count: number }>;
    byHeight: Array<{ range: string; count: number }>;
  }> {
    const whereConditions: any = {};

    if (regionId && !isNaN(Number(regionId))) {
      whereConditions.regionDictId = Number(regionId);
    }

    // 获取所有山塬数据，包含关联的区域信息
    const result = await this.findAll({
      query: whereConditions,
      include: [{ model: RegionDict, as: 'regionDict' }],
    });
    const mountains = result.list;
    const total = mountains.length;

    // 按区域统计
    const regionMap = new Map<
      number,
      { regionId: number; regionName: string; count: number }
    >();

    // 按高度范围统计
    const heightRanges = [
      { range: '0-500米', min: 0, max: 500, count: 0 },
      { range: '500-1000米', min: 500, max: 1000, count: 0 },
      { range: '1000-2000米', min: 1000, max: 2000, count: 0 },
      { range: '2000-3000米', min: 2000, max: 3000, count: 0 },
      { range: '3000米以上', min: 3000, max: Infinity, count: 0 },
    ];

    mountains.forEach(mountain => {
      // 区域统计
      if (mountain.regionDict) {
        const regionId = mountain.regionDict.id;
        const regionName = mountain.regionDict.regionName;
        if (!regionMap.has(regionId)) {
          regionMap.set(regionId, { regionId, regionName, count: 0 });
        }
        regionMap.get(regionId)!.count++;
      }

      // 高度统计
      if (mountain.height !== null && mountain.height !== undefined) {
        const height = mountain.height;
        for (const range of heightRanges) {
          if (height >= range.min && height < range.max) {
            range.count++;
            break;
          }
        }
      }
    });

    const byRegion = Array.from(regionMap.values()).sort(
      (a, b) => b.count - a.count
    );
    const byHeight = heightRanges
      .filter(range => range.count > 0)
      .map(({ range, count }) => ({ range, count }))
      .sort((a, b) => b.count - a.count);

    return {
      total,
      byRegion,
      byHeight,
    };
  }

  /**
   * 批量导入山塬数据
   */
  async batchImportMountains(mountains: CreateMountainDTO[]): Promise<void> {
    // 批量验证数据
    for (const mountain of mountains) {
      await this.validateMountainData(mountain);
    }

    await this.batchCreate(mountains as any);
  }

  /**
   * 验证山塬数据
   */
  private async validateMountainData(
    data: CreateMountainDTO | UpdateMountainDTO,
    excludeId?: number
  ): Promise<void> {
    if (!data.name || data.name.trim().length === 0) {
      throw new Error('山塬名称不能为空');
    }

    if (!data.code || data.code.trim().length === 0) {
      throw new Error('山塬编号不能为空');
    }

    // 验证编号唯一性
    const existingMountain = await this.findByCode(data.code.trim());
    if (existingMountain && (!excludeId || existingMountain.id !== excludeId)) {
      throw new Error(`编号 "${data.code}" 已存在，请使用其他编号`);
    }

    if (data.longitude) {
      // 验证经纬度范围
      if (data.longitude < -180 || data.longitude > 180) {
        throw new Error('经度范围应在-180到180之间');
      }
    }

    if (data.latitude) {
      // 验证经纬度范围
      if (data.latitude < -90 || data.latitude > 90) {
        throw new Error('纬度范围应在-90到90之间');
      }
    }

    // 验证高度
    if (data.height !== undefined && data.height < 0) {
      throw new Error('山塬高度不能为负数');
    }
  }
}
