# 统一搜索接口详细说明

## 接口概述
统一搜索接口提供跨实体类型的全文搜索功能，支持在山塬、水系、历史要素中进行统一检索。

## 1. 跨类型统一搜索

### 接口地址
`GET /openapi/search`

### 功能说明
在所有实体类型中进行统一搜索，支持按类型筛选和分页查询。

### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| keyword | string | 是 | - | 搜索关键词，至少2个字符 |
| type | string | 否 | all | 搜索类型：mountain/waterSystem/historicalElement/all |
| page | number | 否 | 1 | 页码 |
| pageSize | number | 否 | 10 | 每页数量，最大100 |

### 响应示例
```json
{
  "errCode": 0,
  "data": {
    "list": [
      {
        "type": "mountain",
        "id": 1,
        "name": "华山",
        "code": "HS001",
        "regionDictId": 1,
        "typeDictId": 1,
        "longitude": 110.0896,
        "latitude": 34.4749,
        "height": 2154.9,
        "regionDict": {
          "id": 1,
          "regionName": "华阴市"
        },
        "typeDict": {
          "id": 1,
          "typeName": "名山"
        }
      },
      {
        "type": "historicalElement",
        "id": 1,
        "name": "华清池",
        "code": "HQC001",
        "regionDictId": 2,
        "typeDictId": 3,
        "constructionTime": "唐代",
        "constructionLongitude": 109.2144,
        "constructionLatitude": 34.3664,
        "regionDict": {
          "id": 2,
          "regionName": "临潼区"
        },
        "typeDict": {
          "id": 3,
          "typeName": "古建筑"
        }
      },
      {
        "type": "waterSystem",
        "id": 1,
        "name": "华胥河",
        "code": "HXH001",
        "regionDictId": 2,
        "typeDictId": 5,
        "longitude": 109.2000,
        "latitude": 34.3500,
        "lengthArea": 25.6,
        "regionDict": {
          "id": 2,
          "regionName": "临潼区"
        },
        "typeDict": {
          "id": 5,
          "typeName": "河流"
        }
      }
    ],
    "total": 3,
    "page": 1,
    "pageSize": 10,
    "summary": {
      "mountain": 1,
      "waterSystem": 1,
      "historicalElement": 1
    }
  },
  "msg": "OK"
}
```

### 字段说明

#### 公共字段
所有搜索结果都包含以下公共字段：
| 字段名 | 类型 | 说明 |
|--------|------|------|
| type | string | 实体类型：mountain/waterSystem/historicalElement |
| id | number | 实体ID |
| name | string | 实体名称 |
| code | string | 实体编码 |
| regionDictId | number | 区域字典ID |
| typeDictId | number | 类型字典ID |
| regionDict | object | 区域字典信息 |
| typeDict | object | 类型字典信息 |

#### 山塬特有字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| longitude | number | 经度 |
| latitude | number | 纬度 |
| height | number | 海拔高度（米） |

#### 水系特有字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| longitude | number | 经度 |
| latitude | number | 纬度 |
| lengthArea | number | 长度/面积 |

#### 历史要素特有字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| constructionTime | string | 建造时间 |
| constructionLongitude | number | 建造地经度 |
| constructionLatitude | number | 建造地纬度 |

#### summary 搜索摘要
| 字段名 | 类型 | 说明 |
|--------|------|------|
| mountain | number | 山塬搜索结果数量 |
| waterSystem | number | 水系搜索结果数量 |
| historicalElement | number | 历史要素搜索结果数量 |

## 2. 搜索功能特性

### 2.1 搜索范围
- **山塬搜索**：在名称、编码字段中搜索
- **水系搜索**：在名称、编码字段中搜索
- **历史要素搜索**：在名称、编码字段中搜索

### 2.2 搜索算法
- 使用模糊匹配（LIKE查询）
- 支持部分匹配和包含匹配
- 不区分大小写

### 2.3 结果排序
- 按相关性排序（名称完全匹配优先）
- 同等相关性按创建时间倒序
- 保持类型内部的一致性排序

## 3. 使用示例

### 3.1 基础搜索
```javascript
// 搜索包含"华"的所有实体
fetch('/openapi/search?keyword=华')
  .then(response => response.json())
  .then(data => {
    if (data.errCode === 0) {
      const { list, total, summary } = data.data;
      
      console.log(`共找到 ${total} 个结果:`);
      console.log(`- 山塬: ${summary.mountain}个`);
      console.log(`- 水系: ${summary.waterSystem}个`);
      console.log(`- 历史要素: ${summary.historicalElement}个`);
      
      // 显示搜索结果
      list.forEach(item => {
        console.log(`${item.type}: ${item.name} (${item.code})`);
      });
    }
  })
  .catch(error => {
    console.error('搜索失败:', error);
  });
```

### 3.2 按类型搜索
```javascript
// 只搜索山塬
fetch('/openapi/search?keyword=华&type=mountain')
  .then(response => response.json())
  .then(data => {
    if (data.errCode === 0) {
      const mountains = data.data.list;
      console.log('找到的山塬:');
      mountains.forEach(mountain => {
        console.log(`- ${mountain.name}: 海拔${mountain.height}米`);
      });
    }
  });

// 只搜索历史要素
fetch('/openapi/search?keyword=华&type=historicalElement')
  .then(response => response.json())
  .then(data => {
    if (data.errCode === 0) {
      const elements = data.data.list;
      console.log('找到的历史要素:');
      elements.forEach(element => {
        console.log(`- ${element.name}: 建于${element.constructionTime}`);
      });
    }
  });
```

### 3.3 分页搜索
```javascript
// 分页获取搜索结果
async function searchWithPagination(keyword, pageSize = 10) {
  let page = 1;
  let allResults = [];
  
  while (true) {
    const response = await fetch(
      `/openapi/search?keyword=${keyword}&page=${page}&pageSize=${pageSize}`
    );
    const data = await response.json();
    
    if (data.errCode === 0) {
      const { list, total } = data.data;
      allResults = allResults.concat(list);
      
      console.log(`第${page}页: ${list.length}个结果`);
      
      // 如果已获取所有结果，退出循环
      if (allResults.length >= total) {
        break;
      }
      
      page++;
    } else {
      console.error('搜索失败:', data.msg);
      break;
    }
  }
  
  return allResults;
}

// 使用示例
searchWithPagination('华', 5).then(results => {
  console.log(`总共找到 ${results.length} 个结果`);
});
```

### 3.4 搜索结果处理
```javascript
// 处理搜索结果并按类型分组
fetch('/openapi/search?keyword=华')
  .then(response => response.json())
  .then(data => {
    if (data.errCode === 0) {
      const results = data.data.list;
      
      // 按类型分组
      const groupedResults = {
        mountain: [],
        waterSystem: [],
        historicalElement: []
      };
      
      results.forEach(item => {
        groupedResults[item.type].push(item);
      });
      
      // 显示分组结果
      Object.keys(groupedResults).forEach(type => {
        const items = groupedResults[type];
        if (items.length > 0) {
          console.log(`\n${type}类型 (${items.length}个):`);
          items.forEach(item => {
            console.log(`- ${item.name} (${item.regionDict.regionName})`);
          });
        }
      });
    }
  });
```

### 3.5 搜索建议功能
```javascript
// 实现搜索建议功能
function createSearchSuggestion(inputElement, resultsElement) {
  let searchTimeout;
  
  inputElement.addEventListener('input', function() {
    const keyword = this.value.trim();
    
    // 清除之前的定时器
    clearTimeout(searchTimeout);
    
    if (keyword.length < 2) {
      resultsElement.innerHTML = '';
      return;
    }
    
    // 防抖处理，500ms后执行搜索
    searchTimeout = setTimeout(() => {
      fetch(`/openapi/search?keyword=${keyword}&pageSize=5`)
        .then(response => response.json())
        .then(data => {
          if (data.errCode === 0) {
            const suggestions = data.data.list.slice(0, 5);
            
            resultsElement.innerHTML = suggestions.map(item => `
              <div class="suggestion-item" data-type="${item.type}" data-id="${item.id}">
                <span class="type-badge">${getTypeName(item.type)}</span>
                <span class="name">${item.name}</span>
                <span class="region">${item.regionDict.regionName}</span>
              </div>
            `).join('');
          }
        })
        .catch(error => {
          console.error('搜索建议获取失败:', error);
        });
    }, 500);
  });
}

function getTypeName(type) {
  const typeNames = {
    mountain: '山塬',
    waterSystem: '水系',
    historicalElement: '历史要素'
  };
  return typeNames[type] || type;
}

// 使用示例
const searchInput = document.getElementById('search-input');
const searchResults = document.getElementById('search-results');
createSearchSuggestion(searchInput, searchResults);
```

## 4. 搜索优化建议

### 4.1 前端优化
- 实现搜索防抖，避免频繁请求
- 缓存热门搜索结果
- 提供搜索历史记录
- 支持搜索结果高亮显示

### 4.2 用户体验
- 提供搜索建议和自动补全
- 支持搜索结果的多种排序方式
- 提供搜索结果的筛选功能
- 显示搜索耗时和结果统计

### 4.3 性能考虑
- 关键词长度至少2个字符
- 合理设置分页大小
- 避免过于宽泛的搜索词
- 考虑实现搜索结果缓存

### 4.4 扩展功能
- 支持高级搜索（多条件组合）
- 支持地理位置范围搜索
- 支持时间范围搜索（历史要素）
- 支持相关推荐功能
