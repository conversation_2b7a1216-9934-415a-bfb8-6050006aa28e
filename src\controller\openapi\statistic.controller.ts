import { Controller, Get, Query, Inject } from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { StatisticService } from '../../service/statistic.service';
import { HistoricalElementService } from '../../service/historical-element.service';
import { StatisticQueryDTO } from '../../dto/common.dto';

/**
 * 统计数据公开接口
 */
@Controller('/openapi/statistics')
export class PublicStatisticController {
  @Inject()
  statisticService: StatisticService;

  @Inject()
  historicalElementService: HistoricalElementService;

  /**
   * 获取基础统计数据
   */
  @Get('/basic')
  @Validate()
  async getBasicStatistics(@Query() query: StatisticQueryDTO) {
    const data = await this.statisticService.getStatisticData(query);
    return data;
  }

  /**
   * 获取综合统计报告
   */
  @Get('/comprehensive')
  async getComprehensiveReport(@Query('regionId') regionId?: number) {
    // 验证regionId参数
    const validRegionId =
      regionId && !isNaN(Number(regionId)) ? Number(regionId) : undefined;
    const data = await this.statisticService.getComprehensiveReport(
      validRegionId
    );
    return data;
  }

  /**
   * 获取时间轴数据
   */
  @Get('/timeline')
  async getTimelineData(@Query('regionId') regionId?: number) {
    // 验证regionId参数
    const validRegionId =
      regionId && !isNaN(Number(regionId)) ? Number(regionId) : undefined;
    const data = await this.historicalElementService.getTimelineData(
      validRegionId
    );
    return data;
  }

  /**
   * 获取区域分布统计
   */
  @Get('/region-distribution')
  async getRegionDistribution() {
    const data = await this.statisticService.getStatisticData({});
    return data.regionStats;
  }

  /**
   * 获取数据概览
   */
  @Get('/overview')
  async getDataOverview(@Query('regionId') regionId?: number) {
    // 验证regionId参数
    const validRegionId =
      regionId && !isNaN(Number(regionId)) ? Number(regionId) : undefined;
    const data = await this.statisticService.getStatisticData({
      regionId: validRegionId,
    });
    return {
      totalCounts: data.counts,
      regionStats: data.regionStats,
      timelineData: data.timelineData,
    };
  }

  /**
   * 增长趋势（可选）
   */
  @Get('/growth-trend')
  async getGrowthTrend(@Query('period') period = 'month') {
    // 复用管理端逻辑：此处简化，返回统计服务的趋势数据或占位
    const now = Date.now();
    const days = period === 'week' ? 7 : period === 'year' ? 365 : 30;
    function gen(count: number) {
      const arr: Array<{ date: string; count: number }> = [];
      for (let i = days - 1; i >= 0; i--) {
        const d = new Date(now - i * 24 * 3600 * 1000);
        arr.push({ date: d.toISOString().split('T')[0], count: Math.floor(Math.random() * count) });
      }
      return arr;
    }
    return {
      mountains: gen(5),
      waterSystems: gen(3),
      historicalElements: gen(8),
    };
  }
}
