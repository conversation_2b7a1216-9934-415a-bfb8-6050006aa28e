# Excel模板管理API

## 概述

Excel模板管理API提供了完整的Excel模板生成、下载、管理功能，支持多种模板类型和样式主题。

## 基础信息

- **基础路径**: `/api/admin/excel-template`
- **认证方式**: Bearer Token
- **响应格式**: JSON
- **支持格式**: Excel (.xlsx)

## 模板类型

| 类型 | 标识 | 描述 |
|------|------|------|
| 历史要素 | `HISTORICAL_ELEMENT` | 历史要素数据导入模板 |
| 山塬 | `MOUNTAIN` | 山塬数据导入模板 |
| 水系 | `WATER_SYSTEM` | 水系数据导入模板 |

## API接口

### 1. 获取模板下载链接

**接口**: `GET /api/admin/excel-template/{templateType}/url`

**描述**: 获取指定类型模板的下载链接

**路径参数**:
- `templateType` (string, required): 模板类型标识

**请求示例**:
```http
GET /api/admin/excel-template/HISTORICAL_ELEMENT/url
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "url": "/public/templates/historical_element_import_template.xlsx",
    "fileName": "historical_element_import_template.xlsx",
    "fileSize": 16405,
    "generatedAt": "2025-09-03T13:30:00.000Z"
  },
  "message": "模板链接获取成功"
}
```

### 2. 直接下载模板

**接口**: `GET /api/admin/excel-template/{templateType}/download`

**描述**: 直接下载指定类型的模板文件

**路径参数**:
- `templateType` (string, required): 模板类型标识

**查询参数**:
- `theme` (string, optional): 样式主题 (`blue`|`green`|`orange`|`red`)
- `advanced` (boolean, optional): 是否使用高级生成器

**请求示例**:
```http
GET /api/admin/excel-template/HISTORICAL_ELEMENT/download?theme=blue&advanced=true
Authorization: Bearer {token}
```

**响应**: Excel文件流

**响应头**:
```
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
Content-Disposition: attachment; filename="historical_element_import_template.xlsx"
```

### 3. 批量生成模板

**接口**: `POST /api/admin/excel-template/generate-all`

**描述**: 批量生成所有类型的模板文件

**请求体**:
```json
{
  "theme": "blue",
  "advanced": true,
  "force": false
}
```

**参数说明**:
- `theme` (string, optional): 统一样式主题
- `advanced` (boolean, optional): 是否使用高级生成器
- `force` (boolean, optional): 是否强制重新生成

**响应示例**:
```json
{
  "success": true,
  "data": {
    "HISTORICAL_ELEMENT": "/public/templates/historical_element_import_template.xlsx",
    "MOUNTAIN": "/public/templates/mountain_import_template.xlsx",
    "WATER_SYSTEM": "/public/templates/water_system_import_template.xlsx"
  },
  "message": "批量生成完成",
  "meta": {
    "totalCount": 3,
    "successCount": 3,
    "failedCount": 0,
    "totalSize": 49215
  }
}
```

### 4. 获取模板统计信息

**接口**: `GET /api/admin/excel-template/stats`

**描述**: 获取模板系统的统计信息

**响应示例**:
```json
{
  "success": true,
  "data": {
    "totalTemplates": 3,
    "templateTypes": ["HISTORICAL_ELEMENT", "MOUNTAIN", "WATER_SYSTEM"],
    "cacheStatus": {
      "size": 2,
      "keys": ["buffer_HISTORICAL_ELEMENT", "buffer_MOUNTAIN"],
      "totalSize": 32810
    },
    "diskUsage": {
      "HISTORICAL_ELEMENT": 16405,
      "MOUNTAIN": 15230,
      "WATER_SYSTEM": 14580
    },
    "lastGenerated": "2025-09-03T13:30:00.000Z"
  },
  "message": "统计信息获取成功"
}
```

### 5. 清除模板缓存

**接口**: `DELETE /api/admin/excel-template/cache`

**描述**: 清除模板缓存，下次访问时重新生成

**响应示例**:
```json
{
  "success": true,
  "data": {
    "clearedCount": 3,
    "freedMemory": 32810
  },
  "message": "缓存清除成功"
}
```

### 6. 自定义模板生成

**接口**: `POST /api/admin/excel-template/custom`

**描述**: 根据自定义配置生成模板

**请求体**:
```json
{
  "config": {
    "title": "自定义模板",
    "instructions": [
      "请按照要求填写数据",
      "带*号的字段为必填项"
    ],
    "headers": [
      {
        "label": "名称",
        "key": "name",
        "required": true,
        "description": "数据名称",
        "width": 20,
        "type": "string",
        "validation": {
          "max": 100
        }
      },
      {
        "label": "数值",
        "key": "value",
        "required": false,
        "description": "数值数据",
        "width": 15,
        "type": "number",
        "validation": {
          "min": 0,
          "max": 999999
        }
      }
    ],
    "sheetName": "自定义数据",
    "styleConfig": {
      "enabled": true,
      "theme": "green",
      "showBorders": true,
      "highlightRequired": true,
      "useAdvancedGenerator": true
    }
  },
  "fileName": "custom_template.xlsx"
}
```

**响应**: Excel文件流

### 7. 验证模板配置

**接口**: `POST /api/admin/excel-template/validate`

**描述**: 验证模板配置的正确性

**请求体**: 同自定义模板生成的config部分

**响应示例**:
```json
{
  "success": true,
  "data": {
    "valid": true,
    "errors": [],
    "warnings": [
      "建议为数值字段设置验证范围"
    ],
    "fieldCount": 2,
    "requiredFieldCount": 1
  },
  "message": "配置验证完成"
}
```

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填项 |
| 401 | 未授权访问 | 检查认证token |
| 404 | 模板类型不存在 | 使用正确的模板类型标识 |
| 500 | 模板生成失败 | 检查服务器日志，可能是内存不足 |
| 503 | 服务暂时不可用 | 稍后重试或联系管理员 |

## 使用示例

### JavaScript/TypeScript

```typescript
// 获取模板下载链接
const response = await fetch('/api/admin/excel-template/HISTORICAL_ELEMENT/url', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
const result = await response.json();
console.log('下载链接:', result.data.url);

// 直接下载模板
const downloadResponse = await fetch('/api/admin/excel-template/HISTORICAL_ELEMENT/download?theme=blue', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
const blob = await downloadResponse.blob();
const url = URL.createObjectURL(blob);
const a = document.createElement('a');
a.href = url;
a.download = 'template.xlsx';
a.click();
```

### cURL

```bash
# 获取模板链接
curl -X GET "http://localhost:7001/api/admin/excel-template/HISTORICAL_ELEMENT/url" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 下载模板文件
curl -X GET "http://localhost:7001/api/admin/excel-template/HISTORICAL_ELEMENT/download" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -o template.xlsx

# 批量生成模板
curl -X POST "http://localhost:7001/api/admin/excel-template/generate-all" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"theme": "blue", "advanced": true}'
```

## 注意事项

1. **文件大小**: 启用高级样式会增加文件大小
2. **生成时间**: 高级模板生成需要更多时间
3. **缓存机制**: 相同配置的模板会被缓存，提高响应速度
4. **内存使用**: 大量并发请求可能导致内存压力
5. **文件存储**: 生成的模板文件存储在 `public/templates` 目录

## 更新日志

- **v1.0.0**: 基础模板生成功能
- **v1.1.0**: 新增样式主题支持
- **v1.2.0**: 新增高级生成器和数据验证
- **v1.3.0**: 新增缓存机制和批量操作
