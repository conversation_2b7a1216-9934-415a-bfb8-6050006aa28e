import {
  Table,
  Column,
  DataType,
  BelongsTo,
  HasMany,
  ForeignKey,
  Model,
} from 'sequelize-typescript';
import { RegionDict } from './region-dict.entity';
import { TypeDict } from './type-dict.entity';
import { AncientCityDict } from './ancient-city-dict.entity';
import { Photo } from './photo.entity';

export interface CulturalElementAttributes {
  /** ID */
  id: number;
  /** 名称 */
  name: string;
  /** 编号 */
  code: string;
  /** 所属类型ID */
  typeDictId: number;
  /** 所属古城ID */
  ancientCityId: number;
  /** 所属区域ID */
  regionDictId: number;

  // 统一的位置信息
  /** 经度 */
  longitude?: number;
  /** 纬度 */
  latitude?: number;

  // 原山塬特有字段
  /** 高度（米） */
  height?: number;

  // 原水系特有字段
  /** 长度/面积 */
  lengthArea?: string;

  // 原历史要素特有字段
  /** 位置描述 */
  locationDescription?: string;
  /** 建造年份（支持公元前，负数表示公元前） */
  constructionYear?: number;

  // 通用字段
  /** 历史记载 */
  historicalRecords?: string;
  /** 描述信息 */
  description?: string;
  /** 状态 */
  status?: number;
  /** 排序号 */
  sort?: number;
}

/**
 * 文化要素表模型
 */
@Table({
  tableName: 'cultural_element',
  comment: '文化要素表',
})
export class CulturalElement
  extends Model<CulturalElementAttributes>
  implements CulturalElementAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '名称',
  })
  name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '编号',
  })
  code: string;

  @ForeignKey(() => TypeDict)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '所属类型ID',
    field: 'type_dict_id',
  })
  typeDictId: number;

  @ForeignKey(() => AncientCityDict)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '所属古城ID',
    field: 'ancient_city_id',
  })
  ancientCityId: number;

  @ForeignKey(() => RegionDict)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '所属区域ID',
    field: 'region_dict_id',
  })
  regionDictId: number;

  @Column({
    type: DataType.DECIMAL(10, 6),
    allowNull: true,
    comment: '经度',
  })
  longitude: number;

  @Column({
    type: DataType.DECIMAL(10, 6),
    allowNull: true,
    comment: '纬度',
  })
  latitude: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '高度（米）',
  })
  height: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '长度/面积',
    field: 'length_area',
  })
  lengthArea: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '位置描述',
    field: 'location_description',
  })
  locationDescription: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '建造年份（支持公元前，负数表示公元前）',
    field: 'construction_year',
  })
  constructionYear: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '历史记载',
    field: 'historical_records',
  })
  historicalRecords: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '描述信息',
  })
  description: string;

  @Column({
    type: DataType.TINYINT,
    allowNull: false,
    defaultValue: 1,
    comment: '状态（1启用，0禁用）',
  })
  status: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '排序号',
  })
  sort: number;

  // 关联关系
  @BelongsTo(() => TypeDict, 'typeDictId')
  typeDict: TypeDict;

  @BelongsTo(() => AncientCityDict, 'ancientCityId')
  ancientCity: AncientCityDict;

  @BelongsTo(() => RegionDict, 'regionDictId')
  regionDict: RegionDict;

  @HasMany(() => Photo, 'culturalElementId')
  photos: Photo[];
}
