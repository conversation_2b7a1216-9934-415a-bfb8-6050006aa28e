import { Controller, Get, Inject, Query, Param } from '@midwayjs/core';
import { MountainService } from '../../service/mountain.service';
import { PhotoService } from '../../service/photo.service';
import { PageQueryDTO } from '../../dto/common.dto';

/**
 * 山塬公开接口控制器
 */
@Controller('/openapi/mountain')
export class PublicMountainController {
  @Inject()
  mountainService: MountainService;

  @Inject()
  photoService: PhotoService;

  constructor() {
    console.log('🏔️ PublicMountainController 已加载');
  }

  /**
   * 获取所有山塬（不分页）
   */
  @Get('/all')
  async getAll() {
    console.log('🏔️ [公开] 获取所有山塬');

    try {
      const result = await this.mountainService.findAll({
        query: {}, // 查询所有
        order: [['code', 'ASC']], // 按编号排序
      });

      // 只返回必要字段
      const simplifiedData = result.list.map(item => ({
        id: item.id,
        name: item.name,
        code: item.code,
      }));

      console.log('🏔️ [公开] 所有山塬获取成功:', simplifiedData.length);
      return simplifiedData;
    } catch (error) {
      console.error('🏔️ [公开] 所有山塬获取失败:', error);
      throw error;
    }
  }

  /**
   * 列表（分页筛选）
   * GET /openapi/mountain/list
   */
  @Get('/list')
  async getList(@Query() query: PageQueryDTO & { typeId?: number }) {
    const data = await this.mountainService.findList(query);
    return data;
  }

  /**
   * 详情
   * GET /openapi/mountain/{id}
   */
  @Get('/:id')
  async getDetail(@Param('id') id: number) {
    if (!id || isNaN(Number(id))) {
      throw new Error('无效的ID');
    }
    const data = await this.mountainService.findById(Number(id));
    return data;
  }

  /**
   * 详情-照片
   * GET /openapi/mountain/{id}/photos
   */
  @Get('/:id/photos')
  async getPhotos(@Param('id') id: number) {
    if (!id || isNaN(Number(id))) {
      throw new Error('无效的ID');
    }
    const photos = await this.photoService.findPhotosByEntity('mountain', Number(id));
    return photos.map(p => ({ id: p.id, name: p.name, url: p.url }));
  }
}
