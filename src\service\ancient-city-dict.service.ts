import { Provide, Inject } from '@midwayjs/core';
import { AncientCityDict } from '../entity/ancient-city-dict.entity';
import { CacheService } from './cache.service';
import {
  CreateAncientCityDictDTO,
  UpdateAncientCityDictDTO,
  AncientCityDictQueryDTO,
  AncientCityDictResponseDTO,
} from '../dto/dictionary.dto';
import { PageResponseDTO } from '../dto/common.dto';
import { buildTree } from '../utils/tree.util';

@Provide()
export class AncientCityDictService {
  @Inject()
  cacheService: CacheService;

  /**
   * 创建古城字典
   */
  async createAncientCityDict(
    createDto: CreateAncientCityDictDTO
  ): Promise<AncientCityDictResponseDTO> {
    // 验证数据
    await this.validateAncientCityDictData(createDto);

    // 检查编码唯一性
    await this.checkCityCodeUnique(createDto.cityCode);

    const ancientCityDict = await AncientCityDict.create(createDto as any);

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();

    return new AncientCityDictResponseDTO(ancientCityDict.toJSON());
  }

  /**
   * 更新古城字典
   */
  async updateAncientCityDict(
    id: number,
    updateDto: UpdateAncientCityDictDTO
  ): Promise<AncientCityDictResponseDTO> {
    // 验证数据
    await this.validateAncientCityDictData(updateDto, id);

    // 检查编码唯一性
    if (updateDto.cityCode) {
      await this.checkCityCodeUnique(updateDto.cityCode, id);
    }

    await AncientCityDict.update(updateDto as any, { where: { id } });

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();

    const updated = await AncientCityDict.findByPk(id);
    return new AncientCityDictResponseDTO(updated.toJSON());
  }

  /**
   * 删除古城字典
   */
  async deleteAncientCityDict(id: number): Promise<void> {
    const ancientCityDict = await AncientCityDict.findByPk(id);
    if (!ancientCityDict) {
      throw new Error('古城字典不存在');
    }

    // 检查是否有子级
    const childCount = await AncientCityDict.count({ where: { parentId: id } });
    if (childCount > 0) {
      throw new Error('存在子级古城，无法删除');
    }

    await ancientCityDict.destroy();

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();
  }

  /**
   * 获取古城字典详情
   */
  async getAncientCityDictById(
    id: number
  ): Promise<AncientCityDictResponseDTO> {
    const ancientCityDict = await AncientCityDict.findByPk(id, {
      include: [
        {
          model: AncientCityDict,
          as: 'parent',
          attributes: ['id', 'cityName', 'cityCode'],
        },
      ],
    });

    if (!ancientCityDict) {
      throw new Error('古城字典不存在');
    }

    return new AncientCityDictResponseDTO(ancientCityDict.toJSON());
  }

  /**
   * 获取古城字典列表（平铺）
   */
  async getAncientCityDictList(): Promise<AncientCityDictResponseDTO[]> {
    const ancientCityDicts = await AncientCityDict.findAll({
      where: { status: 1 } as any,
      order: [
        ['sort', 'ASC'],
        ['id', 'ASC'],
      ],
      include: [
        {
          model: AncientCityDict,
          as: 'parent',
          attributes: ['id', 'cityName', 'cityCode'],
        },
      ],
    });

    return ancientCityDicts.map(
      dict => new AncientCityDictResponseDTO(dict.toJSON())
    );
  }

  /**
   * 分页查询古城字典
   */
  async getAncientCityDictPage(
    query: AncientCityDictQueryDTO
  ): Promise<PageResponseDTO<AncientCityDictResponseDTO>> {
    const { page = 1, pageSize = 10, keyword, parentId, status } = query;
    const offset = (page - 1) * pageSize;

    const whereCondition: any = {};

    if (keyword) {
      whereCondition.cityName = { [Symbol.for('like')]: `%${keyword}%` };
    }

    if (parentId !== undefined) {
      whereCondition.parentId = parentId;
    }

    if (status !== undefined) {
      whereCondition.status = status;
    }

    const { count, rows } = await AncientCityDict.findAndCountAll({
      where: whereCondition,
      limit: pageSize,
      offset,
      order: [
        ['sort', 'ASC'],
        ['id', 'ASC'],
      ],
      include: [
        {
          model: AncientCityDict,
          as: 'parent',
          attributes: ['id', 'cityName', 'cityCode'],
        },
      ],
    });

    const list = rows.map(row => new AncientCityDictResponseDTO(row.toJSON()));

    return new PageResponseDTO(list, count, page, pageSize);
  }

  /**
   * 获取古城字典树形结构
   */
  async getAncientCityDictTree(): Promise<AncientCityDictResponseDTO[]> {
    const ancientCityDicts = await this.getAncientCityDictList();
    return buildTree(ancientCityDicts);
  }

  /**
   * 验证古城字典数据
   */
  private async validateAncientCityDictData(
    data: CreateAncientCityDictDTO | UpdateAncientCityDictDTO,
    excludeId?: number
  ): Promise<void> {
    // 验证父级是否存在
    if (data.parentId) {
      const parent = await AncientCityDict.findByPk(data.parentId);
      if (!parent) {
        throw new Error('父级古城不存在');
      }

      // 防止循环引用
      if (excludeId && data.parentId === excludeId) {
        throw new Error('不能将自己设置为父级');
      }
    }
  }

  /**
   * 检查古城编码唯一性
   */
  private async checkCityCodeUnique(
    cityCode: string,
    excludeId?: number
  ): Promise<void> {
    const whereCondition: any = { cityCode };
    if (excludeId) {
      whereCondition.id = { [Symbol.for('ne')]: excludeId };
    }

    const existing = await AncientCityDict.findOne({ where: whereCondition });
    if (existing) {
      throw new Error('古城编码已存在');
    }
  }
}
