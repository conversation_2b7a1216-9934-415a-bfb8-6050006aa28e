<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文化要素API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .input-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input, select {
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: 200px;
        }
        .flex {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        .flex > div {
            flex: 1;
            min-width: 300px;
        }
    </style>
</head>
<body>
    <h1>文化要素API测试页面</h1>

    <div class="container" style="background: #fff3cd; border: 1px solid #ffeaa7;">
        <h2>⚠️ 重要：接口权限说明</h2>
        <p><strong>前端门户网站</strong>请使用公开接口（无需认证）：</p>
        <ul>
            <li>统计数据：<code>GET /api/cultural-element/statistics</code></li>
            <li>查询数据：<code>GET /api/cultural-element</code></li>
        </ul>
        <p><strong>后台管理系统</strong>请使用管理端接口（需要认证）：</p>
        <ul>
            <li>统计数据：<code>GET /admin/cultural-element/statistics</code></li>
            <li>管理数据：<code>GET /admin/cultural-element</code></li>
        </ul>
    </div>

    <div class="container">
        <h2>基础配置</h2>
        <div class="input-group">
            <label>服务地址:</label>
            <input type="text" id="baseUrl" value="http://127.0.0.1:7001">
        </div>
        <div class="input-group">
            <label>认证Token:</label>
            <input type="text" id="authToken" placeholder="Bearer token (管理端接口需要)">
        </div>
    </div>

    <div class="flex">
        <div class="container">
            <h2>公开接口测试</h2>
            
            <h3>基础查询</h3>
            <button onclick="testGetList()">获取文化要素列表</button>
            <button onclick="testGetDetail()">获取详情 (ID=1)</button>
            <button onclick="testGetStatistics()">获取统计信息</button>
            
            <h3>筛选查询</h3>
            <div class="input-group">
                <label>类型ID:</label>
                <input type="text" id="typeFilter" placeholder="例如: 1,2,3">
            </div>
            <div class="input-group">
                <label>区域ID:</label>
                <input type="number" id="regionFilter" placeholder="例如: 1">
            </div>
            <div class="input-group">
                <label>古城ID:</label>
                <input type="number" id="cityFilter" placeholder="例如: 1">
            </div>
            <button onclick="testFilteredList()">筛选查询</button>
            
            <h3>搜索功能</h3>
            <div class="input-group">
                <label>关键词:</label>
                <input type="text" id="searchKeyword" placeholder="搜索关键词">
            </div>
            <button onclick="testSearch()">关键词搜索</button>
            <button onclick="testPopular()">热门文化要素</button>
            <button onclick="testRecommended()">推荐文化要素</button>
            
            <h3>分组查询</h3>
            <button onclick="testGroupByType()">按类型分组</button>
            <button onclick="testGroupByRegion()">按区域分组</button>
            <button onclick="testGroupByCity()">按古城分组</button>
        </div>

        <div class="container">
            <h2>地图接口测试</h2>
            <button onclick="testMapData()">获取地图数据</button>
            <button onclick="testMapDetail()">获取地图详情</button>
            <button onclick="testMapStatistics()">获取地图统计</button>
            
            <h2>管理端接口测试</h2>
            <p style="color: #666; font-size: 14px;">注意: 管理端接口需要认证token</p>
            
            <h3>CRUD操作</h3>
            <button onclick="testAdminList()">管理端列表</button>
            <button onclick="testAdminDetail()">管理端详情</button>
            <button onclick="testAdminStatistics()">管理端统计</button>
            
            <h3>创建测试数据</h3>
            <div class="input-group">
                <label>名称:</label>
                <input type="text" id="createName" value="测试文化要素">
            </div>
            <div class="input-group">
                <label>编号:</label>
                <input type="text" id="createCode" value="TEST001">
            </div>
            <div class="input-group">
                <label>类型ID:</label>
                <input type="number" id="createType" value="1">
            </div>
            <button onclick="testCreate()">创建文化要素</button>
            
            <h3>批量操作</h3>
            <button onclick="testBatchCreate()">批量创建</button>
            <button onclick="testExport()">导出Excel</button>
            <button onclick="testTemplate()">下载模板</button>
        </div>
    </div>

    <div class="container">
        <h2>测试结果</h2>
        <div id="result" class="result">点击上方按钮开始测试...</div>
    </div>

    <script>
        const resultDiv = document.getElementById('result');
        
        function getBaseUrl() {
            return document.getElementById('baseUrl').value;
        }
        
        function getAuthHeaders() {
            const token = document.getElementById('authToken').value;
            const headers = {
                'Content-Type': 'application/json'
            };
            if (token) {
                headers['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
            }
            return headers;
        }
        
        function showResult(title, data, isError = false) {
            const timestamp = new Date().toLocaleString();
            const className = isError ? 'error' : 'success';
            resultDiv.className = `result ${className}`;
            resultDiv.textContent = `[${timestamp}] ${title}\n\n${JSON.stringify(data, null, 2)}`;
        }
        
        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: getAuthHeaders(),
                    ...options
                });
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        // 公开接口测试
        async function testGetList() {
            const url = `${getBaseUrl()}/api/cultural-element`;
            const result = await apiCall(url);
            showResult('获取文化要素列表', result, !result.success);
        }
        
        async function testGetDetail() {
            const url = `${getBaseUrl()}/api/cultural-element/1`;
            const result = await apiCall(url);
            showResult('获取文化要素详情', result, !result.success);
        }
        
        async function testGetStatistics() {
            const url = `${getBaseUrl()}/api/cultural-element/statistics`;
            const result = await apiCall(url);
            showResult('获取统计信息', result, !result.success);
        }
        
        async function testFilteredList() {
            const typeIds = document.getElementById('typeFilter').value;
            const regionId = document.getElementById('regionFilter').value;
            const cityId = document.getElementById('cityFilter').value;
            
            const params = new URLSearchParams();
            if (typeIds) params.append('typeDictId', typeIds);
            if (regionId) params.append('regionDictId', regionId);
            if (cityId) params.append('ancientCityId', cityId);
            
            const url = `${getBaseUrl()}/api/cultural-element?${params.toString()}`;
            const result = await apiCall(url);
            showResult('筛选查询结果', result, !result.success);
        }
        
        async function testSearch() {
            const keyword = document.getElementById('searchKeyword').value;
            const url = `${getBaseUrl()}/api/cultural-element/search?keyword=${encodeURIComponent(keyword)}`;
            const result = await apiCall(url);
            showResult('搜索结果', result, !result.success);
        }
        
        async function testPopular() {
            const url = `${getBaseUrl()}/api/cultural-element/popular?limit=5`;
            const result = await apiCall(url);
            showResult('热门文化要素', result, !result.success);
        }
        
        async function testRecommended() {
            const url = `${getBaseUrl()}/api/cultural-element/recommended?limit=5`;
            const result = await apiCall(url);
            showResult('推荐文化要素', result, !result.success);
        }
        
        async function testGroupByType() {
            const url = `${getBaseUrl()}/api/cultural-element/grouped-by-type`;
            const result = await apiCall(url);
            showResult('按类型分组', result, !result.success);
        }
        
        async function testGroupByRegion() {
            const url = `${getBaseUrl()}/api/cultural-element/grouped-by-region`;
            const result = await apiCall(url);
            showResult('按区域分组', result, !result.success);
        }
        
        async function testGroupByCity() {
            const url = `${getBaseUrl()}/api/cultural-element/grouped-by-ancient-city`;
            const result = await apiCall(url);
            showResult('按古城分组', result, !result.success);
        }
        
        // 地图接口测试
        async function testMapData() {
            const url = `${getBaseUrl()}/openapi/map/cultural-elements`;
            const result = await apiCall(url);
            showResult('地图数据', result, !result.success);
        }
        
        async function testMapDetail() {
            const url = `${getBaseUrl()}/openapi/map/detail?type=cultural-element&id=1`;
            const result = await apiCall(url);
            showResult('地图详情', result, !result.success);
        }
        
        async function testMapStatistics() {
            const url = `${getBaseUrl()}/openapi/map/statistics`;
            const result = await apiCall(url);
            showResult('地图统计', result, !result.success);
        }
        
        // 管理端接口测试
        async function testAdminList() {
            const url = `${getBaseUrl()}/admin/cultural-element`;
            const result = await apiCall(url);
            showResult('管理端列表', result, !result.success);
        }
        
        async function testAdminDetail() {
            const url = `${getBaseUrl()}/admin/cultural-element/1`;
            const result = await apiCall(url);
            showResult('管理端详情', result, !result.success);
        }
        
        async function testAdminStatistics() {
            const url = `${getBaseUrl()}/admin/cultural-element/statistics`;
            const result = await apiCall(url);
            showResult('管理端统计', result, !result.success);
        }
        
        async function testCreate() {
            const name = document.getElementById('createName').value;
            const code = document.getElementById('createCode').value;
            const typeDictId = parseInt(document.getElementById('createType').value);
            
            const url = `${getBaseUrl()}/admin/cultural-element`;
            const result = await apiCall(url, {
                method: 'POST',
                body: JSON.stringify({
                    name,
                    code,
                    typeDictId,
                    regionDictId: 1,
                    ancientCityId: 1,
                    longitude: 108.9398,
                    latitude: 34.3416,
                    locationDescription: '测试位置'
                })
            });
            showResult('创建文化要素', result, !result.success);
        }
        
        async function testBatchCreate() {
            const url = `${getBaseUrl()}/admin/cultural-element/batch`;
            const result = await apiCall(url, {
                method: 'POST',
                body: JSON.stringify({
                    elements: [
                        {
                            name: '批量测试1',
                            code: 'BATCH001',
                            typeDictId: 1,
                            regionDictId: 1,
                            ancientCityId: 1
                        },
                        {
                            name: '批量测试2',
                            code: 'BATCH002',
                            typeDictId: 2,
                            regionDictId: 1,
                            ancientCityId: 1
                        }
                    ]
                })
            });
            showResult('批量创建', result, !result.success);
        }
        
        async function testExport() {
            const url = `${getBaseUrl()}/admin/cultural-element/export`;
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        typeDictId: [1, 2, 3]
                    })
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const downloadUrl = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = downloadUrl;
                    a.download = 'cultural-elements.xlsx';
                    a.click();
                    showResult('导出Excel', { message: '文件下载已开始' });
                } else {
                    const error = await response.json();
                    showResult('导出Excel', error, true);
                }
            } catch (error) {
                showResult('导出Excel', { error: error.message }, true);
            }
        }
        
        async function testTemplate() {
            const url = `${getBaseUrl()}/admin/cultural-element/template`;
            try {
                const response = await fetch(url, {
                    headers: getAuthHeaders()
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const downloadUrl = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = downloadUrl;
                    a.download = 'template.xlsx';
                    a.click();
                    showResult('下载模板', { message: '模板下载已开始' });
                } else {
                    const error = await response.json();
                    showResult('下载模板', error, true);
                }
            } catch (error) {
                showResult('下载模板', { error: error.message }, true);
            }
        }
    </script>
</body>
</html>
