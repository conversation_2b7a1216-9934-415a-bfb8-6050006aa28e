import { Provide } from '@midwayjs/core';
import { join } from 'path';
import { promises as fs } from 'fs';
import {
  XlsxUtils,
  ExcelParseResult,
  ExcelValidationError,
} from '../common/xlsxUtils';
import type { ExcelTemplateConfig } from '../config/excel-configs';

/**
 * 通用Excel服务基类
 * 提供模板生成、文件解析等通用功能
 * 业务特定的逻辑由子类实现
 */
@Provide()
export class BaseExcelService {
  /**
   * 生成Excel模板
   * @param config 模板配置
   * @returns Excel文件Buffer
   */
  async generateTemplate(config: ExcelTemplateConfig): Promise<Buffer> {
    return XlsxUtils.generateTemplate(config);
  }

  /**
   * 解析Excel文件
   * @param filePath 文件路径
   * @param config 模板配置
   * @param validator 数据验证函数（可选）
   * @returns 解析结果
   */
  async parseExcel<T = any>(
    filePath: string,
    config: ExcelTemplateConfig,
    validator?: (
      data: any,
      rowIndex: number
    ) => ExcelValidationError[] | Promise<ExcelValidationError[]>
  ): Promise<ExcelParseResult<T>> {
    return XlsxUtils.parseExcel<T>(filePath, config, validator);
  }

  /**
   * 生成并保存模板文件到public目录
   * @param config 模板配置
   * @param fileName 文件名
   * @returns 文件URL路径
   */
  async generateTemplateFile(
    config: ExcelTemplateConfig,
    fileName: string
  ): Promise<string> {
    const buffer = await this.generateTemplate(config);
    const publicDir = join(process.cwd(), 'public', 'templates');

    // 确保public/templates目录存在
    try {
      await fs.access(publicDir);
    } catch {
      await fs.mkdir(publicDir, { recursive: true });
    }

    const filePath = join(publicDir, fileName);
    await fs.writeFile(filePath, buffer);
    return `/public/templates/${fileName}`;
  }

  /**
   * 检查模板文件是否存在，不存在则生成
   * @param config 模板配置
   * @param fileName 文件名
   * @returns 文件URL路径
   */
  async ensureTemplateExists(
    config: ExcelTemplateConfig,
    fileName: string
  ): Promise<string> {
    const publicDir = join(process.cwd(), 'public', 'templates');
    const filePath = join(publicDir, fileName);

    try {
      await fs.access(filePath);
      return `/public/templates/${fileName}`;
    } catch {
      // 文件不存在，生成新的
      return await this.generateTemplateFile(config, fileName);
    }
  }

  /**
   * 通用数字解析方法
   * @param value 待解析的值
   * @returns 解析后的数字或null
   */
  protected parseNumber(value: any): number | null {
    if (value === null || value === undefined || value === '') {
      return null;
    }
    const num = Number(value);
    return isNaN(num) ? null : num;
  }

  /**
   * 通用日期解析方法
   * @param value 待解析的值
   * @returns 解析后的日期或null
   */
  protected parseDate(value: any): Date | null {
    if (!value) return null;

    const dateStr = value.toString().trim();

    // 支持多种日期格式
    const formats = [
      /^\d{4}-\d{1,2}-\d{1,2}$/, // YYYY-MM-DD
      /^\d{4}\/\d{1,2}\/\d{1,2}$/, // YYYY/MM/DD
      /^\d{4}\.\d{1,2}\.\d{1,2}$/, // YYYY.MM.DD
    ];

    let isValidFormat = false;
    for (const format of formats) {
      if (format.test(dateStr)) {
        isValidFormat = true;
        break;
      }
    }

    if (!isValidFormat) {
      return null;
    }

    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date;
  }

  /**
   * 通用字符串处理方法
   * @param value 待处理的值
   * @returns 处理后的字符串
   */
  protected parseString(value: any): string {
    if (value === null || value === undefined) {
      return '';
    }
    return value.toString().trim();
  }

  /**
   * 通用整数解析方法
   * @param value 待解析的值
   * @returns 解析后的整数或null
   */
  protected parseInteger(value: any): number | null {
    if (value === null || value === undefined || value === '') {
      return null;
    }

    const numStr = value.toString().trim();

    // 支持负数（公元前年份）
    const num = parseInt(numStr, 10);

    if (isNaN(num)) {
      return null;
    }

    return num;
  }

  /**
   * 验证基础字段格式的通用方法
   * @param data 数据对象
   * @param config 模板配置
   * @param rowIndex 行号
   * @returns 验证错误数组
   */
  protected validateBasicFields(
    data: any,
    config: ExcelTemplateConfig,
    rowIndex: number
  ): ExcelValidationError[] {
    const errors: ExcelValidationError[] = [];

    config.headers.forEach(header => {
      const value = data[header.key];

      // 检查必填字段
      if (header.required && (!value || value.toString().trim() === '')) {
        errors.push({
          row: rowIndex,
          field: header.label,
          value: value,
          message: `${header.label}不能为空`,
        });
      }
    });

    return errors;
  }

  /**
   * 创建带重复检查的验证器
   * @param uniqueFields 需要检查唯一性的字段
   * @param customValidator 自定义验证函数
   * @returns 验证器函数
   */
  protected createValidatorWithDuplicateCheck(
    uniqueFields: string[],
    customValidator?: (
      data: any,
      rowIndex: number
    ) => ExcelValidationError[] | Promise<ExcelValidationError[]>
  ) {
    const fieldSets = uniqueFields.reduce((acc, field) => {
      acc[field] = new Set<string>();
      return acc;
    }, {} as Record<string, Set<string>>);

    return async (
      data: any,
      rowIndex: number
    ): Promise<ExcelValidationError[]> => {
      const errors: ExcelValidationError[] = [];

      // 执行自定义验证
      if (customValidator) {
        const customErrors = await customValidator(data, rowIndex);
        errors.push(...customErrors);
      }

      // 检查文件内重复
      uniqueFields.forEach(field => {
        const value = data[field];
        if (value && value.toString().trim() !== '') {
          const trimmedValue = value.toString().trim();
          if (fieldSets[field].has(trimmedValue)) {
            errors.push({
              row: rowIndex,
              field: field,
              value: trimmedValue,
              message: `${field}在导入文件中重复`,
            });
          } else {
            fieldSets[field].add(trimmedValue);
          }
        }
      });

      return errors;
    };
  }

  /**
   * 批量数据转换的通用方法
   * @param rawData 原始数据数组
   * @param converter 数据转换函数
   * @returns 转换后的数据数组
   */
  protected convertBatchData<T, R>(
    rawData: T[],
    converter: (item: T) => R
  ): R[] {
    return rawData.map(converter);
  }
}
