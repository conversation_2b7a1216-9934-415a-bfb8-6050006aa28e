import { Controller, Get, Query } from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
// import { MapService } from '../../service/map.service';
import { MapDataQueryDTO, DetailQueryDTO } from '../../dto/common.dto';

/**
 * 地图数据公开接口
 */
@Controller('/openapi/map')
export class PublicMapController {
  // @Inject()
  // mapService: MapService;

  /**
   * 获取统一的文化要素地图数据（新版本）
   */
  @Get('/cultural-elements')
  @Validate()
  async getCulturalElementMapData(@Query() query: MapDataQueryDTO) {
    // 暂时返回测试数据
    return {
      total: 0,
      data: [],
      message: '统一文化要素地图数据接口（开发中）',
    };
  }

  /**
   * 获取地图数据（兼容旧版本）
   */
  @Get('/data')
  async getMapData(@Query() query: any) {
    // 直接返回简单的测试数据，避免循环引用问题
    return {
      mountains: [],
      waterSystems: [],
      historicalElements: [],
      message: '地图数据接口正常工作',
    };
  }

  /**
   * 获取实体详情数据
   */
  @Get('/detail')
  @Validate()
  async getDetail(@Query() query: DetailQueryDTO) {
    // 暂时返回测试数据
    return {
      id: query.id,
      type: query.type,
      name: `测试${query.type}`,
      photos: [],
      relationships: [],
      message: '详情数据接口（开发中）',
    };
  }

  /**
   * 获取地图统计数据
   */
  @Get('/statistics')
  async getMapStatistics(@Query('regionId') regionId?: number) {
    // 暂时返回测试数据
    return {
      mountains: { total: 0 },
      waterSystems: { total: 0 },
      historicalElements: { total: 0 },
      total: {
        mountain: 0,
        waterSystem: 0,
        historicalElement: 0,
      },
      message: '地图统计数据接口（开发中）',
    };
  }
}
