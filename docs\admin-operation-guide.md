# 智慧营建系统管理员操作手册

## 系统概述

智慧营建系统管理端是专为系统管理员设计的后台管理平台，提供完整的数据管理、用户管理、系统维护等功能。管理员可以通过此平台对关中地区的地理文化数据进行全面的管理和维护。

## 登录与权限

### 系统登录
1. 访问管理端登录页面
2. 输入管理员用户名和密码
3. 系统验证通过后获得访问令牌
4. 令牌有效期为7天，过期需重新登录

**默认管理员账户**:
- 用户名: `admin`
- 密码: `admin123`

**登录API**:
```
POST /api/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

### 权限说明
- **超级管理员**: 拥有所有功能的完整权限
- **数据管理员**: 拥有数据增删改查权限
- **只读管理员**: 仅拥有数据查看权限

## 核心功能模块

### 1. 数据管理

#### 1.1 山塬数据管理
**功能路径**: `/admin/mountain`

**主要操作**:
- **新增山塬**: 创建新的山塬记录
- **编辑山塬**: 修改现有山塬信息
- **删除山塬**: 删除不需要的山塬记录
- **批量操作**: 支持批量导入和导出

**API接口**:
```
POST /admin/data/mountain        # 创建山塬
PUT /admin/data/mountain/:id     # 更新山塬
DELETE /admin/data/mountain/:id  # 删除山塬
GET /admin/data/mountain         # 查询山塬列表
GET /admin/data/mountain/:id     # 获取山塬详情
```

**数据字段**:
- 名称 (必填)
- 编号 (必填，唯一)
- 经度、纬度 (可选)
- 高度 (可选)
- 所属区域 (必填)
- 所属类型 (可选)
- 历史记载 (可选)

#### 1.2 水系数据管理
**功能路径**: `/admin/water-system`

**主要操作**:
- **新增水系**: 创建新的水系记录
- **编辑水系**: 修改现有水系信息
- **删除水系**: 删除不需要的水系记录
- **批量操作**: 支持批量导入和导出

**API接口**:
```
POST /admin/data/water-system        # 创建水系
PUT /admin/data/water-system/:id     # 更新水系
DELETE /admin/data/water-system/:id  # 删除水系
GET /admin/data/water-system         # 查询水系列表
GET /admin/data/water-system/:id     # 获取水系详情
```

**数据字段**:
- 名称 (必填)
- 编号 (必填，唯一)
- 经度、纬度 (可选)
- 长度/面积 (可选)
- 所属区域 (必填)
- 所属类型 (可选)
- 历史记载 (可选)

#### 1.3 历史要素数据管理
**功能路径**: `/admin/historical-element`

**主要操作**:
- **新增历史要素**: 创建新的历史要素记录
- **编辑历史要素**: 修改现有历史要素信息
- **删除历史要素**: 删除不需要的历史要素记录
- **批量操作**: 支持批量导入和导出

**API接口**:
```
POST /admin/data/historical-element        # 创建历史要素
PUT /admin/data/historical-element/:id     # 更新历史要素
DELETE /admin/data/historical-element/:id  # 删除历史要素
GET /admin/data/historical-element         # 查询历史要素列表
GET /admin/data/historical-element/:id     # 获取历史要素详情
```

**数据字段**:
- 名称 (必填)
- 编号 (必填，唯一)
- 位置描述 (可选)
- 建造年份 (可选，支持公元前)
- 所属区域 (必填)
- 所属类型 (可选)
- 历史记载 (可选)

#### 1.4 文化要素统一管理 (新版本)
**功能路径**: `/admin/cultural-element`

**主要操作**:
- **统一管理**: 将山塬、水系、历史要素统一管理
- **类型区分**: 通过类型字典区分不同要素类型
- **数据迁移**: 支持从旧版本数据迁移

**API接口**:
```
POST /admin/cultural-element        # 创建文化要素
PUT /admin/cultural-element/:id     # 更新文化要素
DELETE /admin/cultural-element/:id  # 删除文化要素
GET /admin/cultural-element         # 查询文化要素列表
GET /admin/cultural-element/:id     # 获取文化要素详情
```

### 2. 字典管理

#### 2.1 区域字典管理
**功能描述**: 管理行政区域划分信息

**主要操作**:
- 新增区域
- 编辑区域信息
- 删除区域
- 设置父子关系

**API接口**:
```
POST /admin/region-dict        # 创建区域
PUT /admin/region-dict/:id     # 更新区域
DELETE /admin/region-dict/:id  # 删除区域
GET /admin/region-dict         # 查询区域列表
```

#### 2.2 类型字典管理
**功能描述**: 管理各类要素的分类信息

**主要操作**:
- 新增类型
- 编辑类型信息
- 删除类型
- 设置类型层级

**API接口**:
```
POST /admin/type-dict        # 创建类型
PUT /admin/type-dict/:id     # 更新类型
DELETE /admin/type-dict/:id  # 删除类型
GET /admin/type-dict         # 查询类型列表
```

#### 2.3 关系字典管理
**功能描述**: 管理要素间关系类型定义

**主要操作**:
- 新增关系类型
- 编辑关系类型
- 删除关系类型
- 设置关系属性

**API接口**:
```
POST /admin/relationship-dict        # 创建关系类型
PUT /admin/relationship-dict/:id     # 更新关系类型
DELETE /admin/relationship-dict/:id  # 删除关系类型
GET /admin/relationship-dict         # 查询关系类型列表
```

### 3. 关系管理

#### 3.1 要素关系管理
**功能描述**: 管理各类地理文化要素之间的关联关系

**主要操作**:
- 创建关系
- 编辑关系
- 删除关系
- 批量管理

**API接口**:
```
POST /admin/relationship        # 创建关系
PUT /admin/relationship/:id     # 更新关系
DELETE /admin/relationship/:id  # 删除关系
GET /admin/relationship         # 查询关系列表
```

### 4. 文件管理

#### 4.1 照片上传管理
**功能描述**: 管理与地理文化要素相关的照片文件

**主要操作**:
- 上传照片
- 关联要素
- 删除照片
- 批量管理

**API接口**:
```
POST /api/upload/file           # 上传文件
POST /admin/photo               # 创建照片记录
DELETE /admin/photo/:id         # 删除照片
GET /admin/photo                # 查询照片列表
```

**支持格式**: JPG, PNG, GIF, WEBP
**文件大小**: 最大10MB
**存储路径**: `/public/uploads/`

#### 4.2 Excel导入导出
**功能描述**: 支持批量数据的导入导出操作

**导出功能**:
```
POST /admin/cultural-element/export
POST /admin/mountain/export
POST /admin/water-system/export
POST /admin/historical-element/export
```

**导入功能**:
```
GET /admin/cultural-element/template     # 下载模板
POST /admin/cultural-element/import/preview  # 预览导入
POST /admin/cultural-element/import/execute  # 执行导入
```

### 5. 系统管理

#### 5.1 系统状态监控
**功能描述**: 监控系统运行状态和性能指标

**监控内容**:
- 系统运行时间
- 内存使用情况
- 数据库连接状态
- Redis缓存状态

**API接口**:
```
GET /admin/system/status        # 获取系统状态
GET /admin/system/health        # 健康检查
```

#### 5.2 数据初始化
**功能描述**: 初始化系统基础数据和配置

**初始化内容**:
- 字典数据初始化
- 默认用户创建
- 上传目录创建
- 缓存预热

**API接口**:
```
POST /admin/system/data/init     # 初始化数据
POST /admin/system/data/repair   # 修复数据
```

#### 5.3 缓存管理
**功能描述**: 管理系统缓存数据

**主要操作**:
- 刷新字典缓存
- 清除过期缓存
- 预热常用数据

**API接口**:
```
POST /admin/system/cache/refresh  # 刷新缓存
DELETE /admin/system/cache/clear  # 清除缓存
```

## 数据导入导出

### Excel模板格式

#### 山塬数据模板
| 名称 | 编号 | 经度 | 纬度 | 高度 | 区域名称 | 类型名称 | 历史记载 |
|------|------|------|------|------|----------|----------|----------|
| 华山 | HS001 | 110.0910 | 34.4880 | 2154 | 华阴市 | 名山 | 五岳之一 |

#### 水系数据模板
| 名称 | 编号 | 经度 | 纬度 | 长度面积 | 区域名称 | 类型名称 | 历史记载 |
|------|------|------|------|----------|----------|----------|----------|
| 渭河 | WH001 | 108.9 | 34.3 | 818公里 | 西安市 | 主要河流 | 关中母亲河 |

#### 历史要素数据模板
| 名称 | 编号 | 位置描述 | 建造年份 | 区域名称 | 类型名称 | 历史记载 |
|------|------|----------|----------|----------|----------|----------|
| 大雁塔 | DYT001 | 西安市雁塔区 | 652 | 西安市 | 古塔 | 唐代建筑 |

### 导入操作流程
1. 下载对应的Excel模板
2. 按模板格式填写数据
3. 上传Excel文件进行预览
4. 检查预览结果无误后执行导入
5. 查看导入结果和错误报告

### 导出操作流程
1. 设置导出条件和筛选器
2. 选择导出字段
3. 执行导出操作
4. 下载生成的Excel文件

## 常见操作场景

### 场景1: 新增地理要素
1. 选择对应的数据管理模块
2. 点击"新增"按钮
3. 填写必填字段信息
4. 选择所属区域和类型
5. 添加可选的位置和历史信息
6. 保存数据

### 场景2: 批量导入数据
1. 下载Excel导入模板
2. 按模板格式整理数据
3. 上传Excel文件
4. 预览导入结果
5. 确认无误后执行导入
6. 查看导入报告

### 场景3: 建立要素关系
1. 进入关系管理模块
2. 选择源要素和目标要素
3. 选择关系类型
4. 填写关系描述
5. 保存关系记录

### 场景4: 系统维护
1. 定期检查系统状态
2. 刷新字典缓存
3. 清理过期数据
4. 备份重要数据

## 注意事项

### 数据安全
- 定期备份数据库
- 谨慎执行删除操作
- 重要操作前先导出数据
- 保护管理员账户安全

### 性能优化
- 避免频繁的大批量操作
- 定期清理无用的照片文件
- 合理使用缓存机制
- 监控系统资源使用

### 数据质量
- 确保编号的唯一性
- 验证地理坐标的准确性
- 保持数据格式的一致性
- 及时更新过时信息

## 故障排除

### 常见问题
1. **登录失败**: 检查用户名密码，确认账户状态
2. **上传失败**: 检查文件格式和大小限制
3. **导入错误**: 检查Excel格式和数据完整性
4. **系统缓慢**: 检查数据库连接和缓存状态

### 联系支持
- **技术支持**: 联系系统开发团队
- **数据问题**: 联系数据管理部门
- **紧急故障**: 联系系统管理员

---

**文档版本**: v1.0.0  
**更新时间**: 2025-10-09  
**适用系统**: 智慧营建系统管理端 v1.0.0
