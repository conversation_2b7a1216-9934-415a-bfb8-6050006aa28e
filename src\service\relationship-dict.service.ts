import { Provide, Inject } from '@midwayjs/core';
import { Op } from 'sequelize';
import { RelationshipDict } from '../entity/relationship-dict.entity';
import { CacheService } from './cache.service';
import { PageResponseDTO } from '../dto/common.dto';
import {
  CreateRelationshipDictDTO,
  UpdateRelationshipDictDTO,
  RelationshipDictQueryDTO,
  RelationshipDictResponseDTO,
} from '../dto/dictionary.dto';
import { buildTree } from '../utils/tree.util';

@Provide()
export class RelationshipDictService {
  @Inject()
  cacheService: CacheService;

  /**
   * 创建关系字典
   */
  async createRelationshipDict(
    createDto: CreateRelationshipDictDTO
  ): Promise<RelationshipDictResponseDTO> {
    // 验证数据
    await this.validateRelationshipDictData(createDto);

    // 检查编码唯一性
    await this.checkRelationCodeUnique(createDto.relationCode);

    const relationshipDict = await RelationshipDict.create(createDto as any);

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();

    return new RelationshipDictResponseDTO(relationshipDict.toJSON());
  }

  /**
   * 更新关系字典
   */
  async updateRelationshipDict(
    id: number,
    updateDto: UpdateRelationshipDictDTO
  ): Promise<RelationshipDictResponseDTO> {
    // 验证数据
    await this.validateRelationshipDictData(updateDto);

    const relationshipDict = await RelationshipDict.findByPk(id);
    if (!relationshipDict) {
      throw new Error('关系字典不存在');
    }

    // 检查编码唯一性（如果更新了编码）
    if (
      updateDto.relationCode &&
      updateDto.relationCode !== relationshipDict.relationCode
    ) {
      await this.checkRelationCodeUnique(updateDto.relationCode, id);
    }

    await relationshipDict.update(updateDto);

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();

    return new RelationshipDictResponseDTO(relationshipDict.toJSON());
  }

  /**
   * 删除关系字典（级联删除子级）
   */
  async deleteRelationshipDict(id: number): Promise<void> {
    const relationshipDict = await RelationshipDict.findByPk(id);
    if (!relationshipDict) {
      throw new Error('关系字典不存在');
    }

    // TODO: 检查是否有关联的关系数据

    // 使用级联删除，会自动删除所有子级关系
    await relationshipDict.destroy();

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();
  }

  /**
   * 根据ID获取关系字典
   */
  async getRelationshipDictById(
    id: number
  ): Promise<RelationshipDictResponseDTO | null> {
    const relationshipDict = await RelationshipDict.findByPk(id, {
      include: [
        {
          model: RelationshipDict,
          as: 'parent',
          attributes: ['id', 'relationName', 'relationCode'],
        },
        {
          model: RelationshipDict,
          as: 'children',
          attributes: ['id', 'relationName', 'relationCode', 'status'],
          where: { status: 1 },
          required: false,
        },
      ],
    });

    if (!relationshipDict) {
      return null;
    }

    return new RelationshipDictResponseDTO(relationshipDict.toJSON());
  }

  /**
   * 获取关系字典列表（缓存）
   */
  async getRelationshipDictList(): Promise<RelationshipDictResponseDTO[]> {
    const relationships = await this.cacheService.getRelationshipDictCache();
    return relationships.map(
      relationship => new RelationshipDictResponseDTO(relationship)
    );
  }

  /**
   * 获取关系字典树形结构
   */
  async getRelationshipDictTree(): Promise<RelationshipDictResponseDTO[]> {
    const relationships = await this.getRelationshipDictList();
    return buildTree(relationships);
  }

  /**
   * 分页查询关系字典
   */
  async getRelationshipDictPage(
    queryDto: RelationshipDictQueryDTO
  ): Promise<PageResponseDTO<RelationshipDictResponseDTO>> {
    const { page = 1, pageSize = 10, keyword, status, parentId } = queryDto;
    const offset = (page - 1) * pageSize;

    const whereCondition: any = {};

    if (keyword) {
      whereCondition[Op.or] = [
        { relationCode: { [Op.like]: `%${keyword}%` } },
        { relationName: { [Op.like]: `%${keyword}%` } },
      ];
    }

    if (status !== undefined) {
      whereCondition.status = status;
    }

    if (parentId !== undefined) {
      whereCondition.parentId = parentId;
    }

    const { count, rows } = await RelationshipDict.findAndCountAll({
      where: whereCondition,
      limit: pageSize,
      offset,
      order: [
        ['sort', 'ASC'],
        ['id', 'ASC'],
      ],
      include: [
        {
          model: RelationshipDict,
          as: 'parent',
          attributes: ['id', 'relationName', 'relationCode'],
        },
      ],
    });

    const list = rows.map(row => new RelationshipDictResponseDTO(row.toJSON()));

    return new PageResponseDTO(list, count, page, pageSize);
  }

  /**
   * 批量更新状态
   */
  async batchUpdateStatus(ids: number[], status: number): Promise<void> {
    await RelationshipDict.update(
      { status },
      { where: { id: { [Op.in]: ids } } }
    );

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();
  }

  /**
   * 启用/禁用关系字典
   */
  async toggleStatus(id: number): Promise<{ status: number; message: string }> {
    const relationshipDict = await RelationshipDict.findByPk(id);
    if (!relationshipDict) {
      throw new Error('关系字典不存在');
    }

    const newStatus = relationshipDict.status === 1 ? 0 : 1;
    await relationshipDict.update({ status: newStatus });

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();

    return {
      status: newStatus,
      message: newStatus === 1 ? '关系字典已启用' : '关系字典已禁用',
    };
  }

  // ==================== 私有方法 ====================

  /**
   * 验证关系字典数据
   */
  private async validateRelationshipDictData(
    data: CreateRelationshipDictDTO | UpdateRelationshipDictDTO
  ): Promise<void> {
    if (data.relationCode && data.relationCode.trim().length === 0) {
      throw new Error('关系编码不能为空');
    }

    if (data.relationName && data.relationName.trim().length === 0) {
      throw new Error('关系名称不能为空');
    }

    // 验证父级关系是否存在
    if (data.parentId) {
      const parent = await RelationshipDict.findByPk(data.parentId);
      if (!parent) {
        throw new Error('父级关系不存在');
      }
    }
  }

  /**
   * 检查关系编码唯一性
   */
  private async checkRelationCodeUnique(
    relationCode: string,
    excludeId?: number
  ): Promise<void> {
    const whereCondition: any = { relationCode };
    if (excludeId) {
      whereCondition.id = { [Op.ne]: excludeId };
    }

    const existing = await RelationshipDict.findOne({ where: whereCondition });
    if (existing) {
      throw new Error('关系编码已存在');
    }
  }
}
