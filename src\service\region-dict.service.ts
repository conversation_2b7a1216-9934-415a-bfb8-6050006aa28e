import { Provide, Inject } from '@midwayjs/core';
import { Op } from 'sequelize';
import { RegionDict } from '../entity/region-dict.entity';
import { CacheService } from './cache.service';
import { PageResponseDTO } from '../dto/common.dto';
import {
  CreateRegionDictDTO,
  UpdateRegionDictDTO,
  RegionDictQueryDTO,
  RegionDictResponseDTO,
} from '../dto/dictionary.dto';
import { buildTree } from '../utils/tree.util';

@Provide()
export class RegionDictService {
  @Inject()
  cacheService: CacheService;

  /**
   * 创建区域字典
   */
  async createRegionDict(
    createDto: CreateRegionDictDTO
  ): Promise<RegionDictResponseDTO> {
    // 验证数据
    await this.validateRegionDictData(createDto);

    // 检查编码唯一性
    await this.checkRegionCodeUnique(createDto.regionCode);

    const regionDict = await RegionDict.create(createDto as any);

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();

    return new RegionDictResponseDTO(regionDict.toJSON());
  }

  /**
   * 更新区域字典
   */
  async updateRegionDict(
    id: number,
    updateDto: UpdateRegionDictDTO
  ): Promise<RegionDictResponseDTO> {
    // 验证数据
    await this.validateRegionDictData(updateDto);

    const regionDict = await RegionDict.findByPk(id);
    if (!regionDict) {
      throw new Error('区域字典不存在');
    }

    // 检查编码唯一性（如果更新了编码）
    if (
      updateDto.regionCode &&
      updateDto.regionCode !== regionDict.regionCode
    ) {
      await this.checkRegionCodeUnique(updateDto.regionCode, id);
    }

    await regionDict.update(updateDto);

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();

    return new RegionDictResponseDTO(regionDict.toJSON());
  }

  /**
   * 删除区域字典（级联删除子级）
   */
  async deleteRegionDict(id: number): Promise<void> {
    const regionDict = await RegionDict.findByPk(id);
    if (!regionDict) {
      throw new Error('区域字典不存在');
    }

    // TODO: 检查是否有关联的山峰、水系、历史要素等数据

    // 使用级联删除，会自动删除所有子级区域
    await regionDict.destroy();

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();
  }

  /**
   * 根据ID获取区域字典
   */
  async getRegionDictById(id: number): Promise<RegionDictResponseDTO | null> {
    const regionDict = await RegionDict.findByPk(id, {
      include: [
        {
          model: RegionDict,
          as: 'parent',
          attributes: ['id', 'regionName', 'regionCode'],
        },
        {
          model: RegionDict,
          as: 'children',
          attributes: ['id', 'regionName', 'regionCode', 'status'],
          where: { status: 1 },
          required: false,
        },
      ],
    });

    if (!regionDict) {
      return null;
    }

    return new RegionDictResponseDTO(regionDict.toJSON());
  }

  /**
   * 获取区域字典列表（缓存）
   */
  async getRegionDictList(): Promise<RegionDictResponseDTO[]> {
    const regions = await this.cacheService.getRegionDictCache();
    return regions.map(region => new RegionDictResponseDTO(region));
  }

  /**
   * 分页查询区域字典
   */
  async getRegionDictPage(
    queryDto: RegionDictQueryDTO
  ): Promise<PageResponseDTO<RegionDictResponseDTO>> {
    const { page = 1, pageSize = 10, keyword, status, parentId } = queryDto;
    const offset = (page - 1) * pageSize;

    const whereCondition: any = {};

    if (keyword) {
      whereCondition[Op.or] = [
        { regionCode: { [Op.like]: `%${keyword}%` } },
        { regionName: { [Op.like]: `%${keyword}%` } },
      ];
    }

    if (status !== undefined) {
      whereCondition.status = status;
    }

    if (parentId !== undefined) {
      whereCondition.parentId = parentId;
    }

    const { count, rows } = await RegionDict.findAndCountAll({
      where: whereCondition,
      limit: pageSize,
      offset,
      order: [
        ['sort', 'ASC'],
        ['id', 'ASC'],
      ],
      include: [
        {
          model: RegionDict,
          as: 'parent',
          attributes: ['id', 'regionName', 'regionCode'],
        },
      ],
    });

    const list = rows.map(row => new RegionDictResponseDTO(row.toJSON()));

    return new PageResponseDTO(list, count, page, pageSize);
  }

  /**
   * 获取区域字典树形结构
   */
  async getRegionDictTree(): Promise<RegionDictResponseDTO[]> {
    const regions = await this.getRegionDictList();
    return buildTree(regions);
  }

  /**
   * 根据区域名称查找区域字典
   */
  async getRegionDictByName(
    regionName: string
  ): Promise<RegionDictResponseDTO | null> {
    if (!regionName || regionName.trim().length === 0) {
      return null;
    }

    const regionDict = await RegionDict.findOne({
      where: {
        regionName: regionName.trim(),
        status: 1, // 只查找启用的区域
      },
      include: [
        {
          model: RegionDict,
          as: 'parent',
          attributes: ['id', 'regionName', 'regionCode'],
        },
      ],
    });

    if (!regionDict) {
      return null;
    }

    return new RegionDictResponseDTO(regionDict.toJSON());
  }

  /**
   * 批量更新状态
   */
  async batchUpdateStatus(ids: number[], status: number): Promise<void> {
    await RegionDict.update({ status }, { where: { id: { [Op.in]: ids } } });

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();
  }

  /**
   * 启用/禁用区域字典
   */
  async toggleStatus(id: number): Promise<{ status: number; message: string }> {
    const regionDict = await RegionDict.findByPk(id);
    if (!regionDict) {
      throw new Error('区域字典不存在');
    }

    const newStatus = regionDict.status === 1 ? 0 : 1;
    await regionDict.update({ status: newStatus });

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();

    return {
      status: newStatus,
      message: newStatus === 1 ? '区域字典已启用' : '区域字典已禁用',
    };
  }

  // ==================== 私有方法 ====================

  /**
   * 验证区域字典数据
   */
  private async validateRegionDictData(
    data: CreateRegionDictDTO | UpdateRegionDictDTO
  ): Promise<void> {
    if (data.regionCode && data.regionCode.trim().length === 0) {
      throw new Error('区域编码不能为空');
    }

    if (data.regionName && data.regionName.trim().length === 0) {
      throw new Error('区域名称不能为空');
    }

    // 验证父级区域是否存在
    if (data.parentId) {
      const parent = await RegionDict.findByPk(data.parentId);
      if (!parent) {
        throw new Error('父级区域不存在');
      }
    }
  }

  /**
   * 检查区域编码唯一性
   */
  private async checkRegionCodeUnique(
    regionCode: string,
    excludeId?: number
  ): Promise<void> {
    const whereCondition: any = { regionCode };
    if (excludeId) {
      whereCondition.id = { [Op.ne]: excludeId };
    }

    const existing = await RegionDict.findOne({ where: whereCondition });
    if (existing) {
      throw new Error('区域编码已存在');
    }
  }
}
