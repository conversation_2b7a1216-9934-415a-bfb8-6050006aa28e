/*
 * @Description: 使用xlsx生成excel文件，可以更好的控制样式
 * @Date: 2025-04-15 14:30:43
 * @LastEditors: Zhu<PERSON><PERSON>liang <EMAIL>
 * @LastEditTime: 2025-09-03 20:27:43
 */
import * as XLSX from 'xlsx';
// 引入 xlsx-style 库来支持样式
import * as XLSXStyle from 'xlsx-style';
import { ExcelStyleManager } from './excel-style-manager';
import { ExcelAdvancedGenerator } from './excel-advanced-generator';
import type { ExcelTemplateConfig } from '../config/excel-configs';

/**
 * Excel解析结果接口
 */
export interface ExcelParseResult<T = any> {
  /** 是否解析成功 */
  success: boolean;
  /** 解析后的数据 */
  data?: T[];
  /** 错误信息 */
  errors?: ExcelValidationError[];
  /** 总行数 */
  totalRows?: number;
  /** 有效行数 */
  validRows?: number;
}

/**
 * Excel验证错误接口
 */
export interface ExcelValidationError {
  /** 错误所在行号 */
  row: number;
  /** 错误字段 */
  field: string;
  /** 错误值 */
  value: any;
  /** 错误信息 */
  message: string;
}

export class XlsxUtils {
  /**
   * 生成导入模板
   * @param config 模板配置
   * @returns Buffer
   */
  static generateTemplate(config: ExcelTemplateConfig): Buffer {
    // 检查是否使用高级生成器
    const useAdvanced =
      config.styleConfig?.enabled &&
      (config.styleConfig as any)?.useAdvancedGenerator;

    if (useAdvanced) {
      // 使用高级生成器（ExcelJS）
      return this.generateAdvancedTemplate(config);
    }

    // 使用标准生成器
    const workbook = XLSX.utils.book_new();

    // 创建模板工作表
    const mainSheet = this.createTemplateSheet(config);
    XLSX.utils.book_append_sheet(
      workbook,
      mainSheet,
      config.sheetName || '数据导入'
    );

    // 如果启用了样式，使用 xlsx-style 写入，否则使用标准 XLSX
    const useStyles = config.styleConfig?.enabled ?? false;

    if (useStyles) {
      const buffer = XLSXStyle.write(workbook, {
        type: 'buffer',
        bookType: 'xlsx',
      });
      return buffer;
    } else {
      const buffer = XLSX.write(workbook, {
        type: 'buffer',
        bookType: 'xlsx',
      });
      return buffer;
    }
  }

  /**
   * 使用高级生成器生成模板
   */
  private static generateAdvancedTemplate(config: ExcelTemplateConfig): Buffer {
    // 这里需要同步包装异步方法
    // 在实际使用中，建议将整个方法改为异步
    try {
      // 临时使用同步方式，实际应该改为异步
      const result = ExcelAdvancedGenerator.generateAdvancedTemplate(config, {
        theme: config.styleConfig?.theme as any,
        enableDataValidation: true,
        enableConditionalFormatting: true,
        fontSize: 12,
      });

      // 注意：这里需要处理Promise，实际使用时应该将整个方法改为异步
      if (result instanceof Promise) {
        throw new Error(
          '高级生成器需要异步调用，请使用 generateTemplateAsync 方法'
        );
      }

      return result as Buffer;
    } catch (error) {
      console.warn('高级生成器失败，回退到标准生成器:', error.message);
      // 回退到标准生成器
      const workbook = XLSX.utils.book_new();
      const mainSheet = this.createTemplateSheet(config);
      XLSX.utils.book_append_sheet(
        workbook,
        mainSheet,
        config.sheetName || '数据导入'
      );
      return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    }
  }

  /**
   * 异步生成模板（推荐使用）
   */
  static async generateTemplateAsync(
    config: ExcelTemplateConfig
  ): Promise<Buffer> {
    // 检查是否使用高级生成器
    const useAdvanced =
      config.styleConfig?.enabled &&
      (config.styleConfig as any)?.useAdvancedGenerator;

    if (useAdvanced) {
      // 使用高级生成器（ExcelJS）
      return await ExcelAdvancedGenerator.generateAdvancedTemplate(config, {
        theme: config.styleConfig?.theme as any,
        enableDataValidation: true,
        enableConditionalFormatting: true,
        fontSize: 12,
      });
    }

    // 使用标准生成器
    return this.generateTemplate(config);
  }

  /**
   * 美化表格，添加线框，表头加粗加反色
   * @param sheet - 工作表对象
   */
  static beautifySheet(sheet: XLSX.WorkSheet) {
    if (!sheet['!ref']) return;

    const range = XLSX.utils.decode_range(sheet['!ref']);

    // 更明确的样式定义
    const headerStyle = {
      font: {
        bold: true,
        color: { rgb: 'FFFFFF' },
      },
      fill: {
        patternType: 'solid',
        fgColor: { rgb: '4472C4' },
      },
      border: {
        top: { style: 'thin', color: { auto: 1 } },
        bottom: { style: 'thin', color: { auto: 1 } },
        left: { style: 'thin', color: { auto: 1 } },
        right: { style: 'thin', color: { auto: 1 } },
      },
      alignment: {
        horizontal: 'center',
        vertical: 'center',
      },
    };

    const cellStyle = {
      border: {
        top: { style: 'thin', color: { auto: 1 } },
        bottom: { style: 'thin', color: { auto: 1 } },
        left: { style: 'thin', color: { auto: 1 } },
        right: { style: 'thin', color: { auto: 1 } },
      },
    };

    // 设置列宽
    const colWidth = [];
    for (let col = range.s.c; col <= range.e.c; col++) {
      colWidth.push({ wch: 15 }); // 设置默认列宽
    }
    sheet['!cols'] = colWidth;

    // 设置表头样式 - 对所有表头行应用样式
    const headerRows = sheet['!merges']
      ? Math.max(...sheet['!merges'].map(m => m.e.r)) + 1
      : 1;

    for (let row = range.s.r; row < headerRows; row++) {
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        if (!sheet[cellAddress]) {
          sheet[cellAddress] = { t: 's', v: '' };
        }
        sheet[cellAddress].s = headerStyle;
      }
    }

    // 设置数据单元格样式
    for (let row = headerRows; row <= range.e.r; row++) {
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        if (!sheet[cellAddress]) {
          sheet[cellAddress] = { t: 's', v: '' };
        }
        sheet[cellAddress].s = cellStyle;
      }
    }
  }

  /**
   * 创建模板工作表
   */
  private static createTemplateSheet(
    config: ExcelTemplateConfig
  ): XLSX.WorkSheet {
    const data: any[][] = [];
    let currentRow = 0;

    // 1. 添加标题
    data.push([config.title]);
    const titleRow = currentRow++;
    data.push([]); // 空行
    currentRow++;

    // 2. 添加填写说明（限制数量以避免内存问题）
    const instructions = config.instructions.slice(0, 3);
    const instructionStartRow = currentRow;
    instructions.forEach(instruction => {
      data.push([instruction]);
      currentRow++;
    });
    const instructionEndRow = currentRow - 1;
    data.push([]); // 空行
    currentRow++;

    // 3. 添加表头
    const headerRow = config.headers.map(h =>
      h.required ? `${h.label}*` : h.label
    );
    data.push(headerRow);
    const headerRowIndex = currentRow++;

    // 4. 添加字段说明行
    const descriptionRow = config.headers.map(h => h.description || '');
    data.push(descriptionRow);
    const descriptionRowIndex = currentRow++;

    // 5. 添加一行空数据作为示例
    const emptyRow = config.headers.map(() => '');
    data.push(emptyRow);
    const dataStartRow = currentRow;

    // 创建工作表
    const sheet = XLSX.utils.aoa_to_sheet(data);

    // 设置列宽
    const colWidths = config.headers.map(h => ({ wch: h.width || 15 }));
    ExcelStyleManager.setColumnWidths(
      sheet,
      colWidths.map(w => w.wch)
    );

    // 如果启用样式，应用样式
    if (config.styleConfig?.enabled) {
      // 合并标题行
      ExcelStyleManager.mergeCells(
        sheet,
        titleRow,
        0,
        titleRow,
        config.headers.length - 1
      );

      // 找出必填列
      const requiredColumns = config.headers
        .map((h, index) => (h.required ? index : -1))
        .filter(i => i >= 0);

      // 应用样式
      ExcelStyleManager.applyStylesToSheet(
        sheet,
        {
          titleRow,
          instructionStartRow,
          instructionEndRow,
          headerRow: headerRowIndex,
          descriptionRow: descriptionRowIndex,
          dataStartRow,
          requiredColumns,
        },
        config.styleConfig
      );
    }

    return sheet;
  }

  /**
   * 解析Excel文件
   * @param filePath 文件路径
   * @param config 模板配置
   * @param validator 数据验证函数（支持异步）
   * @returns 解析结果
   */
  static async parseExcel<T = any>(
    filePath: string,
    config: ExcelTemplateConfig,
    validator?: (
      data: any,
      rowIndex: number
    ) => ExcelValidationError[] | Promise<ExcelValidationError[]>
  ): Promise<ExcelParseResult<T>> {
    try {
      // 读取Excel文件
      const workbook = XLSX.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      // 转换为JSON数据
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      if (jsonData.length < 3) {
        return {
          success: false,
          errors: [
            {
              row: 0,
              field: 'file',
              value: '',
              message: 'Excel文件格式不正确',
            },
          ],
        };
      }

      // 找到数据开始行（跳过标题、说明、表头、字段说明）
      const dataStartRow = this.findDataStartRow(jsonData as any[][]);
      const dataRows = jsonData.slice(dataStartRow);

      const validData: T[] = [];
      const errors: ExcelValidationError[] = [];

      for (let index = 0; index < dataRows.length; index++) {
        const row = dataRows[index] as any[];
        // 实际行号：dataStartRow是数组索引，需要+1转换为Excel行号，再加上当前数据行的偏移
        const rowIndex = dataStartRow + index + 1;

        // 跳过空行
        if (!row || row.every(cell => !cell || cell.toString().trim() === '')) {
          continue;
        }

        // 转换行数据为对象
        const rowData: any = {};
        config.headers.forEach((header, colIndex) => {
          rowData[header.key] = row[colIndex];
        });

        // 验证数据
        if (validator) {
          const rowErrors = await validator(rowData, rowIndex);
          if (rowErrors.length > 0) {
            errors.push(...rowErrors);
            continue;
          }
        }

        validData.push(rowData);
      }

      return {
        success: errors.length === 0,
        data: validData,
        errors: errors.length > 0 ? errors : undefined,
        totalRows: dataRows.filter(
          row =>
            row &&
            Array.isArray(row) &&
            row.some((cell: any) => cell && cell.toString().trim() !== '')
        ).length,
        validRows: validData.length,
      };
    } catch (error) {
      return {
        success: false,
        errors: [
          {
            row: 0,
            field: 'file',
            value: '',
            message: `文件解析失败: ${error.message}`,
          },
        ],
      };
    }
  }

  /**
   * 找到数据开始行
   */
  private static findDataStartRow(data: any[][]): number {
    // 查找表头行：应该是包含多个*号字段的行，且不是纯文本说明
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      if (row && Array.isArray(row) && row.length > 1) {
        // 检查是否有多个包含*号的单元格（表头特征）
        const starCells = row.filter(
          cell =>
            cell &&
            cell.toString().includes('*') &&
            cell.toString().trim().length < 20 // 排除长文本说明
        );

        if (starCells.length >= 2) {
          // 至少2个*号字段才认为是表头
          // 表头行之后是字段说明行，再之后才是数据行
          return i + 2;
        }
      }
    }
    // 如果没找到表头行，默认从第9行开始（基于模板结构）
    return 8; // 第9行（索引从0开始）
  }

  /**
   * 将 JSON 数据转换为工作表，并支持合并单元格和表格美化
   * @param jsonData - JSON 数据
   * @param headerConfig - 表头配置，包含表头标签和数据映射
   * @param mergeConfigs - 合并单元格配置，格式为 { startRow, startCol, endRow, endCol } 的数组
   * @returns 工作表对象
   */
  static jsonToSheet({
    jsonData,
    headerConfig,
    mergeConfigs = [],
  }: {
    jsonData: any[];
    headerConfig: {
      // 表头用二维数组表示，以支持多级表头
      headers: string[][];
      dataKeys: (string | ((item: any, index: number) => any))[];
    };
    mergeConfigs?: {
      startRow: number;
      startCol: number;
      endRow: number;
      endCol: number;
    }[];
  }): XLSX.WorkSheet {
    const headers = headerConfig.headers;
    const data = jsonData.map((item, index) => {
      return headerConfig.dataKeys.map(key => {
        if (typeof key === 'function') {
          return key(item, index);
        }
        return item[key];
      });
    });

    const sheet = XLSX.utils.aoa_to_sheet([...headers, ...data]);

    // 合并单元格
    mergeConfigs.forEach(config => {
      this.mergeCells(
        sheet,
        config.startRow,
        config.startCol,
        config.endRow,
        config.endCol
      );
    });

    return sheet;
  }

  static createBuffer(
    sheetList: { sheet: XLSX.WorkSheet; sheetName?: string }[]
  ) {
    const workbook = XLSX.utils.book_new();
    sheetList.forEach(({ sheet, sheetName }, index) => {
      XLSX.utils.book_append_sheet(
        workbook,
        sheet,
        sheetName || 'Sheet' + (index + 1)
      );
    });

    // 使用标准 XLSX 写入方法
    const buffer = XLSX.write(workbook, {
      type: 'buffer',
      bookType: 'xlsx',
    });
    return buffer;
  }

  /**
   * 合并单元格
   * @param sheet - 工作表对象
   * @param startRow - 起始行
   * @param startCol - 起始列
   * @param endRow - 结束行
   * @param endCol - 结束列
   */
  static mergeCells(
    sheet: XLSX.WorkSheet,
    startRow: number,
    startCol: number,
    endRow: number,
    endCol: number
  ) {
    if (!sheet['!merges']) {
      sheet['!merges'] = [];
    }
    sheet['!merges'].push({
      s: { r: startRow, c: startCol },
      e: { r: endRow, c: endCol },
    });
  }
}
