import {
  Controller,
  Post,
  Put,
  Del,
  Get,
  Body,
  Param,
  Query,
  Inject,
} from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { JwtMiddleware } from '../../middleware/jwt.middleware';
import { AuthMiddleware } from '../../middleware/auth.middleware';
import { TypeDictService } from '../../service/type-dict.service';
import {
  CreateTypeDictDTO,
  UpdateTypeDictDTO,
  TypeDictQueryDTO,
  BatchUpdateStatusDTO,
} from '../../dto/dictionary.dto';

/**
 * 类型字典管理控制器
 */
@Controller('/admin/type-dict', { middleware: [JwtMiddleware, AuthMiddleware] })
export class AdminTypeDictController {
  @Inject()
  typeDictService: TypeDictService;

  /**
   * 创建类型字典
   */
  @Post('/')
  @Validate()
  async create(@Body() createDto: CreateTypeDictDTO) {
    console.log('🏷️ 创建类型字典:', createDto);

    try {
      const data = await this.typeDictService.createTypeDict(createDto);
      console.log('🏷️ 类型字典创建成功:', data.id);
      return data;
    } catch (error) {
      console.error('🏷️ 类型字典创建失败:', error);
      throw error;
    }
  }

  /**
   * 更新类型字典
   */
  @Put('/:id')
  @Validate()
  async update(@Param('id') id: number, @Body() updateDto: UpdateTypeDictDTO) {
    console.log('🏷️ 更新类型字典:', { id, updateDto });

    try {
      const data = await this.typeDictService.updateTypeDict(id, updateDto);
      console.log('🏷️ 类型字典更新成功:', data.id);
      return data;
    } catch (error) {
      console.error('🏷️ 类型字典更新失败:', error);
      throw error;
    }
  }

  /**
   * 删除类型字典
   */
  @Del('/:id')
  async delete(@Param('id') id: number) {
    console.log('🏷️ 删除类型字典:', id);

    try {
      await this.typeDictService.deleteTypeDict(id);
      console.log('🏷️ 类型字典删除成功:', id);
      return { message: '删除成功' };
    } catch (error) {
      console.error('🏷️ 类型字典删除失败:', error);
      throw error;
    }
  }

  /**
   * 获取类型字典详情
   */
  @Get('/:id')
  async getById(@Param('id') id: string) {
    console.log('🏷️ 获取类型字典详情:', id);

    // 检查是否是tree请求，如果是则提示使用公开接口
    if (id === 'tree') {
      throw new Error(
        '类型字典树形结构接口已移至公开接口，请使用: GET /openapi/type-dict/tree'
      );
    }

    // 验证ID是否为有效数字
    const numericId = parseInt(id);
    if (isNaN(numericId) || numericId <= 0) {
      throw new Error('无效的类型字典ID');
    }

    try {
      const data = await this.typeDictService.getTypeDictById(numericId);
      if (!data) {
        throw new Error('类型字典不存在');
      }
      console.log('🏷️ 类型字典详情获取成功:', data.id);
      return data;
    } catch (error) {
      console.error('🏷️ 类型字典详情获取失败:', error);
      throw error;
    }
  }

  /**
   * 获取类型字典列表（分页）
   */
  @Get('/')
  @Validate()
  async getList(@Query() queryDto: TypeDictQueryDTO) {
    console.log('🏷️ 类型字典列表查询开始:', queryDto);

    try {
      const data = await this.typeDictService.getTypeDictPage(queryDto);
      console.log('🏷️ 类型字典列表查询成功:', {
        total: data.total,
        page: data.page,
        pageSize: data.pageSize,
      });
      return data;
    } catch (error) {
      console.error('🏷️ 类型字典列表查询失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有类型字典（缓存）
   */
  @Get('/all')
  async getAll() {
    console.log('🏷️ 获取所有类型字典');

    try {
      const data = await this.typeDictService.getTypeDictList();
      console.log('🏷️ 所有类型字典获取成功:', data.length);
      return data;
    } catch (error) {
      console.error('🏷️ 所有类型字典获取失败:', error);
      throw error;
    }
  }

  /**
   * 批量更新状态
   */
  @Put('/batch-status')
  @Validate()
  async batchUpdateStatus(@Body() batchDto: BatchUpdateStatusDTO) {
    console.log('🏷️ 批量更新类型字典状态:', batchDto);

    try {
      await this.typeDictService.batchUpdateStatus(
        batchDto.ids,
        batchDto.status
      );
      console.log('🏷️ 批量更新类型字典状态成功');
      return { message: '批量更新成功' };
    } catch (error) {
      console.error('🏷️ 批量更新类型字典状态失败:', error);
      throw error;
    }
  }

  /**
   * 启用/禁用类型字典
   */
  @Post('/:id/toggle-status')
  async toggleStatus(@Param('id') id: number) {
    console.log('🏷️ 切换类型字典状态:', id);

    try {
      const result = await this.typeDictService.toggleStatus(id);
      console.log('🏷️ 类型字典状态切换成功:', result);
      return result;
    } catch (error) {
      console.error('🏷️ 类型字典状态切换失败:', error);
      throw error;
    }
  }
}
