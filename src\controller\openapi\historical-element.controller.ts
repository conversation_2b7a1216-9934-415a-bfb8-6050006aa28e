import { Controller, Get, Inject, Query, Param } from '@midwayjs/core';
import { HistoricalElementService } from '../../service/historical-element.service';
import { PhotoService } from '../../service/photo.service';
import { PageQueryDTO } from '../../dto/common.dto';

/**
 * 历史要素公开接口控制器
 */
@Controller('/openapi/historical-element')
export class PublicHistoricalElementController {
  @Inject()
  historicalElementService: HistoricalElementService;

  @Inject()
  photoService: PhotoService;

  /**
   * 获取所有历史要素（不分页）
   */
  @Get('/all')
  async getAll() {
    console.log('🏛️ [公开] 获取所有历史要素');

    try {
      const result = await this.historicalElementService.findAll({
        query: {}, // 查询所有
        order: [['code', 'ASC']], // 按编号排序
      });

      // 只返回必要字段
      const simplifiedData = result.list.map(item => ({
        id: item.id,
        name: item.name,
        code: item.code,
      }));

      console.log('🏛️ [公开] 所有历史要素获取成功:', simplifiedData.length);
      return simplifiedData;
    } catch (error) {
      console.error('🏛️ [公开] 所有历史要素获取失败:', error);
      throw error;
    }
  }

  /**
   * 列表（分页筛选）
   */
  @Get('/list')
  async getList(
    @Query() query: PageQueryDTO & { regionId?: number; typeId?: number }
  ) {
    const data = await this.historicalElementService.findList(query);
    return data;
  }

  /**
   * 详情
   */
  @Get('/:id')
  async getDetail(@Param('id') id: number) {
    if (!id || isNaN(Number(id))) {
      throw new Error('无效的ID');
    }
    const data = await this.historicalElementService.findById(Number(id));
    return data;
  }

  /**
   * 详情-照片
   */
  @Get('/:id/photos')
  async getPhotos(@Param('id') id: number) {
    if (!id || isNaN(Number(id))) {
      throw new Error('无效的ID');
    }
    const photos = await this.photoService.findPhotosByEntity('historicalElement', Number(id));
    return photos.map(p => ({ id: p.id, name: p.name, url: p.url }));
  }
}
