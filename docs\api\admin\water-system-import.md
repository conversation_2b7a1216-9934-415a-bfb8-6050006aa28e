# 水系导入 API 文档

## 概述

水系导入功能提供完整的 Excel 批量导入解决方案，包括模板下载、数据预览、正式导入等功能。

## API 接口

### 1. 获取导入模板

**GET** `/admin/water-system/template/download`

获取 Excel 导入模板下载链接。

#### 响应
```json
{
  "downloadUrl": "http://localhost:7001/public/templates/water_system_import_template.xlsx",
  "filename": "水系导入模板.xlsx",
  "description": "点击链接下载Excel导入模板，包含字段说明和示例数据"
}
```

**说明**:
- `downloadUrl`: 完整的下载URL，包含协议、域名和端口
- `filename`: 建议的文件名，用于下载时的文件命名
- `description`: 模板说明信息

#### 示例
```javascript
const response = await fetch('/admin/water-system/template/download');
const data = await response.json();
window.open(data.downloadUrl);
```

---

### 2. 导入数据预览

**POST** `/admin/water-system/import/preview`

验证 Excel 文件并预览导入结果，不会实际导入数据到数据库。

#### 请求
- **Content-Type**: `multipart/form-data`
- **文件字段**: `files`
- **支持格式**: `.xlsx`, `.xls`
- **文件大小**: 最大 10MB

#### 响应
```json
{
  "success": true,
  "message": "验证成功",
  "totalRows": 100,
  "validRows": 95,
  "errors": [
    {
      "row": 5,
      "field": "编号",
      "value": "DUP001",
      "message": "编号已存在于数据库中"
    }
  ],
  "preview": [
    {
      "name": "渭河",
      "code": "WH001",
      "longitude": 108.9633,
      "latitude": 34.2658,
      "lengthArea": "818公里",
      "regionDictId": 1
    }
  ]
}
```

#### 错误响应
```json
{
  "success": false,
  "message": "验证失败",
  "totalRows": 100,
  "validRows": 0,
  "errors": [
    {
      "row": 2,
      "field": "名称",
      "value": "",
      "message": "名称不能为空"
    }
  ]
}
```

#### 示例
```javascript
const formData = new FormData();
formData.append('files', excelFile);

const response = await fetch('/admin/water-system/import/preview', {
  method: 'POST',
  body: formData
});
const result = await response.json();

if (result.success && result.errors.length === 0) {
  console.log('验证通过，可以导入');
} else {
  console.log('验证失败:', result.errors);
}
```

---

### 3. 正式导入数据

**POST** `/admin/water-system/import/execute`

解析 Excel 文件并将数据导入到数据库。

#### 请求
同预览接口的请求格式。

#### 响应
```json
{
  "success": true,
  "message": "导入成功",
  "totalRows": 100,
  "validRows": 95,
  "importedCount": 95
}
```

#### 错误响应
```json
{
  "success": false,
  "message": "导入失败: 数据验证错误",
  "totalRows": 100,
  "validRows": 0,
  "errors": [
    {
      "row": 2,
      "field": "编号",
      "value": "DUP001",
      "message": "编号已存在于数据库中"
    }
  ]
}
```

#### 示例
```javascript
const formData = new FormData();
formData.append('files', excelFile);

const response = await fetch('/admin/water-system/import/execute', {
  method: 'POST',
  body: formData
});
const result = await response.json();

if (result.success) {
  console.log(`导入成功：${result.importedCount} 条记录`);
} else {
  console.log('导入失败:', result.message);
}
```

---

### 4. 批量导入接口

**POST** `/admin/water-system/batch-import`

直接批量导入水系数据，用于程序调用。

#### 请求
```json
{
  "waterSystems": [
    {
      "name": "渭河",
      "code": "WH001",
      "longitude": 108.9633,
      "latitude": 34.2658,
      "lengthArea": "818公里",
      "regionDictId": 1,
      "historicalRecords": "渭河是黄河的最大支流，流经关中平原，是关中地区的母亲河。"
    }
  ]
}
```

#### 响应
```json
{
  "message": "批量导入成功"
}
```

#### 示例
```javascript
const data = {
  waterSystems: [
    {
      name: "渭河",
      code: "WH001",
      longitude: 108.9633,
      latitude: 34.2658,
      lengthArea: "818公里",
      regionDictId: 1
    }
  ]
};

const response = await fetch('/admin/water-system/batch-import', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(data)
});
```

---

## 模板字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| 名称 | string | 是 | 水系名称，最大255字符 |
| 编号 | string | 是 | 水系编号，最大50字符，系统内唯一 |
| 经度 | number | 否 | 经度坐标，范围-180到180 |
| 纬度 | number | 否 | 纬度坐标，范围-90到90 |
| 长度/面积 | string | 否 | 长度或面积描述，最大50字符 |
| 历史记载 | string | 否 | 相关历史记载，最大2000字符 |
| 区域名称 | string | 是 | 所属区域名称，必须在区域字典中存在 |

## 导入流程

### 标准流程
1. **下载模板** → 调用模板下载接口
2. **填写数据** → 按照模板格式填写
3. **预览验证** → 上传文件进行预览
4. **确认导入** → 验证通过后正式导入

### 验证规则

#### 文件验证
- 文件格式：.xlsx 或 .xls
- 文件大小：最大 10MB
- 文件结构：符合模板格式

#### 数据验证
- **必填字段**：名称、编号、区域名称
- **可选字段**：经度、纬度、长度/面积、历史记载
- **数据类型**：数字字段必须为有效数字
- **数据范围**：经度 -180~180，纬度 -90~90
- **字段长度**：名称最大255字符，编号最大50字符，长度/面积最大50字符，历史记载最大2000字符
- **唯一性**：编号在系统中必须唯一
- **外键约束**：区域名称必须在区域字典中存在

## 错误处理

### 常见错误
1. **文件格式错误**：请上传 .xlsx 或 .xls 格式文件
2. **文件过大**：文件大小不能超过 10MB
3. **编号重复**：编号已存在于数据库中
4. **区域不存在**：区域名称不存在于字典中
5. **数据格式错误**：经纬度必须为有效数字
6. **数据范围错误**：经纬度超出有效范围

### 错误定位
- 每个错误都会标明具体的行号和字段
- 提供详细的错误描述和修改建议
- 支持批量错误显示

## 性能建议

1. **批量大小**：建议单次导入不超过1000条记录
2. **文件大小**：建议文件大小控制在5MB以内
3. **预览优先**：正式导入前务必先进行预览验证
4. **错误处理**：及时处理验证错误，避免重复上传

## 注意事项

1. **坐标系统**：使用WGS84坐标系统
2. **长度面积格式**：支持灵活的描述格式，如"818公里"、"134766平方公里"等
3. **编号规则**：建议使用有意义的编号，如"WH001"表示渭河001
4. **区域关联**：必须关联到已存在的区域字典项
5. **数据备份**：导入前建议备份现有数据
6. **权限控制**：所有导入接口都需要管理员权限
