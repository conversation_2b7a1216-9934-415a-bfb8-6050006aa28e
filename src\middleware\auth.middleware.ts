import { Middleware } from '@midwayjs/core';
import { Context, NextFunction } from '@midwayjs/koa';

@Middleware()
export class AuthMiddleware {
  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      // 检查用户是否已认证
      if (!ctx.state.user) {
        console.log('🔐 用户未认证');
        ctx.status = 401;
        ctx.body = {
          success: false,
          message: '用户未认证',
          code: 401,
        };
        return;
      }

      // 检查用户角色权限
      const user = ctx.state.user;

      if (user.role !== 'admin') {
        console.log('🔐 权限不足，用户角色:', user.role);
        ctx.status = 403;
        ctx.body = {
          success: false,
          message: '权限不足，需要管理员权限',
          code: 403,
        };
        return;
      }

      await next();
    };
  }

  ignore(ctx: Context): boolean {
    // 跳过不需要管理员权限的路径
    const skipPaths = ['/openapi', '/api', '/admin/auth/login', '/health', '/public'];

    // 下面的路由将忽略此中间件
    return skipPaths.some(path => ctx.path.startsWith(path));
  }

  static getName(): string {
    return 'API_AUTH';
  }
}
