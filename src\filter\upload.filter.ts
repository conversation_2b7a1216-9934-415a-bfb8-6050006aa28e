/**
 * 文件上传错误过滤器
 * 专门处理busboy/multer相关的文件上传错误
 */
import { Catch } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';

@Catch()
export class UploadErrorFilter {
  async catch(err: Error, ctx: Context) {
    // 检查是否是文件上传相关的错误
    if (this.isUploadError(err)) {
      console.log('📁 文件上传错误:', {
        errorName: err.name,
        errorMessage: err.message,
      });

      ctx.logger.error('文件上传错误:', err);

      // 根据错误类型返回相应的错误信息
      const errorMessage = this.getUploadErrorMessage(err);

      return {
        errCode: 400,
        msg: errorMessage,
      };
    }

    // 不是文件上传错误，继续抛出让其他过滤器处理
    throw err;
  }

  /**
   * 判断是否是文件上传相关的错误
   */
  private isUploadError(error: Error): boolean {
    // 只检查明确的文件上传错误类型
    const uploadErrorTypes = [
      'MulterInvalidFilenameError',
      'MulterError',
      'BusboyError',
      'FileUploadError',
    ];

    return uploadErrorTypes.includes(error.name);
  }

  /**
   * 根据错误类型获取友好的错误消息
   */
  private getUploadErrorMessage(error: Error): string {
    const errorMessage = error.message || '';

    // 文件名或格式错误
    if (
      error.name === 'MulterInvalidFilenameError' ||
      errorMessage.includes('Invalid file name or suffix')
    ) {
      return '文件格式不支持，请上传正确格式的文件';
    }

    // 文件大小错误
    if (
      errorMessage.includes('file size') ||
      errorMessage.includes('too large')
    ) {
      return '文件大小超过限制，请上传较小的文件';
    }

    // 文件类型错误
    if (
      errorMessage.includes('file type') ||
      errorMessage.includes('whitelist')
    ) {
      return '文件类型不支持，请检查文件格式';
    }

    // 上传失败
    if (errorMessage.includes('upload') || errorMessage.includes('busboy')) {
      return '文件上传失败，请重试';
    }

    // 默认错误消息
    return `文件处理失败: ${errorMessage}`;
  }
}
