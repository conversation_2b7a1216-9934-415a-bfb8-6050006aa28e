import { Provide, Inject } from '@midwayjs/core';
import { ModelCtor } from 'sequelize-typescript';
import { Op } from 'sequelize';
import { HistoricalElement } from '../entity/historical-element.entity';
import { PageQueryDTO, PageResponseDTO } from '../dto/common.dto';
import {
  CreateHistoricalElementDTO,
  UpdateHistoricalElementDTO,
} from '../dto/entity.dto';
import { CacheService } from './cache.service';
import { PhotoService } from './photo.service';
import { BaseService } from '../common/BaseService';
import { RegionDict, TypeDict } from '../entity';

@Provide()
export class HistoricalElementService extends BaseService<HistoricalElement> {
  @Inject()
  cacheService: CacheService;

  @Inject()
  photoService: PhotoService;

  constructor() {
    super('历史要素');
  }

  protected getModel(): ModelCtor<HistoricalElement> {
    return HistoricalElement;
  }

  /**
   * 创建历史要素（业务逻辑封装）
   */
  async createHistoricalElement(
    createDto: CreateHistoricalElementDTO
  ): Promise<HistoricalElement> {
    await this.validateHistoricalElementData(createDto);
    return await this.create(createDto as any);
  }

  /**
   * 更新历史要素（业务逻辑封装）
   */
  async updateHistoricalElement(
    id: number,
    updateDto: UpdateHistoricalElementDTO
  ): Promise<HistoricalElement> {
    await this.validateHistoricalElementData(updateDto, id);
    await this.update({ id }, updateDto as any);
    return (await this.findById(id)) as HistoricalElement;
  }

  /**
   * 删除历史要素（业务逻辑封装）
   * @param id 历史要素ID
   * @param deletePhotos 是否同时删除关联的照片，默认为false（外键设置为SET NULL）
   */
  async deleteHistoricalElement(
    id: number,
    deletePhotos = false
  ): Promise<void> {
    // 检查历史要素是否存在
    const element = await this.findById(id);
    if (!element) {
      throw new Error('历史要素不存在');
    }

    try {
      // 如果用户指定要删除照片，则先删除关联的照片记录
      if (deletePhotos) {
        await this.deleteRelatedPhotos(id);
      }

      // 删除历史要素本身
      // 如果deletePhotos为false，外键会被设置为NULL（需要数据库层面配置ON DELETE SET NULL）
      await this.delete({ id });
    } catch (error) {
      throw new Error(`删除历史要素失败: ${error.message}`);
    }
  }

  /**
   * 删除历史要素关联的所有照片
   * @param historicalElementId 历史要素ID
   */
  private async deleteRelatedPhotos(
    historicalElementId: number
  ): Promise<void> {
    try {
      // 获取所有关联的照片
      const photos = await this.photoService.findPhotosByEntity(
        'historicalElement',
        historicalElementId
      );

      // 逐个删除照片（包括物理文件）
      for (const photo of photos) {
        await this.photoService.deletePhoto(photo.id);
      }

      console.log(
        `已删除历史要素 ${historicalElementId} 的 ${photos.length} 张关联照片`
      );
    } catch (error) {
      // 照片删除失败不能阻止历史要素的删除
      // 但仍需记录错误信息，方便后续排查
      console.error(
        `删除历史要素 ${historicalElementId} 的关联照片失败:`,
        error
      );
    }
  }

  /**
   * 批量删除历史要素
   * @param ids 历史要素ID数组
   * @param deletePhotos 是否同时删除关联的照片，默认为false
   */
  async batchDeleteHistoricalElements(
    ids: number[],
    deletePhotos = false
  ): Promise<void> {
    if (!ids || ids.length === 0) {
      throw new Error('请提供要删除的历史要素ID');
    }

    // 检查所有历史要素是否存在
    const elements = await this.findAll({
      query: { id: { [Op.in]: ids } },
    });

    const existingIds = elements.list.map(e => e.id);
    const notFoundIds = ids.filter(id => !existingIds.includes(id));

    if (notFoundIds.length > 0) {
      throw new Error(`以下历史要素不存在: ${notFoundIds.join(', ')}`);
    }

    try {
      // 如果用户指定要删除照片，则先删除关联的照片记录
      if (deletePhotos) {
        for (const id of ids) {
          await this.deleteRelatedPhotos(id);
        }
      }

      // 批量删除历史要素
      await this.delete({ id: { [Op.in]: ids } });
    } catch (error) {
      throw new Error(`批量删除历史要素失败: ${error.message}`);
    }
  }

  /**
   * 分页查询历史要素列表
   */
  async findList(
    query: PageQueryDTO & { typeId?: number }
  ): Promise<PageResponseDTO<HistoricalElement>> {
    const { page, pageSize, keyword, regionId, typeId } = query;
    const offset = (page - 1) * pageSize;

    const whereConditions: any = {};

    if (keyword) {
      whereConditions.name = { [Symbol.for('like')]: `%${keyword}%` };
    }

    if (regionId) {
      whereConditions.regionDictId = regionId;
    }

    if (typeId) {
      whereConditions.typeDictId = typeId;
    }

    const result = await this.findAll({
      query: whereConditions,
      offset,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
    });

    return new PageResponseDTO(result.list, result.total || 0, page, pageSize);
  }

  /**
   * 根据编号查找历史要素
   */
  async findByCode(code: string): Promise<HistoricalElement | null> {
    const result = await this.findAll({
      query: { code } as any,
      limit: 1,
    });
    return result.list.length > 0 ? result.list[0] : null;
  }

  /**
   * 根据类型获取历史要素列表
   */
  async findByType(typeId: number): Promise<HistoricalElement[]> {
    const result = await this.findAll({
      query: { typeDictId: typeId } as any,
      order: [['name', 'ASC']],
    });
    return result.list;
  }

  /**
   * 根据区域获取历史要素列表
   */
  async findByRegion(regionId: number): Promise<HistoricalElement[]> {
    const result = await this.findAll({
      query: { regionDictId: regionId } as any,
      order: [['name', 'ASC']],
    });
    return result.list;
  }

  /**
   * 根据建造年份范围查询
   */
  async findByConstructionYear(
    startYear?: number,
    endYear?: number
  ): Promise<HistoricalElement[]> {
    const whereConditions: any = {};

    if (startYear !== undefined) {
      whereConditions.constructionYear = { [Symbol.for('gte')]: startYear };
    }

    if (endYear !== undefined) {
      if (whereConditions.constructionYear) {
        whereConditions.constructionYear[Symbol.for('lte')] = endYear;
      } else {
        whereConditions.constructionYear = { [Symbol.for('lte')]: endYear };
      }
    }

    const result = await this.findAll({
      query: whereConditions,
      order: [['constructionYear', 'ASC']],
    });
    return result.list;
  }

  /**
   * 获取历史要素统计数据
   */
  async getStatistics(
    regionId?: number,
    typeId?: number
  ): Promise<{
    total: number;
    byType: Array<{ typeId: number; typeName: string; count: number }>;
    byRegion: Array<{ regionId: number; regionName: string; count: number }>;
    byPeriod: Array<{ period: string; count: number }>;
  }> {
    const whereConditions: any = {};

    if (regionId) {
      whereConditions.regionDictId = regionId;
    }

    if (typeId) {
      whereConditions.typeDictId = typeId;
    }

    // 获取所有历史要素数据，包含关联的类型和区域信息
    const result = await this.findAll({
      query: whereConditions,
      include: [
        { model: TypeDict, as: 'typeDict' },
        { model: RegionDict, as: 'regionDict' },
      ],
    });
    const elements = result.list;
    // 由于没有分页参数，result.total 为 undefined，需要手动计算总数
    const total = elements.length;

    // 按类型统计
    const typeMap = new Map<
      number,
      { typeId: number; typeName: string; count: number }
    >();
    // 按区域统计
    const regionMap = new Map<
      number,
      { regionId: number; regionName: string; count: number }
    >();
    // 按时期统计
    const periodMap = new Map<string, number>();

    elements.forEach(element => {
      // 统计类型
      if (element.typeDictId && element.typeDict) {
        const typeId = element.typeDictId;
        if (typeMap.has(typeId)) {
          typeMap.get(typeId)!.count++;
        } else {
          typeMap.set(typeId, {
            typeId,
            typeName: element.typeDict.typeName,
            count: 1,
          });
        }
      }

      // 统计区域
      if (element.regionDictId && element.regionDict) {
        const regionId = element.regionDictId;
        if (regionMap.has(regionId)) {
          regionMap.get(regionId)!.count++;
        } else {
          regionMap.set(regionId, {
            regionId,
            regionName: element.regionDict.regionName,
            count: 1,
          });
        }
      }

      // 统计时期（按朝代）
      if (
        element.constructionYear !== undefined &&
        element.constructionYear !== null
      ) {
        const period = this.getPeriodByYear(element.constructionYear);
        periodMap.set(period, (periodMap.get(period) || 0) + 1);
      }
    });

    const byType = Array.from(typeMap.values()).sort(
      (a, b) => b.count - a.count
    );
    const byRegion = Array.from(regionMap.values()).sort(
      (a, b) => b.count - a.count
    );
    const byPeriod = Array.from(periodMap.entries())
      .map(([period, count]) => ({
        period,
        count,
      }))
      .sort((a, b) => b.count - a.count);

    return {
      total,
      byType,
      byRegion,
      byPeriod,
    };
  }

  /**
   * 根据年份获取历史时期
   */
  private getPeriodByYear(year: number): string {
    if (year < 221) return '先秦';
    if (year < 206) return '秦朝';
    if (year < 220) return '汉朝';
    if (year < 280) return '三国';
    if (year < 420) return '晋朝';
    if (year < 589) return '南北朝';
    if (year < 618) return '隋朝';
    if (year < 907) return '唐朝';
    if (year < 960) return '五代十国';
    if (year < 1127) return '北宋';
    if (year < 1279) return '南宋';
    if (year < 1368) return '元朝';
    if (year < 1644) return '明朝';
    if (year < 1912) return '清朝';
    if (year < 1949) return '民国';
    return '现代';
  }

  /**
   * 获取时间轴数据
   */
  async getTimelineData(regionId?: number): Promise<
    Array<{
      year: number;
      count: number;
      elements: Array<{ id: number; name: string; type: string }>;
    }>
  > {
    const whereConditions: any = {
      constructionYear: { [Symbol.for('ne')]: null },
    };

    if (regionId) {
      whereConditions.regionDictId = regionId;
    }

    const result = await this.findAll({ query: whereConditions });
    const elements = result.list;

    // 按年份分组
    const timelineMap = new Map<
      number,
      Array<{ id: number; name: string; type: string }>
    >();

    elements.forEach(element => {
      if (
        element.constructionYear !== undefined &&
        element.constructionYear !== null
      ) {
        const year = element.constructionYear;
        if (!timelineMap.has(year)) {
          timelineMap.set(year, []);
        }
        timelineMap.get(year)!.push({
          id: element.id,
          name: element.name,
          type: 'historical_element',
        });
      }
    });

    return Array.from(timelineMap.entries()).map(([year, elements]) => ({
      year,
      count: elements.length,
      elements,
    }));
  }

  /**
   * 批量导入历史要素数据
   */
  async batchImportElements(
    elements: CreateHistoricalElementDTO[]
  ): Promise<void> {
    // 批量验证数据
    for (const element of elements) {
      await this.validateHistoricalElementData(element);
    }

    await this.batchCreate(elements as any);
  }

  /**
   * 格式化年份显示
   * @param year 年份（负数表示公元前）
   * @returns 格式化的年份字符串
   */
  formatYear(year: number): string {
    if (year < 0) {
      return `公元前${Math.abs(year)}年`;
    } else {
      return `公元${year}年`;
    }
  }

  /**
   * 解析年份字符串
   * @param yearStr 年份字符串（如："公元前221年"、"公元652年"、"-221"、"652"）
   * @returns 年份数字（负数表示公元前）
   */
  parseYear(yearStr: string): number | null {
    if (!yearStr || typeof yearStr !== 'string') {
      return null;
    }

    const trimmed = yearStr.trim();

    // 处理纯数字格式
    const numericMatch = trimmed.match(/^-?\d+$/);
    if (numericMatch) {
      return parseInt(trimmed, 10);
    }

    // 处理中文格式
    const bcMatch = trimmed.match(/^公元前(\d+)年?$/);
    if (bcMatch) {
      return -parseInt(bcMatch[1], 10);
    }

    const adMatch = trimmed.match(/^公元(\d+)年?$/);
    if (adMatch) {
      return parseInt(adMatch[1], 10);
    }

    return null;
  }

  /**
   * 验证历史要素数据
   */
  private async validateHistoricalElementData(
    data: CreateHistoricalElementDTO | UpdateHistoricalElementDTO,
    excludeId?: number
  ): Promise<void> {
    if (!data.name || data.name.trim().length === 0) {
      throw new Error('历史要素名称不能为空');
    }

    if (!data.code || data.code.trim().length === 0) {
      throw new Error('历史要素编号不能为空');
    }

    // 验证编号唯一性
    const existingElement = await this.findByCode(data.code.trim());
    if (existingElement && (!excludeId || existingElement.id !== excludeId)) {
      throw new Error(`编号 "${data.code}" 已存在，请使用其他编号`);
    }

    if (data.constructionLongitude) {
      // 验证经纬度范围
      if (
        data.constructionLongitude < -180 ||
        data.constructionLongitude > 180
      ) {
        throw new Error('建筑经度范围应在-180到180之间');
      }
    }

    if (data.constructionLatitude) {
      // 验证经纬度范围
      if (data.constructionLatitude < -90 || data.constructionLatitude > 90) {
        throw new Error('建筑纬度范围应在-90到90之间');
      }
    }

    // 验证建造年份
    if (data.constructionYear !== undefined && data.constructionYear !== null) {
      const currentYear = new Date().getFullYear();

      if (data.constructionYear > currentYear) {
        throw new Error('建造年份不能晚于当前年份');
      }

      // 验证年份是否合理（不能早于公元前5000年）
      if (data.constructionYear < -5000) {
        throw new Error('建造年份不能早于公元前5000年');
      }

      // 验证年份不能为0（历史上没有公元0年）
      if (data.constructionYear === 0) {
        throw new Error('年份不能为0，请使用公元前1年（-1）或公元1年（1）');
      }
    }
  }
}
