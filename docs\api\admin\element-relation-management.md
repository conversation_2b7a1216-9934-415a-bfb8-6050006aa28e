# 要素关联管理 API 文档

## 概述

要素关联管理模块提供山塬、水系、历史要素之间关联关系的完整管理功能，支持要素与要素、要素与类别的关联，包括创建、更新、删除、查询、统计分析和网络图数据生成等操作。

## 相关文档

- [水系管理 API 文档](./water-system-management.md) - 水系管理功能详细说明
- [山塬管理 API 文档](./mountain-management.md) - 山塬管理功能详细说明
- [历史要素管理 API 文档](./historical-element-management.md) - 历史要素管理功能详细说明

---

## 统一响应格式

### 成功响应
所有接口成功时返回统一格式：
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应
所有接口失败时返回统一格式：
```json
{
  "errCode": 400,  // 错误码
  "msg": "错误信息"  // 错误描述
}
```

---

## 创建要素关联

### 接口信息

- **URL**: `/admin/relationship`
- **方法**: `POST`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| relationDictId | number | 否 | 关系类型ID |
| parentRelationshipId | number | 否 | 父级关系ID |
| sourceType | string | 是 | 源要素类型：mountain, water_system, historical_element |
| sourceId | number | 是 | 源要素ID |
| targetType | string | 是 | 目标类型：element（具体要素）, category（类别） |
| targetEntityType | string | 否 | 目标要素类型：mountain, water_system, historical_element, type_dict, region_dict |
| targetId | number | 是 | 目标要素ID |
| direction | string | 否 | 关联方向，最大50字符，如：前有、后有、上有、下有、东连、西连等 |
| term | string | 否 | 词条描述，最大255字符 |
| record | string | 否 | 记载内容 |
| sort | number | 否 | 排序号 |
| status | number | 否 | 状态（1启用，0禁用），默认1 |

### 请求示例

```bash
curl -X POST "http://localhost:7001/admin/relationship" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "sourceType": "historical_element",
    "sourceId": 1,
    "targetType": "element",
    "targetEntityType": "historical_element",
    "targetId": 2,
    "relationDictId": 1,
    "direction": "前有",
    "term": "前有",
    "record": "前有飞楼",
    "sort": 1
  }'
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "relationDictId": 1,
    "parentRelationshipId": null,
    "sourceType": "historical_element",
    "sourceId": 1,
    "targetType": "element",
    "targetEntityType": "historical_element",
    "targetId": 2,
    "direction": "前有",
    "term": "前有",
    "record": "前有飞楼",
    "sort": 1,
    "status": 1,
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:00:00.000Z",
    "relationDict": {
      "id": 1,
      "relationName": "选址关联",
      "relationCode": "SITE_RELATION"
    },
    "sourceElement": {
      "id": 1,
      "name": "齐云洞",
      "code": "QYD001"
    },
    "targetElement": {
      "id": 2,
      "name": "亭台楼阁",
      "code": "TTLG001"
    }
  }
}
```

---

## 更新要素关联

### 接口信息

- **URL**: `/admin/relationship/{id}`
- **方法**: `PUT`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 要素关联ID |

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| relationDictId | number | 否 | 关系类型ID |
| parentRelationshipId | number | 否 | 父级关系ID |
| sourceType | string | 否 | 源要素类型：mountain, water_system, historical_element |
| sourceId | number | 否 | 源要素ID |
| targetType | string | 否 | 目标类型：element（具体要素）, category（类别） |
| targetEntityType | string | 否 | 目标要素类型：mountain, water_system, historical_element, type_dict, region_dict |
| targetId | number | 否 | 目标要素ID |
| direction | string | 否 | 关联方向，最大50字符，如：前有、后有、上有、下有、东连、西连等 |
| term | string | 否 | 词条描述，最大255字符 |
| record | string | 否 | 记载内容 |
| sort | number | 否 | 排序号 |
| status | number | 否 | 状态（1启用，0禁用） |

### 请求示例

```bash
curl -X PUT "http://localhost:7001/admin/relationship/1" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "direction": "后有",
    "term": "后有",
    "record": "后有飞楼，对梅杨柳"
  }'
```

---

## 删除要素关联

### 接口信息

- **URL**: `/admin/relationship/{id}`
- **方法**: `DELETE`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 要素关联ID |

### 请求示例

```bash
curl -X DELETE "http://localhost:7001/admin/relationship/1" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "message": "删除成功"
  }
}
```

---

## 获取要素关联列表

### 接口信息

- **URL**: `/admin/relationship`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，从1开始，默认1 |
| pageSize | number | 否 | 每页数量，1-100，默认10 |
| keyword | string | 否 | 搜索关键词，按词条、记载模糊搜索 |
| relationDictId | number | 否 | 关系类型ID筛选 |
| parentRelationshipId | number | 否 | 父级关系ID筛选 |
| sourceType | string | 否 | 源要素类型筛选 |
| sourceId | number | 否 | 源要素ID筛选 |
| targetType | string | 否 | 目标类型筛选 |
| targetEntityType | string | 否 | 目标要素类型筛选 |
| targetId | number | 否 | 目标要素ID筛选 |
| direction | string | 否 | 方向筛选 |
| status | number | 否 | 状态筛选 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/relationship?page=1&pageSize=10&keyword=齐云洞" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "齐云洞-亭台楼阁关联",
        "code": "REL001",
        "sourceType": "historical_element",
        "sourceId": 1,
        "targetType": "element",
        "targetEntityType": "historical_element",
        "targetId": 2,
        "relationDictId": 1,
        "direction": "前有",
        "term": "前有",
        "record": "前有飞楼",
        "sort": 1,
        "status": 1,
        "relationDict": {
          "id": 1,
          "relationName": "选址关联",
          "relationCode": "SITE_RELATION"
        },
        "sourceElement": {
          "id": 1,
          "name": "齐云洞",
          "code": "QYD001"
        },
        "targetElement": {
          "id": 2,
          "name": "亭台楼阁",
          "code": "TTLG001"
        }
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  }
}
```

---

## 获取所有要素关联（不分页）

### 接口信息

- **URL**: `/admin/relationship/all`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| relationDictId | number | 否 | 关系类型ID筛选 |
| sourceType | string | 否 | 源要素类型筛选 |
| sourceId | number | 否 | 源要素ID筛选 |
| targetType | string | 否 | 目标类型筛选 |
| targetEntityType | string | 否 | 目标要素类型筛选 |
| targetId | number | 否 | 目标要素ID筛选 |
| direction | string | 否 | 方向筛选 |
| status | number | 否 | 状态筛选 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/relationship/all?status=1" \
  -H "Authorization: Bearer {token}"
```

---

## 获取要素关联详情

### 接口信息

- **URL**: `/admin/relationship/{id}`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 要素关联ID |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/relationship/1" \
  -H "Authorization: Bearer {token}"
```

---

## 批量创建要素关联

### 接口信息

- **URL**: `/admin/relationship/batch`
- **方法**: `POST`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| relations | array | 是 | 要素关联数据数组，每个元素包含创建要素关联所需的所有字段 |

### 请求示例

```bash
curl -X POST "http://localhost:7001/admin/relationship/batch" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "relations": [
      {
        "name": "齐云洞-白水关联",
        "code": "REL002",
        "sourceType": "historical_element",
        "sourceId": 1,
        "targetType": "element",
        "targetEntityType": "water_system",
        "targetId": 1,
        "relationDictId": 1,
        "direction": "下有",
        "term": "下有",
        "record": "下有白水"
      },
      {
        "name": "白水-源和山关联",
        "code": "REL003",
        "sourceType": "water_system",
        "sourceId": 1,
        "targetType": "element",
        "targetEntityType": "mountain",
        "targetId": 1,
        "relationDictId": 1,
        "direction": "绕其后",
        "term": "绕其后",
        "record": "白水绕其后"
      }
    ]
  }'
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "message": "批量创建成功"
  }
}
```

---

## 根据要素获取关联关系

### 接口信息

- **URL**: `/admin/relationship/by-element/{elementType}/{elementId}`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| elementType | string | 是 | 要素类型：mountain, water_system, historical_element |
| elementId | number | 是 | 要素ID |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/relationship/by-element/historical_element/1" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "name": "齐云洞-亭台楼阁关联",
      "sourceType": "historical_element",
      "sourceId": 1,
      "targetType": "element",
      "targetEntityType": "historical_element",
      "targetId": 2,
      "direction": "前有",
      "term": "前有",
      "record": "前有飞楼",
      "relationDict": {
        "id": 1,
        "relationName": "选址关联"
      },
      "sourceElement": {
        "id": 1,
        "name": "齐云洞"
      },
      "targetElement": {
        "id": 2,
        "name": "亭台楼阁"
      }
    }
  ]
}
```

---

## 获取要素关联统计

### 接口信息

- **URL**: `/admin/relationship/statistics/overview`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sourceType | string | 否 | 源要素类型筛选 |
| targetType | string | 否 | 目标类型筛选 |
| relationDictId | number | 否 | 关系类型ID筛选 |
| status | number | 否 | 状态筛选 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/relationship/statistics/overview" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "total": 25,
    "bySourceType": [
      {
        "sourceType": "historical_element",
        "count": 15
      },
      {
        "sourceType": "water_system",
        "count": 6
      },
      {
        "sourceType": "mountain",
        "count": 4
      }
    ],
    "byTargetType": [
      {
        "targetType": "element",
        "count": 20
      },
      {
        "targetType": "category",
        "count": 5
      }
    ],
    "byRelationType": [
      {
        "relationId": 1,
        "relationName": "选址关联",
        "count": 12
      },
      {
        "relationId": 2,
        "relationName": "视线关联",
        "count": 8
      },
      {
        "relationId": 3,
        "relationName": "功能关联",
        "count": 5
      }
    ],
    "byDirection": [
      {
        "direction": "前有",
        "count": 8
      },
      {
        "direction": "后有",
        "count": 6
      },
      {
        "direction": "上有",
        "count": 4
      },
      {
        "direction": "下有",
        "count": 3
      },
      {
        "direction": "东连",
        "count": 2
      },
      {
        "direction": "西连",
        "count": 2
      }
    ]
  }
}
```

---

## 获取网络图数据

### 接口信息

- **URL**: `/admin/relationship/network-graph`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sourceType | string | 否 | 源要素类型筛选 |
| targetType | string | 否 | 目标类型筛选 |
| relationDictId | number | 否 | 关系类型ID筛选 |
| status | number | 否 | 状态筛选 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/relationship/network-graph" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "nodes": [
      {
        "id": "historical_element_1",
        "name": "齐云洞",
        "type": "historical_element",
        "category": "历史要素",
        "size": 10,
        "color": "#DC143C"
      },
      {
        "id": "water_system_1",
        "name": "白水",
        "type": "water_system",
        "category": "水系",
        "size": 10,
        "color": "#4169E1"
      },
      {
        "id": "mountain_1",
        "name": "源和山",
        "type": "mountain",
        "category": "山塬",
        "size": 10,
        "color": "#8B4513"
      }
    ],
    "links": [
      {
        "source": "historical_element_1",
        "target": "water_system_1",
        "relation": "选址关联",
        "direction": "下有",
        "term": "下有",
        "weight": 1,
        "color": "#4169E1"
      },
      {
        "source": "water_system_1",
        "target": "mountain_1",
        "relation": "选址关联",
        "direction": "绕其后",
        "term": "绕其后",
        "weight": 1,
        "color": "#4169E1"
      }
    ],
    "categories": ["历史要素", "水系", "山塬"]
  }
}
```

### 响应字段说明

#### 节点（nodes）字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | string | 节点唯一标识，格式：{elementType}_{elementId} |
| name | string | 节点名称 |
| type | string | 节点类型 |
| category | string | 节点分类 |
| size | number | 节点大小 |
| color | string | 节点颜色 |

#### 连线（links）字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| source | string | 源节点ID |
| target | string | 目标节点ID |
| relation | string | 关系名称 |
| direction | string | 关联方向 |
| term | string | 词条描述 |
| weight | number | 连线权重 |
| color | string | 连线颜色 |

---

## 批量更新状态

### 接口信息

- **URL**: `/admin/relationship/batch-status`
- **方法**: `PUT`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | array | 是 | 要素关联ID数组 |
| status | number | 是 | 状态（1启用，0禁用） |

### 请求示例

```bash
curl -X PUT "http://localhost:7001/admin/relationship/batch-status" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [1, 2, 3],
    "status": 0
  }'
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "message": "批量更新成功"
  }
}
```

---

## 公开接口

### 获取网络图数据（公开）

- **URL**: `/openapi/relationship/network-graph`
- **方法**: `GET`
- **认证**: 无需认证

### 获取要素关联统计（公开）

- **URL**: `/openapi/relationship/statistics`
- **方法**: `GET`
- **认证**: 无需认证

### 根据要素获取关联关系（公开）

- **URL**: `/openapi/relationship/by-element/{elementType}/{elementId}`
- **方法**: `GET`
- **认证**: 无需认证

### 搜索要素关联（公开）

- **URL**: `/openapi/relationship/search?keyword={keyword}`
- **方法**: `GET`
- **认证**: 无需认证

### 获取关系列表（公开）

- **URL**: `/openapi/relationship/list`
- **方法**: `GET`
- **认证**: 无需认证

### 根据关系类型获取关联关系（公开）

- **URL**: `/openapi/relationship/by-relation/{relationId}`
- **方法**: `GET`
- **认证**: 无需认证

### 根据方向获取关联关系（公开）

- **URL**: `/openapi/relationship/by-direction/{direction}`
- **方法**: `GET`
- **认证**: 无需认证

---

## 数据字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 要素关联唯一标识 |
| relationDictId | number | 关系类型ID |
| parentRelationshipId | number | 父级关系ID |
| sourceType | string | 源要素类型 |
| sourceId | number | 源要素ID |
| targetType | string | 目标类型 |
| targetEntityType | string | 目标要素类型 |
| targetId | number | 目标要素ID |
| direction | string | 关联方向 |
| term | string | 词条描述 |
| record | string | 记载内容 |
| sort | number | 排序号 |
| status | number | 状态 |
| relationDict | object | 关系类型详细信息 |
| parent | object | 父级关系信息 |
| children | array | 子级关系信息 |
| sourceElement | object | 源要素详细信息 |
| targetElement | object | 目标要素详细信息 |
| createdAt | string | 创建时间 |
| updatedAt | string | 更新时间 |

---

## 要素类型说明

### 源要素类型（sourceType）

- `mountain` - 山塬
- `water_system` - 水系
- `historical_element` - 历史要素

### 目标类型（targetType）

- `element` - 具体要素（指向具体的山塬、水系、历史要素）
- `category` - 类别（指向类型字典或区域字典）

### 目标要素类型（targetEntityType）

- `mountain` - 山塬
- `water_system` - 水系
- `historical_element` - 历史要素
- `type_dict` - 类型字典
- `region_dict` - 区域字典

---

## 关联方向示例

根据截图数据，常见的关联方向包括：

- **空间位置关系**：前有、后有、上有、下有、左有、右有
- **连接关系**：东连、西连、南连、北连、相连
- **包含关系**：内有、外有、中有
- **功能关系**：通向、流向、面向
- **时间关系**：先有、后建、同期
- **视觉关系**：可见、遥望、俯视、仰视

---

## 网络图颜色说明

### 节点颜色

- **山塬**：#8B4513（棕色）
- **水系**：#4169E1（蓝色）
- **历史要素**：#DC143C（红色）
- **类型**：#32CD32（绿色）
- **区域**：#FFD700（金色）

### 连线颜色

- **空间关系**：#4169E1（蓝色）
- **视线关系**：#9932CC（紫色）
- **历史关系**：#DC143C（红色）
- **功能关系**：#32CD32（绿色）
- **其他关系**：#808080（灰色）

---

## 注意事项

1. **要素验证**: 创建关联前会验证源要素和目标要素是否存在
2. **关系类型**: 关系类型ID（relationDictId）为可选，可以关联到已存在的关系字典项
3. **层级关系**: 支持父子级关系（parentRelationshipId），可以构建层级化的关联关系
4. **双向关联**: 如需双向关联，需要创建两条记录
5. **删除影响**: 删除要素关联不会影响关联的要素本身
6. **批量操作**: 支持批量创建和批量状态更新
7. **权限控制**: 管理接口需要管理员权限，公开接口无需认证
8. **数据过滤**: 公开接口只返回启用状态（status=1）的关联关系
9. **网络图**: 适用于可视化展示要素间的复杂关联关系
10. **性能优化**: 大量数据时建议使用筛选条件限制返回结果
11. **字段长度**: direction字段最大50字符，term字段最大255字符
12. **状态管理**: status字段只能为0（禁用）或1（启用），默认为1
