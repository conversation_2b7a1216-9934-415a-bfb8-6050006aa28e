# 数字化统计接口详细说明

## 接口概述
数字化统计接口提供系统的各类统计分析数据，支持数据可视化和决策分析。

## 1. 基础统计数据

### 接口地址
`GET /openapi/statistics/basic`

### 功能说明
获取系统的基础统计信息，包括各类数据的数量统计、区域分布和时间轴数据。

### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| regionId | number | 否 | - | 区域ID筛选 |
| startTime | string | 否 | - | 开始时间（ISO格式） |
| endTime | string | 否 | - | 结束时间（ISO格式） |

### 响应示例
```json
{
  "errCode": 0,
  "data": {
    "counts": {
      "mountain": 150,
      "waterSystem": 80,
      "historicalElement": 200,
      "user": 50,
      "typeDict": 25,
      "regionDict": 12,
      "relationshipDict": 30
    },
    "regionStats": [
      {
        "region": "西安市",
        "regionId": 1,
        "mountainCount": 50,
        "waterSystemCount": 30,
        "historicalElementCount": 80,
        "total": 160
      }
    ],
    "timelineData": [
      {
        "year": 618,
        "elements": [
          {
            "id": 1,
            "name": "大雁塔",
            "type": "historicalElement"
          }
        ]
      }
    ]
  },
  "msg": "OK"
}
```

## 2. 综合统计报告

### 接口地址
`GET /openapi/statistics/comprehensive`

### 功能说明
获取更详细的综合统计报告，包含深度分析数据。

### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| regionId | number | 否 | - | 区域ID筛选 |

## 3. 时间轴数据

### 接口地址
`GET /openapi/statistics/timeline`

### 功能说明
获取历史要素按时间分布的时间轴数据，用于时间维度的数据分析。

### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| regionId | number | 否 | - | 区域ID筛选 |

### 响应示例
```json
{
  "errCode": 0,
  "data": [
    {
      "year": 618,
      "elements": [
        {
          "id": 1,
          "name": "大雁塔",
          "type": "historicalElement"
        },
        {
          "id": 2,
          "name": "小雁塔",
          "type": "historicalElement"
        }
      ]
    },
    {
      "year": 1370,
      "elements": [
        {
          "id": 3,
          "name": "西安城墙",
          "type": "historicalElement"
        }
      ]
    }
  ],
  "msg": "OK"
}
```

## 4. 区域分布统计

### 接口地址
`GET /openapi/statistics/region-distribution`

### 功能说明
获取各区域的数据分布统计，用于区域维度的数据分析。

### 响应示例
```json
{
  "errCode": 0,
  "data": [
    {
      "region": "西安市",
      "regionId": 1,
      "mountainCount": 50,
      "waterSystemCount": 30,
      "historicalElementCount": 80,
      "total": 160
    },
    {
      "region": "宝鸡市",
      "regionId": 2,
      "mountainCount": 30,
      "waterSystemCount": 20,
      "historicalElementCount": 40,
      "total": 90
    }
  ],
  "msg": "OK"
}
```

## 5. 数据概览

### 接口地址
`GET /openapi/statistics/overview`

### 功能说明
获取数据概览信息，整合了总体统计、区域分布和时间轴数据。

### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| regionId | number | 否 | - | 区域ID筛选 |

### 响应示例
```json
{
  "errCode": 0,
  "data": {
    "totalCounts": {
      "mountain": 150,
      "waterSystem": 80,
      "historicalElement": 200,
      "user": 50,
      "typeDict": 25,
      "regionDict": 12,
      "relationshipDict": 30
    },
    "regionStats": [...],
    "timelineData": [...]
  },
  "msg": "OK"
}
```

## 使用示例

### 获取基础统计数据
```javascript
// 获取全部统计数据
fetch('/openapi/statistics/basic')
  .then(response => response.json())
  .then(data => {
    if (data.errCode === 0) {
      const { counts, regionStats, timelineData } = data.data;
      
      // 显示总体统计
      console.log('数据统计:', counts);
      
      // 显示区域分布
      regionStats.forEach(region => {
        console.log(`${region.region}: 总计${region.total}个实体`);
      });
      
      // 显示时间轴数据
      timelineData.forEach(timeline => {
        console.log(`${timeline.year}年: ${timeline.elements.length}个历史要素`);
      });
    }
  });

// 获取特定区域统计数据
fetch('/openapi/statistics/basic?regionId=1')
  .then(response => response.json())
  .then(data => {
    console.log('西安市统计数据:', data.data);
  });

// 获取特定时间范围统计数据
const startTime = '2023-01-01T00:00:00.000Z';
const endTime = '2023-12-31T23:59:59.999Z';
fetch(`/openapi/statistics/basic?startTime=${startTime}&endTime=${endTime}`)
  .then(response => response.json())
  .then(data => {
    console.log('2023年统计数据:', data.data);
  });
```

### 获取时间轴数据用于可视化
```javascript
fetch('/openapi/statistics/timeline')
  .then(response => response.json())
  .then(data => {
    if (data.errCode === 0) {
      const timelineData = data.data;
      
      // 转换为图表数据格式
      const chartData = timelineData.map(item => ({
        year: item.year,
        count: item.elements.length,
        elements: item.elements
      }));
      
      // 使用图表库渲染时间轴
      renderTimelineChart(chartData);
    }
  });
```

### 获取区域分布数据用于地图可视化
```javascript
fetch('/openapi/statistics/region-distribution')
  .then(response => response.json())
  .then(data => {
    if (data.errCode === 0) {
      const regionData = data.data;
      
      // 转换为地图数据格式
      const mapData = regionData.map(region => ({
        name: region.region,
        value: region.total,
        details: {
          mountain: region.mountainCount,
          waterSystem: region.waterSystemCount,
          historicalElement: region.historicalElementCount
        }
      }));
      
      // 使用地图库渲染区域分布
      renderRegionMap(mapData);
    }
  });
```

## 数据分析建议

### 1. 时间维度分析
- 使用时间轴数据分析历史要素的时间分布特征
- 识别历史发展的重要时期和节点
- 分析不同历史时期的文化特色

### 2. 空间维度分析
- 使用区域分布数据分析地理空间特征
- 识别文化资源的空间聚集模式
- 分析区域间的文化差异和联系

### 3. 类型维度分析
- 分析不同类型实体的数量分布
- 识别主要的文化资源类型
- 分析类型间的关联关系

### 4. 综合分析
- 结合时间、空间、类型三个维度进行综合分析
- 识别文化资源的整体特征和发展趋势
- 为文化保护和开发提供数据支撑
