import { Controller, Get, Query, Inject } from '@midwayjs/core';
import { MountainService } from '../../service/mountain.service';
import { WaterSystemService } from '../../service/water-system.service';
import { HistoricalElementService } from '../../service/historical-element.service';

@Controller('/openapi')
export class PublicSearchController {
  @Inject()
  mountainService: MountainService;

  @Inject()
  waterSystemService: WaterSystemService;

  @Inject()
  historicalElementService: HistoricalElementService;

  @Get('/search')
  async search(
    @Query('keyword') keyword: string,
    @Query('type')
    type: 'mountain' | 'waterSystem' | 'historicalElement' | 'all' = 'all',
    @Query('page') page = 1,
    @Query('pageSize') pageSize = 10
  ) {
    if (!keyword || keyword.trim().length === 0) {
      return { list: [], total: 0, page, pageSize };
    }

    const kw = keyword.trim();
    const types =
      type === 'all' || !type
        ? ['mountain', 'waterSystem', 'historicalElement']
        : [type];

    let allResults: Array<any> = [];

    if (types.includes('mountain')) {
      const res = await this.mountainService.findAll({
        query: { name: { [Symbol.for('like')]: `%${kw}%` } } as any,
        attributes: [
          'id',
          'name',
          'regionDictId',
          'typeDictId',
          'longitude',
          'latitude',
        ],
        // 不设置 limit 参数，获取所有搜索结果
      });
      allResults = allResults.concat(
        res.list.map(i => ({ type: 'mountain', ...i }))
      );
    }

    if (types.includes('waterSystem')) {
      const res = await this.waterSystemService.findAll({
        query: { name: { [Symbol.for('like')]: `%${kw}%` } } as any,
        attributes: [
          'id',
          'name',
          'regionDictId',
          'typeDictId',
          'longitude',
          'latitude',
        ],
        // 不设置 limit 参数，获取所有搜索结果
      });
      allResults = allResults.concat(
        res.list.map(i => ({ type: 'waterSystem', ...i }))
      );
    }

    if (types.includes('historicalElement')) {
      const res = await this.historicalElementService.findAll({
        query: { name: { [Symbol.for('like')]: `%${kw}%` } } as any,
        attributes: ['id', 'name', 'regionDictId', 'typeDictId'],
        // 不设置 limit 参数，获取所有搜索结果
      });
      allResults = allResults.concat(
        res.list.map(i => ({ type: 'historicalElement', ...i }))
      );
    }

    const total = allResults.length;
    const start = (Number(page) - 1) * Number(pageSize);
    const list = allResults.slice(start, start + Number(pageSize));

    return { list, total, page: Number(page), pageSize: Number(pageSize) };
  }
}
