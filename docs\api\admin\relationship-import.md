# 要素关联导入 API 文档

## 概述

要素关联导入功能提供完整的 Excel 批量导入解决方案，包括模板下载、数据预览、正式导入等功能。

## API 接口

### 1. 获取导入模板

**GET** `/admin/relationship/template/download`

获取 Excel 导入模板下载链接。

#### 响应
```json
{
  "downloadUrl": "http://localhost:7001/public/templates/relationship_import_template.xlsx",
  "filename": "要素关联导入模板.xlsx",
  "description": "点击链接下载Excel导入模板，包含字段说明和示例数据",
  "buffer": "base64编码的文件内容"
}
```

---

### 2. 预览导入数据

**POST** `/admin/relationship/import/preview`

上传Excel文件并预览导入数据，用于验证数据格式和内容。

#### 请求
- **Content-Type**: `multipart/form-data`
- **文件字段**: `files`

#### 响应
```json
{
  "success": true,
  "message": "预览成功",
  "data": [
    {
      "relationName": "空间关系",
      "sourceType": "mountain",
      "sourceName": "华山",
      "targetType": "element",
      "targetEntityType": "water_system",
      "targetName": "渭河",
      "direction": "东连",
      "term": "华山东连渭河",
      "record": "华山位于渭河东侧",
      "sort": 1
    }
  ],
  "totalRows": 100,
  "validRows": 95,
  "previewCount": 10
}
```

#### 错误响应
```json
{
  "success": false,
  "message": "文件解析失败",
  "errors": [
    {
      "row": 2,
      "field": "源要素类型",
      "value": "无效类型",
      "message": "源要素类型必须是：山塬、水系、历史要素"
    }
  ],
  "totalRows": 100,
  "validRows": 95
}
```

---

### 3. 执行导入

**POST** `/admin/relationship/import/execute`

执行正式的数据导入操作。

#### 请求
- **Content-Type**: `multipart/form-data`
- **文件字段**: `files`

#### 响应
```json
{
  "success": true,
  "message": "导入完成",
  "totalRows": 100,
  "validRows": 95,
  "successCount": 90,
  "failureCount": 5,
  "errors": [
    {
      "index": 10,
      "error": "源要素 \"不存在的山塬\" 不存在",
      "data": {
        "sourceName": "不存在的山塬",
        "sourceType": "mountain"
      }
    }
  ]
}
```

#### 示例
```javascript
const formData = new FormData();
formData.append('files', excelFile);

const response = await fetch('/admin/relationship/import/execute', {
  method: 'POST',
  body: formData
});
const result = await response.json();

if (result.success) {
  console.log(`导入成功：${result.successCount} 条记录`);
  if (result.failureCount > 0) {
    console.log(`失败：${result.failureCount} 条记录`);
    console.log('错误详情：', result.errors);
  }
} else {
  console.log('导入失败:', result.message);
}
```

---

### 4. 批量导入接口

**POST** `/admin/relationship/batch-import`

直接批量导入要素关联数据，用于程序调用。

#### 请求
```json
{
  "relationships": [
    {
      "relationDictId": 1,
      "sourceType": "mountain",
      "sourceId": 1,
      "targetType": "element",
      "targetEntityType": "water_system",
      "targetId": 1,
      "direction": "东连",
      "term": "华山东连渭河",
      "record": "华山位于渭河东侧",
      "sort": 1,
      "status": 1
    }
  ]
}
```

#### 响应
```json
{
  "message": "批量导入完成",
  "successCount": 1,
  "failureCount": 0,
  "errors": []
}
```

---

## Excel 模板格式

### 字段说明

| 字段名 | 必填 | 说明 | 示例 |
|--------|------|------|------|
| 关系类型 | 否 | 关系类型名称 | 空间关系 |
| 源要素类型 | 是 | 源要素类型 | 山塬 |
| 源要素名称 | 是 | 源要素名称 | 华山 |
| 目标类型 | 是 | 目标类型 | 要素 |
| 目标要素类型 | 否 | 目标要素类型 | 水系 |
| 目标要素名称 | 是 | 目标要素名称 | 渭河 |
| 关联方向 | 否 | 关联方向 | 东连 |
| 词条描述 | 否 | 词条描述 | 华山东连渭河 |
| 记载内容 | 否 | 详细记载 | 华山位于渭河东侧 |
| 排序号 | 否 | 显示排序 | 1 |

### 数据类型说明

#### 源要素类型
- 山塬
- 水系
- 历史要素

#### 目标类型
- 要素：具体的地理要素
- 类别：要素类别

#### 目标要素类型（当目标类型为"要素"时）
- 山塬
- 水系
- 历史要素
- 类型字典
- 区域字典

### 示例数据

| 关系类型 | 源要素类型 | 源要素名称 | 目标类型 | 目标要素类型 | 目标要素名称 | 关联方向 | 词条描述 | 记载内容 | 排序号 |
|----------|------------|------------|----------|--------------|------------|----------|----------|----------|--------|
| 空间关系 | 山塬 | 华山 | 要素 | 水系 | 渭河 | 东连 | 华山东连渭河 | 华山位于渭河东侧 | 1 |
| 视线关系 | 水系 | 渭河 | 要素 | 历史要素 | 大雁塔 | 北望 | 渭河北望大雁塔 | 从渭河可以北望大雁塔 | 2 |

---

## 错误处理

### 常见错误类型

1. **文件格式错误**
   - 错误信息：Excel文件格式不正确
   - 解决方案：确保上传的是有效的Excel文件

2. **必填字段缺失**
   - 错误信息：源要素类型不能为空
   - 解决方案：填写所有必填字段

3. **数据不存在**
   - 错误信息：源要素 "xxx" 不存在
   - 解决方案：确保引用的要素在系统中存在

4. **重复数据**
   - 错误信息：该关系已存在
   - 解决方案：检查并删除重复的关联关系

### 最佳实践

1. **数据准备**
   - 先下载模板，按照格式填写数据
   - 确保引用的要素在系统中已存在
   - 检查数据的完整性和准确性

2. **导入流程**
   - 先使用预览功能验证数据
   - 修正预览中发现的错误
   - 再执行正式导入

3. **错误处理**
   - 仔细查看错误信息
   - 根据错误提示修正数据
   - 可以分批导入减少错误影响

---

## 注意事项

1. **数据依赖**: 导入的要素关联必须引用已存在的源要素和目标要素
2. **权限控制**: 所有导入接口都需要管理员权限
3. **文件大小**: 建议单次导入不超过1000条记录
4. **数据验证**: 系统会自动验证数据的完整性和有效性
5. **重复检查**: 系统会自动检查并跳过重复的关联关系
