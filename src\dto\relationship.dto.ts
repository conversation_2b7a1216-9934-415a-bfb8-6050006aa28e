import { Rule, RuleType } from '@midwayjs/validate';
import { PageQueryDTO } from './common.dto';

/**
 * 创建关系DTO
 */
export class CreateRelationshipDTO {
  @Rule(RuleType.number().integer().optional())
  relationDictId?: number;

  @Rule(RuleType.number().integer().optional())
  parentRelationshipId?: number;

  @Rule(RuleType.string().required().valid('ancient_city', 'cultural_element'))
  sourceEntityType: string;

  @Rule(RuleType.number().integer().required())
  sourceEntityId: number;

  @Rule(RuleType.string().required().valid('ancient_city', 'cultural_element'))
  targetEntityType: string;

  @Rule(RuleType.number().integer().required())
  targetEntityId: number;

  @Rule(RuleType.string().optional().max(50))
  direction?: string;

  @Rule(RuleType.string().optional())
  description?: string;

  @Rule(RuleType.string().optional())
  historicalRecord?: string;

  @Rule(RuleType.number().integer().optional())
  sort?: number;

  @Rule(RuleType.number().integer().valid(0, 1).default(1))
  status?: number = 1;
}

/**
 * 更新关系DTO
 */
export class UpdateRelationshipDTO {
  @Rule(RuleType.number().integer().optional())
  relationDictId?: number;

  @Rule(RuleType.number().integer().optional())
  parentRelationshipId?: number;

  @Rule(RuleType.string().optional().valid('ancient_city', 'cultural_element'))
  sourceEntityType?: string;

  @Rule(RuleType.number().integer().optional())
  sourceEntityId?: number;

  @Rule(RuleType.string().optional().valid('ancient_city', 'cultural_element'))
  targetEntityType?: string;

  @Rule(RuleType.number().integer().optional())
  targetEntityId?: number;

  @Rule(RuleType.string().optional().max(50))
  direction?: string;

  @Rule(RuleType.string().optional())
  description?: string;

  @Rule(RuleType.string().optional())
  historicalRecord?: string;

  @Rule(RuleType.number().integer().optional())
  sort?: number;

  @Rule(RuleType.number().integer().valid(0, 1).optional())
  status?: number;
}

/**
 * 关系查询DTO
 */
export class RelationshipQueryDTO extends PageQueryDTO {
  @Rule(RuleType.string().optional())
  keyword?: string;

  @Rule(RuleType.number().integer().optional())
  relationDictId?: number;

  @Rule(RuleType.number().integer().optional())
  parentRelationshipId?: number;

  @Rule(RuleType.string().optional().valid('ancient_city', 'cultural_element'))
  sourceEntityType?: string;

  @Rule(RuleType.number().integer().optional())
  sourceEntityId?: number;

  @Rule(RuleType.string().optional().valid('ancient_city', 'cultural_element'))
  targetEntityType?: string;

  @Rule(RuleType.number().integer().optional())
  targetEntityId?: number;

  @Rule(RuleType.string().optional())
  direction?: string;

  @Rule(RuleType.number().integer().valid(0, 1).optional())
  status?: number;
}

/**
 * 关系响应DTO
 */
export class RelationshipResponseDTO {
  id: number;
  relationDictId?: number;
  parentRelationshipId?: number;
  sourceEntityType: string;
  sourceEntityId: number;
  targetEntityType: string;
  targetEntityId: number;
  direction?: string;
  description?: string;
  historicalRecord?: string;
  sort?: number;
  status: number;
  createdAt?: Date;
  updatedAt?: Date;

  // 关联数据
  relationDict?: any;
  parent?: any;
  children?: any[];
  sourceElement?: any;
  targetElement?: any;

  constructor(data: any) {
    this.id = data.id;
    this.relationDictId = data.relationDictId;
    this.parentRelationshipId = data.parentRelationshipId;
    this.sourceEntityType = data.sourceEntityType;
    this.sourceEntityId = data.sourceEntityId;
    this.targetEntityType = data.targetEntityType;
    this.targetEntityId = data.targetEntityId;
    this.direction = data.direction;
    this.description = data.description;
    this.historicalRecord = data.historicalRecord;
    this.sort = data.sort;
    this.status = data.status;
    this.createdAt = data.createdAt;
    this.updatedAt = data.updatedAt;
    this.relationDict = data.relationDict;
    this.parent = data.parent;
    this.children = data.children;
    this.sourceElement = data.sourceElement;
    this.targetElement = data.targetElement;
  }
}

/**
 * 批量创建关系DTO
 */
export class BatchCreateRelationshipDTO {
  @Rule(RuleType.array().items(RuleType.object()).required())
  relations: CreateRelationshipDTO[];
}

/**
 * 网络图节点DTO
 */
export class NetworkNodeDTO {
  id: string;
  name: string;
  type: string;
  category: string;
  size?: number;
  color?: string;
  x?: number;
  y?: number;
}

/**
 * 网络图连线DTO
 */
export class NetworkLinkDTO {
  source: string;
  target: string;
  relation: string;
  direction?: string;
  description?: string;
  weight?: number;
  color?: string;
}

/**
 * 网络图数据DTO
 */
export class NetworkGraphDTO {
  nodes: NetworkNodeDTO[];
  links: NetworkLinkDTO[];
  categories: string[];
}

/**
 * 关系统计DTO
 */
export class RelationshipStatisticsDTO {
  total: number;
  bySourceEntityType: Array<{
    sourceEntityType: string;
    count: number;
  }>;
  byTargetEntityType: Array<{
    targetEntityType: string;
    count: number;
  }>;
  byRelationType: Array<{
    relationId: number;
    relationName: string;
    count: number;
  }>;
  byDirection: Array<{
    direction: string;
    count: number;
  }>;
}
