# 前端迁移代码示例

## ⚠️ 重要：接口权限区分

在开始迁移之前，请注意接口的权限区分：

| 接口类型 | 路径前缀 | 权限要求 | 使用场景 |
|----------|----------|----------|----------|
| **公开接口** | `/api/*` | 无需认证 | 前端门户网站 |
| **管理端接口** | `/admin/*` | 需要管理员认证 | 后台管理系统 |
| **兼容接口** | `/openapi/*` | 无需认证 | 地图服务等 |

**统计接口示例**：
- 前端门户：`GET /api/cultural-element/statistics` （无需认证）
- 后台管理：`GET /admin/cultural-element/statistics` （需要认证）

## 1. API调用示例

### 1.1 旧接口调用方式

```typescript
// 旧的分别调用三个接口
const getMapData = async () => {
  try {
    const [mountains, waterSystems, historicalElements] = await Promise.all([
      api.get('/openapi/mountain'),
      api.get('/openapi/water-system'), 
      api.get('/openapi/historical-element')
    ]);
    
    return {
      mountains: mountains.data,
      waterSystems: waterSystems.data,
      historicalElements: historicalElements.data
    };
  } catch (error) {
    console.error('获取地图数据失败:', error);
    throw error;
  }
};
```

### 1.2 新接口调用方式

```typescript
// 新的统一接口调用
const getMapData = async () => {
  try {
    const response = await api.get('/openapi/map/cultural-elements');
    
    if (response.success) {
      return response.data;
    } else {
      throw new Error(response.message);
    }
  } catch (error) {
    console.error('获取地图数据失败:', error);
    throw error;
  }
};

// 按类型获取特定数据（公开接口，无需认证）
const getHistoricalElements = async (params = {}) => {
  try {
    const response = await api.get('/api/cultural-element', {
      params: {
        ...params,
        typeDictId: [1, 2, 3, 4, 5] // 历史要素类型ID
      }
    });

    return response.success ? response.data : { list: [], total: 0 };
  } catch (error) {
    console.error('获取历史要素失败:', error);
    return { list: [], total: 0 };
  }
};

// 获取统计数据（公开接口，无需认证）
const getPublicStatistics = async () => {
  try {
    const response = await api.get('/api/cultural-element/statistics');
    return response.success ? response.data : null;
  } catch (error) {
    console.error('获取统计数据失败:', error);
    return null;
  }
};

// 管理端统计数据（需要认证）
const getAdminStatistics = async () => {
  try {
    const response = await api.get('/admin/cultural-element/statistics', {
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`
      }
    });
    return response.success ? response.data : null;
  } catch (error) {
    console.error('获取管理端统计数据失败:', error);
    return null;
  }
};
```

## 2. 数据处理示例

### 2.1 旧的数据处理方式

```typescript
// 旧的分别处理不同类型数据
const processMapData = (mountains, waterSystems, historicalElements) => {
  const mapPoints = [];
  
  // 处理山塬数据
  mountains.forEach(mountain => {
    mapPoints.push({
      id: mountain.id,
      name: mountain.name,
      type: 'mountain',
      coordinates: [mountain.longitude, mountain.latitude],
      height: mountain.height,
      // 其他山塬特有属性
    });
  });
  
  // 处理水系数据
  waterSystems.forEach(waterSystem => {
    mapPoints.push({
      id: waterSystem.id,
      name: waterSystem.name,
      type: 'waterSystem',
      coordinates: [waterSystem.longitude, waterSystem.latitude],
      length: waterSystem.length,
      // 其他水系特有属性
    });
  });
  
  // 处理历史要素数据
  historicalElements.forEach(element => {
    mapPoints.push({
      id: element.id,
      name: element.name,
      type: 'historicalElement',
      coordinates: [element.longitude, element.latitude],
      constructionYear: element.constructionYear,
      // 其他历史要素特有属性
    });
  });
  
  return mapPoints;
};
```

### 2.2 新的统一数据处理方式

```typescript
// 新的统一数据处理
const processMapData = (culturalElements) => {
  return culturalElements.map(element => ({
    id: element.id,
    name: element.name,
    code: element.code,
    type: getElementType(element.typeDictId), // 根据类型ID确定类型
    coordinates: [element.longitude, element.latitude],
    typeName: element.typeName,
    regionName: element.regionName,
    ancientCityName: element.ancientCityName,
    // 统一的属性
    height: element.height,
    lengthArea: element.lengthArea,
    constructionYear: element.constructionYear,
    locationDescription: element.locationDescription,
    historicalRecords: element.historicalRecords,
  }));
};

// 类型判断辅助函数
const getElementType = (typeDictId) => {
  if ([1, 2, 3, 4, 5].includes(typeDictId)) return 'historical';
  if ([6, 7, 8].includes(typeDictId)) return 'mountain';
  if ([9, 10, 11].includes(typeDictId)) return 'waterSystem';
  return 'unknown';
};

// 按类型分组
const groupByType = (culturalElements) => {
  return culturalElements.reduce((groups, element) => {
    const type = getElementType(element.typeDictId);
    if (!groups[type]) groups[type] = [];
    groups[type].push(element);
    return groups;
  }, {});
};
```

## 3. React组件迁移示例

### 3.1 旧的组件实现

```tsx
// 旧的分别管理三种数据的组件
const MapComponent = () => {
  const [mountains, setMountains] = useState([]);
  const [waterSystems, setWaterSystems] = useState([]);
  const [historicalElements, setHistoricalElements] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [mountainData, waterData, historicalData] = await Promise.all([
          api.getMountains(),
          api.getWaterSystems(),
          api.getHistoricalElements()
        ]);
        
        setMountains(mountainData.data);
        setWaterSystems(waterData.data);
        setHistoricalElements(historicalData.data);
      } catch (error) {
        console.error('数据加载失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div>
      {loading && <div>加载中...</div>}
      <MapView 
        mountains={mountains}
        waterSystems={waterSystems}
        historicalElements={historicalElements}
      />
    </div>
  );
};
```

### 3.2 新的组件实现

```tsx
// 新的统一数据管理组件
const MapComponent = () => {
  const [culturalElements, setCulturalElements] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    typeDictId: null,
    regionDictId: null,
    ancientCityId: null,
  });

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const response = await api.getCulturalElements(filters);
        if (response.success) {
          setCulturalElements(response.data.list);
        }
      } catch (error) {
        console.error('数据加载失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [filters]);

  // 按类型分组数据
  const groupedData = useMemo(() => {
    return groupByType(culturalElements);
  }, [culturalElements]);

  return (
    <div>
      {loading && <div>加载中...</div>}
      
      {/* 筛选器 */}
      <FilterPanel 
        filters={filters}
        onFiltersChange={setFilters}
      />
      
      {/* 地图视图 */}
      <MapView 
        culturalElements={culturalElements}
        groupedData={groupedData}
      />
      
      {/* 统计信息 */}
      <StatisticsPanel data={groupedData} />
    </div>
  );
};
```

## 4. 状态管理迁移示例

### 4.1 Redux/Zustand状态迁移

```typescript
// 旧的状态结构
interface OldState {
  mountains: {
    data: Mountain[];
    loading: boolean;
    error: string | null;
  };
  waterSystems: {
    data: WaterSystem[];
    loading: boolean;
    error: string | null;
  };
  historicalElements: {
    data: HistoricalElement[];
    loading: boolean;
    error: string | null;
  };
}

// 新的统一状态结构
interface NewState {
  culturalElements: {
    data: CulturalElement[];
    total: number;
    loading: boolean;
    error: string | null;
    filters: CulturalElementFilters;
    pagination: {
      page: number;
      pageSize: number;
    };
  };
  // 缓存分组数据
  groupedData: {
    historical: CulturalElement[];
    mountain: CulturalElement[];
    waterSystem: CulturalElement[];
  };
}
```

### 4.2 Actions迁移

```typescript
// 旧的Actions
const oldActions = {
  fetchMountains: () => async (dispatch) => {
    dispatch({ type: 'FETCH_MOUNTAINS_START' });
    try {
      const data = await api.getMountains();
      dispatch({ type: 'FETCH_MOUNTAINS_SUCCESS', payload: data });
    } catch (error) {
      dispatch({ type: 'FETCH_MOUNTAINS_ERROR', payload: error.message });
    }
  },
  // 类似的 fetchWaterSystems, fetchHistoricalElements
};

// 新的统一Actions
const newActions = {
  fetchCulturalElements: (filters = {}) => async (dispatch) => {
    dispatch({ type: 'FETCH_CULTURAL_ELEMENTS_START' });
    try {
      const response = await api.getCulturalElements(filters);
      if (response.success) {
        dispatch({ 
          type: 'FETCH_CULTURAL_ELEMENTS_SUCCESS', 
          payload: response.data 
        });
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      dispatch({ 
        type: 'FETCH_CULTURAL_ELEMENTS_ERROR', 
        payload: error.message 
      });
    }
  },
  
  setFilters: (filters) => ({
    type: 'SET_FILTERS',
    payload: filters
  }),
  
  // 按类型获取数据的便捷方法
  fetchByType: (typeIds) => (dispatch) => {
    return dispatch(newActions.fetchCulturalElements({ typeDictId: typeIds }));
  }
};
```

## 5. 错误处理迁移

### 5.1 新的错误处理方式

```typescript
// 统一的错误处理
const handleApiError = (error, response) => {
  if (response && !response.success) {
    // API返回的业务错误
    switch (response.code) {
      case 400:
        message.error(`参数错误: ${response.message}`);
        break;
      case 401:
        message.error('请先登录');
        // 跳转到登录页
        break;
      case 403:
        message.error('权限不足');
        break;
      case 404:
        message.error('资源不存在');
        break;
      default:
        message.error(response.message || '操作失败');
    }
  } else {
    // 网络错误或其他错误
    console.error('请求失败:', error);
    message.error('网络错误，请稍后重试');
  }
};

// 使用示例
const fetchData = async () => {
  try {
    const response = await api.getCulturalElements();
    if (response.success) {
      return response.data;
    } else {
      handleApiError(null, response);
      return null;
    }
  } catch (error) {
    handleApiError(error, null);
    return null;
  }
};
```

## 6. 类型定义迁移

```typescript
// 新的TypeScript类型定义
interface CulturalElement {
  id: number;
  name: string;
  code: string;
  typeDictId?: number;
  ancientCityId?: number;
  regionDictId: number;
  longitude?: number;
  latitude?: number;
  height?: number;
  lengthArea?: string;
  locationDescription?: string;
  constructionYear?: number;
  historicalRecords?: string;
  createdAt: string;
  updatedAt: string;
  
  // 关联数据
  typeName?: string;
  regionName?: string;
  ancientCityName?: string;
}

interface CulturalElementFilters {
  page?: number;
  pageSize?: number;
  name?: string;
  code?: string;
  typeDictId?: number | number[];
  regionDictId?: number;
  ancientCityId?: number;
  constructionYear?: number;
}

interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  code?: number;
  timestamp: string;
}

interface PageResponse<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}
```

---

这些示例展示了如何从旧的分离式接口迁移到新的统一接口。建议按照示例逐步进行迁移，确保每个步骤都经过充分测试。
