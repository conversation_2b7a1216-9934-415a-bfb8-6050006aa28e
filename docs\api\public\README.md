# 门户数字化接口文档 (OpenAPI)

## 概述

本文档详细描述了智慧影建门户系统的数字化相关公开接口，基于现有后端实现整理，方便前端调用和调试。

### 接口规范

**基础信息：**
- 基础路径: `/openapi`
- 请求方式: 主要为 `GET` 请求
- 认证方式: 公开接口，无需认证

**统一响应格式：**
```json
{
  "errCode": 0,
  "data": {},
  "msg": "OK"
}
```

**分页规范：**
- 请求参数: `page`(默认1), `pageSize`(默认10)
- 响应格式: `{ list: [], total: number, page: number, pageSize: number }`

**时间格式：** ISO 8601 字符串格式

---

## 1. 门户首页接口

### 1.1 获取门户概览
获取门户首页的综合概览数据，包括统计信息和最新数据。

**接口地址：** `GET /openapi/portal/overview`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID，用于筛选特定区域的数据 |

**响应示例：**
```json
{
  "errCode": 0,
  "data": {
    "statistics": {
      "mountain": 150,
      "waterSystem": 80,
      "historicalElement": 200,
      "user": 50,
      "typeDict": 25,
      "regionDict": 12,
      "relationshipDict": 30
    },
    "recentMountains": [
      {
        "id": 1,
        "name": "华山",
        "code": "HS001",
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "recentWaterSystems": [...],
    "recentHistoricalElements": [...]
  },
  "msg": "OK"
}
```

### 1.2 获取地图标记点
获取地图上的标记点数据，支持按类型和区域筛选，包含关联的照片信息。

**接口地址：** `GET /openapi/portal/map-markers`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| types | string | 否 | 标记点类型，多个用逗号分隔：mountain,waterSystem,historicalElement |
| regionId | number | 否 | 区域ID筛选 |

**响应字段说明：**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 实体ID |
| type | string | 实体类型 |
| name | string | 实体名称 |
| longitude | number | 经度 |
| latitude | number | 纬度 |
| thumbnailUrl | string | 缩略图URL（第一张照片） |
| photos | array | 照片列表（最多3张预览） |

**响应示例：**
```json
{
  "errCode": 0,
  "data": [
    {
      "id": 1,
      "type": "mountain",
      "name": "华山",
      "longitude": 110.0896,
      "latitude": 34.4749,
      "thumbnailUrl": "/uploads/2024/01/01/mountain-1.jpg",
      "photos": [
        {
          "id": 1,
          "name": "华山主峰",
          "url": "/uploads/2024/01/01/mountain-1.jpg"
        },
        {
          "id": 2,
          "name": "华山日出",
          "url": "/uploads/2024/01/01/mountain-2.jpg"
        }
      ]
    }
  ],
  "msg": "OK"
}
```

### 1.3 获取地图数据
获取用于地图展示的详细数据，包含照片信息。

**接口地址：** `GET /openapi/map/data`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | string | 否 | 数据类型：mountain/water_system/historical_element |
| regionId | number | 否 | 区域ID筛选 |

---

## 2. 山塬数据接口

### 2.1 获取山塬列表
分页获取山塬数据列表，支持关键词搜索和区域筛选。

**接口地址：** `GET /openapi/mountain/list`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |
| keyword | string | 否 | 搜索关键词 |
| regionId | number | 否 | 区域ID筛选 |

### 2.2 获取山塬详情
根据ID获取山塬的详细信息。

**接口地址：** `GET /openapi/mountain/{id}`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 山塬ID |

### 2.3 获取山塬照片
获取指定山塬的照片列表。

**接口地址：** `GET /openapi/mountain/{id}/photos`

### 2.4 获取所有山塬（下拉选项）
获取所有山塬的简化信息，用于下拉选择。

**接口地址：** `GET /openapi/mountain/all`

**响应示例：**
```json
{
  "errCode": 0,
  "data": [
    {
      "id": 1,
      "name": "华山",
      "code": "HS001"
    }
  ],
  "msg": "OK"
}
```

---

## 3. 水系数据接口

### 3.1 获取水系列表
**接口地址：** `GET /openapi/water-system/list`

### 3.2 获取水系详情
**接口地址：** `GET /openapi/water-system/{id}`

### 3.3 获取水系照片
**接口地址：** `GET /openapi/water-system/{id}/photos`

### 3.4 获取所有水系（下拉选项）
**接口地址：** `GET /openapi/water-system/all`

*参数说明与山塬接口类似*

---

## 4. 历史要素数据接口

### 4.1 获取历史要素列表
**接口地址：** `GET /openapi/historical-element/list`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |
| keyword | string | 否 | 搜索关键词 |
| regionId | number | 否 | 区域ID筛选 |
| typeId | number | 否 | 类型ID筛选 |

### 4.2 获取历史要素详情
**接口地址：** `GET /openapi/historical-element/{id}`

### 4.3 获取历史要素照片
**接口地址：** `GET /openapi/historical-element/{id}/photos`

### 4.4 获取所有历史要素（下拉选项）
**接口地址：** `GET /openapi/historical-element/all`

---

## 5. 数字化统计接口

### 5.1 获取基础统计数据
获取系统的基础统计信息，包括各类数据的数量统计。

**接口地址：** `GET /openapi/statistics/basic`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID筛选 |
| startTime | string | 否 | 开始时间（ISO格式） |
| endTime | string | 否 | 结束时间（ISO格式） |

**响应示例：**
```json
{
  "errCode": 0,
  "data": {
    "counts": {
      "mountain": 150,
      "waterSystem": 80,
      "historicalElement": 200,
      "user": 50,
      "typeDict": 25,
      "regionDict": 12,
      "relationshipDict": 30
    },
    "regionStats": [
      {
        "region": "西安市",
        "regionId": 1,
        "mountainCount": 50,
        "waterSystemCount": 30,
        "historicalElementCount": 80,
        "total": 160
      }
    ],
    "timelineData": [
      {
        "year": 2023,
        "elements": [
          {
            "id": 1,
            "name": "大雁塔",
            "type": "historicalElement"
          }
        ]
      }
    ]
  },
  "msg": "OK"
}
```

### 5.2 获取综合统计报告
获取更详细的综合统计报告数据。

**接口地址：** `GET /openapi/statistics/comprehensive`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID筛选 |

### 5.3 获取时间轴数据
获取历史要素按时间分布的时间轴数据。

**接口地址：** `GET /openapi/statistics/timeline`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID筛选 |

**响应示例：**
```json
{
  "errCode": 0,
  "data": [
    {
      "year": 618,
      "elements": [
        {
          "id": 1,
          "name": "大雁塔",
          "type": "historicalElement"
        }
      ]
    }
  ],
  "msg": "OK"
}
```

### 5.4 获取区域分布统计
获取各区域的数据分布统计。

**接口地址：** `GET /openapi/statistics/region-distribution`

### 5.5 获取数据概览
获取数据概览信息，包含总体统计、区域分布和时间轴数据。

**接口地址：** `GET /openapi/statistics/overview`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID筛选 |

---

## 6. 关系网络接口

### 6.1 获取网络图数据
获取关系网络的图形化数据，用于展示实体间的关联关系。

**接口地址：** `GET /openapi/relationship/network-graph`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sourceType | string | 否 | 源实体类型 |
| targetType | string | 否 | 目标实体类型 |
| relationDictId | number | 否 | 关系类型ID |
| status | number | 否 | 状态筛选（默认为1，启用状态） |

**响应示例：**
```json
{
  "errCode": 0,
  "data": {
    "nodes": [
      {
        "id": "mountain_1",
        "name": "华山",
        "type": "mountain",
        "category": "山塬",
        "size": 10,
        "color": "#ff6b6b"
      }
    ],
    "links": [
      {
        "source": "mountain_1",
        "target": "water_1",
        "relation": "临近",
        "direction": "bidirectional",
        "term": "地理位置",
        "weight": 1,
        "color": "#4ecdc4"
      }
    ],
    "categories": [
      {
        "name": "山塬",
        "color": "#ff6b6b"
      }
    ]
  },
  "msg": "OK"
}
```

### 6.2 根据要素获取关联关系
根据指定的实体类型和ID获取其关联的关系列表。

**接口地址：** `GET /openapi/relationship/by-element/{elementType}/{elementId}`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| elementType | string | 是 | 实体类型：mountain/waterSystem/historicalElement |
| elementId | number | 是 | 实体ID |

### 6.3 获取关系统计
获取关系数据的统计信息。

**接口地址：** `GET /openapi/relationship/statistics`

### 6.4 获取关系列表
获取关系数据的列表（简化版）。

**接口地址：** `GET /openapi/relationship/list`

### 6.5 根据关系类型获取关联关系
根据关系类型ID获取相关的关联关系。

**接口地址：** `GET /openapi/relationship/by-relation/{relationId}`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| relationId | number | 是 | 关系类型ID |

### 6.6 搜索关系
根据关键词搜索关系数据。

**接口地址：** `GET /openapi/relationship/search`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyword | string | 是 | 搜索关键词 |

---

## 7. 字典数据接口

### 7.1 获取区域字典列表
获取所有区域字典数据。

**接口地址：** `GET /openapi/region-dict/all`

**响应示例：**
```json
{
  "errCode": 0,
  "data": [
    {
      "id": 1,
      "regionName": "西安市",
      "regionCode": "XA",
      "parentId": null,
      "level": 1
    }
  ],
  "msg": "OK"
}
```

### 7.2 获取区域字典树形结构
获取区域字典的树形结构数据。

**接口地址：** `GET /openapi/region-dict/tree`

### 7.3 获取类型字典列表
获取所有类型字典数据。

**接口地址：** `GET /openapi/type-dict/all`

### 7.4 获取类型字典树形结构
获取类型字典的树形结构数据。

**接口地址：** `GET /openapi/type-dict/tree`

---

## 8. 地图数据接口

### 8.1 获取地图数据
获取地图上显示的各类实体数据。

**接口地址：** `GET /openapi/map/data`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| types | string | 否 | 实体类型，多个用逗号分隔 |
| regionId | number | 否 | 区域ID筛选 |

### 8.2 获取实体详情数据
获取地图上实体的详细信息。

**接口地址：** `GET /openapi/map/detail`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | string | 是 | 实体类型：mountain/waterSystem/historicalElement |
| id | number | 是 | 实体ID |

### 8.3 获取地图统计数据
获取地图相关的统计数据。

**接口地址：** `GET /openapi/map/statistics`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID筛选 |

---

## 9. 统一搜索接口

### 9.1 跨类型搜索
在所有实体类型中进行统一搜索。

**接口地址：** `GET /openapi/search`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyword | string | 是 | 搜索关键词 |
| type | string | 否 | 搜索类型：mountain/waterSystem/historicalElement/all，默认all |
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |

**响应示例：**
```json
{
  "errCode": 0,
  "data": {
    "list": [
      {
        "type": "mountain",
        "id": 1,
        "name": "华山",
        "regionDictId": 1,
        "typeDictId": 1,
        "longitude": 110.0,
        "latitude": 34.0
      },
      {
        "type": "historicalElement",
        "id": 1,
        "name": "华清池",
        "regionDictId": 1,
        "typeDictId": 2
      }
    ],
    "total": 2,
    "page": 1,
    "pageSize": 10
  },
  "msg": "OK"
}
```

---

## 10. 数据模型说明

### 10.1 公共字段
所有实体都包含以下公共字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 唯一标识ID |
| name | string | 名称 |
| code | string | 编码 |
| regionDictId | number | 区域字典ID |
| typeDictId | number | 类型字典ID |
| createdAt | string | 创建时间（ISO格式） |
| updatedAt | string | 更新时间（ISO格式） |

### 10.2 山塬特有字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| height | number | 海拔高度（米） |
| longitude | number | 经度 |
| latitude | number | 纬度 |

### 10.3 水系特有字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| lengthArea | number | 长度/面积 |
| longitude | number | 经度 |
| latitude | number | 纬度 |

### 10.4 历史要素特有字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| constructionTime | string | 建造时间 |
| constructionLongitude | number | 建造地经度 |
| constructionLatitude | number | 建造地纬度 |
| historicalRecords | string | 历史记录 |

### 10.5 照片数据结构
```json
{
  "id": 1,
  "name": "照片名称",
  "url": "https://example.com/photo.jpg",
  "description": "照片描述"
}
```

### 10.6 时间轴数据结构
```json
{
  "year": 618,
  "elements": [
    {
      "id": 1,
      "name": "大雁塔",
      "type": "historicalElement"
    }
  ]
}
```

### 10.7 网络图数据结构
**节点（Nodes）：**
```json
{
  "id": "mountain_1",
  "name": "华山",
  "type": "mountain",
  "category": "山塬",
  "size": 10,
  "color": "#ff6b6b"
}
```

**连接（Links）：**
```json
{
  "source": "mountain_1",
  "target": "water_1",
  "relation": "临近",
  "direction": "bidirectional",
  "term": "地理位置",
  "weight": 1,
  "color": "#4ecdc4"
}
```

---

## 11. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

---

## 12. 使用示例

### 12.1 获取门户概览数据
```javascript
// 获取全部数据
fetch('/openapi/portal/overview')
  .then(response => response.json())
  .then(data => console.log(data));

// 获取特定区域数据
fetch('/openapi/portal/overview?regionId=1')
  .then(response => response.json())
  .then(data => console.log(data));
```

### 12.2 搜索历史要素
```javascript
// 分页搜索
fetch('/openapi/historical-element/list?keyword=塔&page=1&pageSize=10&regionId=1')
  .then(response => response.json())
  .then(data => console.log(data));
```

### 12.3 获取网络图数据
```javascript
// 获取关系网络图
fetch('/openapi/relationship/network-graph')
  .then(response => response.json())
  .then(data => {
    const { nodes, links, categories } = data.data;
    // 使用数据渲染网络图
  });
```

---

## 13. 注意事项

1. **数据权限**：所有OpenAPI接口都是公开的，不需要认证，但只返回公开状态的数据。

2. **分页限制**：为了性能考虑，分页接口的pageSize最大值为100。

3. **搜索性能**：搜索接口支持模糊匹配，但建议关键词长度至少2个字符以获得更好的搜索效果。

4. **缓存策略**：字典类接口（如区域字典、类型字典）数据相对稳定，建议前端进行适当缓存。

5. **错误处理**：所有接口都遵循统一的错误响应格式，请根据errCode字段判断请求是否成功。

6. **数据更新**：统计类数据可能存在延迟，建议根据业务需求合理设置刷新频率。

---

## 14. 详细接口文档

为了方便开发和调试，我们为主要的接口模块提供了详细的说明文档：

### 14.1 核心功能模块
- **[门户概览接口](./portal-overview.md)** - 门户首页概览数据的详细说明
- **[数字化统计接口](./statistics-digital.md)** - 统计分析接口的完整文档
- **[关系网络接口](./relationship-network.md)** - 关系网络和知识图谱接口详解
- **[统一搜索接口](./search-unified.md)** - 跨类型搜索功能的详细说明
- **[地图数据接口](./map-data.md)** - 地图可视化数据接口文档
- **[字典数据接口](./dictionary-data.md)** - 字典数据管理接口说明

### 14.2 实体数据模块
- **[历史要素接口](./historical-element.md)** - 历史要素数据接口详解

### 14.3 文档说明
每个详细文档都包含：
- 接口的完整参数说明
- 详细的响应数据结构
- 实际的使用示例代码
- 最佳实践和优化建议
- 常见问题和解决方案

建议开发者根据具体需求查阅相应的详细文档，以获得更全面的接口使用指导。

### 14.4 快速参考
- **[接口快速参考](./quick-reference.md)** - 所有接口的快速查询表格，方便开发时快速查找接口信息


