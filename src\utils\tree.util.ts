/**
 * 树形结构工具类
 */

/**
 * 树形节点接口
 */
export interface TreeNode {
  id: number;
  parentId?: number;
  children?: TreeNode[];
  [key: string]: any;
}

/**
 * 构建树形结构
 * @param items 扁平化的数据数组
 * @returns 树形结构数组
 */
export function buildTree<T extends TreeNode>(items: T[]): T[] {
  const tree: T[] = [];
  const map = new Map<number, T>();

  // 创建映射，为每个节点初始化children数组
  items.forEach(item => {
    map.set(item.id, { ...item, children: [] } as T);
  });

  // 构建树形结构
  items.forEach(item => {
    const node = map.get(item.id)!;
    if (item.parentId && map.has(item.parentId)) {
      const parent = map.get(item.parentId)!;
      if (!parent.children) {
        parent.children = [];
      }
      parent.children.push(node);
    } else {
      tree.push(node);
    }
  });

  // 清理空的children属性
  cleanEmptyChildren(tree);

  return tree;
}

/**
 * 递归清理空的children属性
 * @param nodes 树形节点数组
 */
function cleanEmptyChildren<T extends TreeNode>(nodes: T[]): void {
  nodes.forEach(node => {
    if (node.children) {
      if (node.children.length === 0) {
        // 删除空的children属性
        delete node.children;
      } else {
        // 递归处理子节点
        cleanEmptyChildren(node.children);
      }
    }
  });
}

/**
 * 扁平化树形结构
 * @param tree 树形结构数组
 * @returns 扁平化的数组
 */
export function flattenTree<T extends TreeNode>(tree: T[]): T[] {
  const result: T[] = [];

  function traverse(nodes: T[]) {
    nodes.forEach(node => {
      const { children, ...nodeWithoutChildren } = node;
      result.push(nodeWithoutChildren as T);

      if (children && children.length > 0) {
        traverse(children as T[]);
      }
    });
  }

  traverse(tree);
  return result;
}

/**
 * 查找树形结构中的节点
 * @param tree 树形结构数组
 * @param predicate 查找条件函数
 * @returns 找到的节点或undefined
 */
export function findNodeInTree<T extends TreeNode>(
  tree: T[],
  predicate: (node: T) => boolean
): T | undefined {
  for (const node of tree) {
    if (predicate(node)) {
      return node;
    }

    if (node.children && node.children.length > 0) {
      const found = findNodeInTree(node.children as T[], predicate);
      if (found) {
        return found as T;
      }
    }
  }

  return undefined;
}

/**
 * 获取节点的所有父级节点路径
 * @param items 扁平化的数据数组
 * @param nodeId 节点ID
 * @returns 父级节点路径数组（从根节点到直接父节点）
 */
export function getNodePath<T extends TreeNode>(
  items: T[],
  nodeId: number
): T[] {
  const map = new Map<number, T>();
  items.forEach(item => map.set(item.id, item));

  const path: T[] = [];
  let currentNode = map.get(nodeId);

  while (currentNode && currentNode.parentId) {
    const parent = map.get(currentNode.parentId);
    if (parent) {
      path.unshift(parent);
      currentNode = parent;
    } else {
      break;
    }
  }

  return path;
}

/**
 * 获取节点的所有子级节点（扁平化）
 * @param tree 树形结构数组
 * @param nodeId 节点ID
 * @returns 所有子级节点的扁平化数组
 */
export function getNodeChildren<T extends TreeNode>(
  tree: T[],
  nodeId: number
): T[] {
  const targetNode = findNodeInTree(tree, node => node.id === nodeId);

  if (!targetNode || !targetNode.children) {
    return [];
  }

  return flattenTree(targetNode.children as T[]);
}
