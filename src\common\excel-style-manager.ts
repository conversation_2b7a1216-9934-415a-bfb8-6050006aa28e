/**
 * Excel样式管理器
 * 专门处理Excel文件的样式美化
 */
import * as XLSX from 'xlsx';

export interface StyleConfig {
  theme?: 'blue' | 'green' | 'orange' | 'red';
  showBorders?: boolean;
  highlightRequired?: boolean;
}

export interface CellStyle {
  font?: {
    bold?: boolean;
    size?: number;
    color?: { rgb: string };
    italic?: boolean;
  };
  fill?: {
    patternType: string;
    fgColor: { rgb: string };
  };
  border?: {
    top?: { style: string; color: { auto: number } };
    bottom?: { style: string; color: { auto: number } };
    left?: { style: string; color: { auto: number } };
    right?: { style: string; color: { auto: number } };
  };
  alignment?: {
    horizontal?: string;
    vertical?: string;
  };
}

/**
 * Excel样式管理器
 */
export class ExcelStyleManager {
  private static readonly THEMES = {
    blue: {
      primary: '4472C4',
      secondary: 'E7E6E6',
      required: 'FF0000',
      text: '333333', // 改为深灰色，适合一般文字
      headerText: 'FFFFFF', // 新增表头专用白色文字
      border: 'CCCCCC',
    },
    green: {
      primary: '70AD47',
      secondary: 'E2EFDA',
      required: 'FF0000',
      text: '333333', // 改为深灰色，适合一般文字
      headerText: 'FFFFFF', // 新增表头专用白色文字
      border: 'CCCCCC',
    },
    orange: {
      primary: 'ED7D31',
      secondary: 'FCE4D6',
      required: 'FF0000',
      text: '333333', // 改为深灰色，适合一般文字
      headerText: 'FFFFFF', // 新增表头专用白色文字
      border: 'CCCCCC',
    },
    red: {
      primary: 'C5504B',
      secondary: 'F2DCDB',
      required: 'FFFFFF',
      text: '333333', // 改为深灰色，适合一般文字
      headerText: 'FFFFFF', // 新增表头专用白色文字
      border: 'CCCCCC',
    },
  };

  /**
   * 获取主题颜色
   */
  private static getThemeColors(theme = 'blue') {
    return this.THEMES[theme as keyof typeof this.THEMES] || this.THEMES.blue;
  }

  /**
   * 创建标题样式
   */
  static createTitleStyle(config: StyleConfig = {}): CellStyle {
    const colors = this.getThemeColors(config.theme);

    return {
      font: {
        bold: true,
        size: 16,
        color: { rgb: '000000' },
      },
      fill: {
        patternType: 'solid',
        fgColor: { rgb: colors.secondary },
      },
      alignment: {
        horizontal: 'center',
        vertical: 'center',
      },
      ...(config.showBorders && {
        border: {
          top: { style: 'thin', color: { auto: 1 } },
          bottom: { style: 'thin', color: { auto: 1 } },
          left: { style: 'thin', color: { auto: 1 } },
          right: { style: 'thin', color: { auto: 1 } },
        },
      }),
    };
  }

  /**
   * 创建表头样式
   */
  static createHeaderStyle(
    config: StyleConfig = {},
    isRequired = false
  ): CellStyle {
    const colors = this.getThemeColors(config.theme);

    return {
      font: {
        bold: true,
        color: { rgb: colors.headerText || colors.text }, // 优先使用headerText，回退到text
        ...(isRequired &&
          config.highlightRequired && {
            color: { rgb: colors.required },
          }),
      },
      fill: {
        patternType: 'solid',
        fgColor: { rgb: colors.primary },
      },
      alignment: {
        horizontal: 'center',
        vertical: 'center',
      },
      ...(config.showBorders && {
        border: {
          top: { style: 'thin', color: { auto: 1 } },
          bottom: { style: 'thin', color: { auto: 1 } },
          left: { style: 'thin', color: { auto: 1 } },
          right: { style: 'thin', color: { auto: 1 } },
        },
      }),
    };
  }

  /**
   * 创建说明文字样式
   */
  static createInstructionStyle(config: StyleConfig = {}): CellStyle {
    const colors = this.getThemeColors(config.theme);
    return {
      font: {
        color: { rgb: colors.text || '666666' },
        size: 11,
      },
      alignment: {
        horizontal: 'left',
        vertical: 'center',
      },
    };
  }

  /**
   * 创建数据单元格样式
   */
  static createDataCellStyle(config: StyleConfig = {}): CellStyle {
    return {
      alignment: {
        horizontal: 'left',
        vertical: 'center',
      },
      ...(config.showBorders && {
        border: {
          top: { style: 'thin', color: { auto: 1 } },
          bottom: { style: 'thin', color: { auto: 1 } },
          left: { style: 'thin', color: { auto: 1 } },
          right: { style: 'thin', color: { auto: 1 } },
        },
      }),
    };
  }

  /**
   * 创建字段说明样式
   */
  static createDescriptionStyle(config: StyleConfig = {}): CellStyle {
    const colors = this.getThemeColors(config.theme);
    return {
      font: {
        italic: true,
        color: { rgb: colors.text }, // 使用主题的文字颜色（现在是深灰色）
        size: 10,
      },
      fill: {
        patternType: 'solid',
        fgColor: { rgb: 'F8F9FA' }, // 浅灰色背景
      },
      alignment: {
        horizontal: 'center',
        vertical: 'center',
      },
      ...(config.showBorders && {
        border: {
          top: { style: 'thin', color: { auto: 1 } },
          bottom: { style: 'thin', color: { auto: 1 } },
          left: { style: 'thin', color: { auto: 1 } },
          right: { style: 'thin', color: { auto: 1 } },
        },
      }),
    };
  }

  /**
   * 应用样式到工作表
   */
  static applyStylesToSheet(
    sheet: XLSX.WorkSheet,
    layout: {
      titleRow?: number;
      instructionStartRow?: number;
      instructionEndRow?: number;
      headerRow?: number;
      descriptionRow?: number;
      dataStartRow?: number;
      requiredColumns?: number[];
    },
    config: StyleConfig = {}
  ): void {
    if (!sheet['!ref']) return;

    const range = XLSX.utils.decode_range(sheet['!ref']);

    // 应用标题样式
    if (layout.titleRow !== undefined) {
      this.applyRowStyle(
        sheet,
        layout.titleRow,
        range,
        this.createTitleStyle(config)
      );
    }

    // 应用说明文字样式
    if (
      layout.instructionStartRow !== undefined &&
      layout.instructionEndRow !== undefined
    ) {
      for (
        let row = layout.instructionStartRow;
        row <= layout.instructionEndRow;
        row++
      ) {
        this.applyCellStyle(sheet, row, 0, this.createInstructionStyle(config));
      }
    }

    // 应用表头样式
    if (layout.headerRow !== undefined) {
      for (let col = range.s.c; col <= range.e.c; col++) {
        const isRequired = layout.requiredColumns?.includes(col) || false;
        this.applyCellStyle(
          sheet,
          layout.headerRow,
          col,
          this.createHeaderStyle(config, isRequired)
        );
      }
    }

    // 应用字段说明样式
    if (layout.descriptionRow !== undefined) {
      this.applyRowStyle(
        sheet,
        layout.descriptionRow,
        range,
        this.createDescriptionStyle(config)
      );
    }

    // 应用数据单元格样式
    if (layout.dataStartRow !== undefined) {
      for (let row = layout.dataStartRow; row <= range.e.r; row++) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          this.applyCellStyle(
            sheet,
            row,
            col,
            this.createDataCellStyle(config)
          );
        }
      }
    }
  }

  /**
   * 应用样式到单个单元格
   */
  private static applyCellStyle(
    sheet: XLSX.WorkSheet,
    row: number,
    col: number,
    style: CellStyle
  ): void {
    const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
    if (!sheet[cellAddress]) {
      sheet[cellAddress] = { t: 's', v: '' };
    }
    sheet[cellAddress].s = style;
  }

  /**
   * 应用样式到整行
   */
  private static applyRowStyle(
    sheet: XLSX.WorkSheet,
    row: number,
    range: XLSX.Range,
    style: CellStyle
  ): void {
    for (let col = range.s.c; col <= range.e.c; col++) {
      this.applyCellStyle(sheet, row, col, style);
    }
  }

  /**
   * 合并单元格
   */
  static mergeCells(
    sheet: XLSX.WorkSheet,
    startRow: number,
    startCol: number,
    endRow: number,
    endCol: number
  ): void {
    if (!sheet['!merges']) {
      sheet['!merges'] = [];
    }
    sheet['!merges'].push({
      s: { r: startRow, c: startCol },
      e: { r: endRow, c: endCol },
    });
  }

  /**
   * 设置列宽
   */
  static setColumnWidths(sheet: XLSX.WorkSheet, widths: number[]): void {
    sheet['!cols'] = widths.map(width => ({ wch: width }));
  }
}
