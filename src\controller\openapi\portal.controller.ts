import { Controller, Get, Inject, Query } from '@midwayjs/core';
import { StatisticService } from '../../service/statistic.service';
import { MountainService } from '../../service/mountain.service';
import { WaterSystemService } from '../../service/water-system.service';
import { HistoricalElementService } from '../../service/historical-element.service';

@Controller('/openapi/portal')
export class PublicPortalController {
  @Inject()
  statisticService: StatisticService;

  @Inject()
  mountainService: MountainService;

  @Inject()
  waterSystemService: WaterSystemService;

  @Inject()
  historicalElementService: HistoricalElementService;

  @Get('/overview')
  async getOverview(@Query('regionId') regionId?: number) {
    const validRegionId =
      regionId && !isNaN(Number(regionId)) ? Number(regionId) : undefined;

    const basicStats = await this.statisticService.getStatisticData({
      regionId: validRegionId,
    });

    const recentMountains = await this.mountainService.findAll({
      query: validRegionId ? { regionDictId: validRegionId } : {},
      offset: 0,
      limit: 5,
      order: [['createdAt', 'DESC']],
      attributes: ['id', 'name', 'code', 'createdAt'],
    });

    const recentWaterSystems = await this.waterSystemService.findAll({
      query: validRegionId ? { regionDictId: validRegionId } : {},
      offset: 0,
      limit: 5,
      order: [['createdAt', 'DESC']],
      attributes: ['id', 'name', 'code', 'createdAt'],
    });

    const recentHistoricalElements =
      await this.historicalElementService.findAll({
        query: validRegionId ? { regionDictId: validRegionId } : {},
        offset: 0,
        limit: 5,
        order: [['createdAt', 'DESC']],
        attributes: ['id', 'name', 'code', 'createdAt'],
      });

    return {
      statistics: {
        mountain: basicStats.counts.mountain,
        waterSystem: basicStats.counts.waterSystem,
        historicalElement: basicStats.counts.historicalElement,
      },
      regionDistribution:
        basicStats.regionStats?.map((r: any) => ({
          region: r.regionName || r.region,
          regionId: r.regionId,
          mountainCount: r.mountainCount ?? 0,
          waterSystemCount: r.waterSystemCount ?? 0,
          historicalElementCount: r.historicalElementCount ?? 0,
          total: r.total ?? r.count ?? 0,
        })) || [],
      recentData: {
        mountains: recentMountains.list,
        waterSystems: recentWaterSystems.list,
        historicalElements: recentHistoricalElements.list,
      },
    };
  }

  @Get('/map-markers')
  async getMapMarkers(
    @Query('types') types?: string,
    @Query('regionId') regionId?: number
  ) {
    const validRegionId =
      regionId && !isNaN(Number(regionId)) ? Number(regionId) : undefined;
    const typeSet = (
      types
        ? types.split(',')
        : ['mountain', 'waterSystem', 'historicalElement']
    )
      .map(t => t.trim())
      .filter(Boolean);

    const markers: Array<any> = [];

    if (typeSet.includes('mountain')) {
      const res = await this.mountainService.findAll({
        query: validRegionId ? { regionDictId: validRegionId } : {},
        attributes: ['id', 'name', 'longitude', 'latitude'],
        include: [
          {
            association: 'photos',
            attributes: ['id', 'name', 'url'],
            required: false, // LEFT JOIN，即使没有照片也返回山塬数据
            limit: 3, // 只获取前3张照片
          },
        ],
        // 不设置 limit 参数，获取所有数据
      });
      for (const m of res.list) {
        if (m.longitude != null && m.latitude != null) {
          const thumbnailUrl = m.photos?.length > 0 ? m.photos[0].url : null;

          markers.push({
            id: m.id,
            type: 'mountain',
            name: m.name,
            longitude: m.longitude,
            latitude: m.latitude,
            thumbnailUrl,
            summary: undefined,
            photos: m.photos || [],
          });
        }
      }
    }

    if (typeSet.includes('waterSystem')) {
      const res = await this.waterSystemService.findAll({
        query: validRegionId ? { regionDictId: validRegionId } : {},
        attributes: ['id', 'name', 'longitude', 'latitude'],
        include: [
          {
            association: 'photos',
            attributes: ['id', 'name', 'url'],
            required: false, // LEFT JOIN，即使没有照片也返回水系数据
            limit: 3, // 只获取前3张照片
          },
        ],
        // 不设置 limit 参数，获取所有数据
      });
      for (const w of res.list) {
        if (w.longitude != null && w.latitude != null) {
          const thumbnailUrl = w.photos?.length > 0 ? w.photos[0].url : null;

          markers.push({
            id: w.id,
            type: 'waterSystem',
            name: w.name,
            longitude: w.longitude,
            latitude: w.latitude,
            thumbnailUrl,
            summary: undefined,
            photos: w.photos || [],
          });
        }
      }
    }

    if (typeSet.includes('historicalElement')) {
      const res = await this.historicalElementService.findAll({
        query: validRegionId ? { regionDictId: validRegionId } : {},
        attributes: [
          'id',
          'name',
          'constructionLongitude',
          'constructionLatitude',
        ],
        include: [
          {
            association: 'photos',
            attributes: ['id', 'name', 'url'],
            required: false, // LEFT JOIN，即使没有照片也返回历史要素数据
            limit: 3, // 只获取前3张照片
          },
        ],
        // 不设置 limit 参数，获取所有数据
      });
      for (const h of res.list) {
        if (h.constructionLongitude != null && h.constructionLatitude != null) {
          const thumbnailUrl = h.photos?.length > 0 ? h.photos[0].url : null;

          markers.push({
            id: h.id,
            type: 'historicalElement',
            name: h.name,
            longitude: h.constructionLongitude,
            latitude: h.constructionLatitude,
            thumbnailUrl,
            summary: undefined,
            photos: h.photos || [],
          });
        }
      }
    }

    return markers;
  }
}
