# 文化要素统一表迁移计划

## 概述

将现有的山塬、水系、历史要素三张独立表合并为一张统一的文化要素表，实现数据结构统一和功能整合。

## 当前状况

### 现有表结构
- `mountain` - 山塬表（高度、经纬度）
- `water_system` - 水系表（长度/面积、经纬度）
- `historical_element` - 历史要素表（建筑经纬度、建造年份、位置描述）

### 现有功能模块
- 实体类：3个独立实体
- 服务类：3个独立服务
- 控制器：6个控制器（管理端3个 + 公开接口3个）
- DTO：6套DTO（创建/更新各3套）
- 导入功能：3个独立导入
- 统计功能：3个独立统计
- 地图服务：集成3种数据类型

## 目标架构

### 新表结构
- `cultural_element` - 统一文化要素表
- `ancient_city_dict` - 古城字典表（新增）
- 更新 `type_dict` - 重新设计类型层级

### 新功能架构
- 1个统一实体类
- 1个统一服务类
- 2个统一控制器（管理端 + 公开接口）
- 1套统一DTO
- 1个统一导入功能
- 1个统一统计功能
- 更新地图服务

## 详细实施步骤

### 第一步：数据库结构准备
**目标：** 创建新的数据库结构，不影响现有功能

#### 1.1 创建古城字典表
- 创建 `AncientCityDict` 实体类
- 创建对应的DTO和服务
- 添加初始化数据

#### 1.2 创建统一文化要素表
- 创建 `CulturalElement` 实体类
- 包含所有现有字段的并集
- 建立与古城字典的关联关系

#### 1.3 更新类型字典数据
- 重新设计类型字典层级结构
- 添加历史要素、自然要素、历史人物三大类
- 添加具体子类型数据

**预计工作量：** 1-2天
**风险评估：** 低，不影响现有功能

### 第二步：核心业务逻辑实现
**目标：** 实现新的统一业务逻辑

#### 2.1 创建统一服务类
- 创建 `CulturalElementService`
- 实现CRUD基础功能
- 实现按类型、区域、古城的查询功能
- 实现统计功能（替代原有3个统计）

#### 2.2 创建统一DTO
- 创建 `CreateCulturalElementDTO`
- 创建 `UpdateCulturalElementDTO`
- 支持所有字段的验证规则

#### 2.3 更新照片关联
- 更新 `Photo` 实体，添加 `culturalElementId` 字段
- 保持向后兼容（暂时保留原有关联字段）

**预计工作量：** 2-3天
**风险评估：** 中，需要仔细测试业务逻辑

### 第三步：接口层实现
**目标：** 提供新的统一接口

#### 3.1 创建管理端控制器
- 创建 `AdminCulturalElementController`
- 实现完整的CRUD接口
- 实现批量导入功能（替代原有3个导入）
- 实现统计接口

#### 3.2 创建公开接口控制器
- 创建 `PublicCulturalElementController`
- 实现查询和详情接口
- 支持按类型筛选

#### 3.3 更新地图服务
- 修改 `MapService`，使用新的统一数据源
- 保持接口兼容性

**预计工作量：** 2天
**风险评估：** 低，主要是接口适配

### 第四步：数据迁移
**目标：** 将现有数据迁移到新表

#### 4.1 编写迁移脚本
- 创建数据迁移服务 `MigrationService`
- 实现三张表到统一表的数据迁移
- 处理类型字典关联更新
- 处理照片关联关系迁移

#### 4.2 数据验证
- 验证迁移数据完整性
- 验证关联关系正确性
- 提供回滚机制

**预计工作量：** 1-2天
**风险评估：** 高，需要充分测试和备份

### 第五步：兼容性处理
**目标：** 确保平滑过渡

#### 5.1 保留原有接口（标记废弃）
- 在原有控制器添加 `@Deprecated` 标记
- 内部调用新的统一服务
- 保持响应格式兼容

#### 5.2 更新相关功能
- 更新导入模板和说明文档
- 更新API文档
- 更新前端接口调用（如需要）

**预计工作量：** 1天
**风险评估：** 低

### 第六步：清理和优化
**目标：** 清理冗余代码，优化性能

#### 6.1 代码清理（可选）
- 移除原有实体类、服务类、控制器
- 清理无用的DTO和导入功能
- 更新单元测试

#### 6.2 性能优化
- 优化查询性能
- 添加必要的索引
- 优化缓存策略

**预计工作量：** 1天
**风险评估：** 低

## 关键技术点

### 字段映射策略
```typescript
// 统一字段映射
const FIELD_MAPPING = {
  // 山塬特有字段
  height: '仅山塬类型使用',
  
  // 水系特有字段
  lengthArea: '仅水系类型使用',
  
  // 历史要素特有字段
  constructionYear: '仅历史要素类型使用',
  locationDescription: '仅历史要素类型使用',
  
  // 统一经纬度字段
  longitude: '统一使用，原 constructionLongitude 映射到此字段',
  latitude: '统一使用，原 constructionLatitude 映射到此字段'
};
```

### 类型判断逻辑
```typescript
// 根据类型字典判断要素类别
const getElementCategory = (typeDictId: number) => {
  // 通过类型字典的父级关系判断是山塬、水系还是历史要素
  // 用于前端显示和业务逻辑处理
};
```

## 风险控制

### 数据安全
1. **迁移前完整备份**
2. **分步迁移，每步验证**
3. **提供回滚方案**

### 功能兼容
1. **保留原有接口**
2. **渐进式切换**
3. **充分测试**

### 性能考虑
1. **合理设计索引**
2. **优化查询语句**
3. **监控性能指标**

## 总体时间规划

- **第一步：** 1-2天
- **第二步：** 2-3天  
- **第三步：** 2天
- **第四步：** 1-2天
- **第五步：** 1天
- **第六步：** 1天（可选）

**总计：** 8-11个工作日

## 验收标准

1. ✅ 新统一表结构创建完成
2. ✅ 数据迁移100%成功，无数据丢失
3. ✅ 新接口功能完整，包含CRUD、导入、统计
4. ✅ 原有接口保持兼容
5. ✅ 地图服务正常工作
6. ✅ 单元测试覆盖率不低于80%
7. ✅ API文档更新完成

## 后续优化建议

1. **前端界面统一**：统一三种要素的管理界面
2. **搜索优化**：实现跨类型的统一搜索
3. **数据分析**：基于统一数据结构的深度分析功能
4. **导出功能**：支持按类型或综合导出

---

## 📋 当前进度更新 (2025-10-08)

### ✅ 已完成阶段

- [x] **第一步：数据库结构准备** - 已完成
- [x] **第二步：核心业务逻辑实现** - 已完成
- [x] **第三步：接口层实现** - 已完成 ✅

### 🔄 当前阶段：前端接口迁移

**状态**: 等待前端测试和迁移

**已提供资源**:
1. **接口变更文档** - `docs/api-changes/cultural-element-api-migration.md`
2. **接口对照表** - `docs/api-changes/interface-mapping-table.md`
3. **前端迁移示例** - `docs/api-changes/frontend-migration-examples.md`
4. **API测试指南** - `docs/api-changes/api-testing-guide.md`
5. **在线测试页面** - http://127.0.0.1:7001/test-api.html

**新接口状态**:
- ✅ 管理端接口：`/admin/cultural-element/*`
- ✅ 公开接口：`/api/cultural-element/*`
- ✅ 地图接口：`/openapi/map/cultural-elements`
- ✅ 统一响应格式：`ApiResponse<T>`
- ✅ 错误处理机制
- ✅ 基础Excel功能框架

### ⏳ 待完成阶段

- [ ] **第四步：数据迁移** - 待前端测试完成后进行
- [ ] **第五步：兼容性处理** - 待前端测试完成后进行
- [ ] **第六步：清理和优化** - 待前端测试完成后进行

### 📞 下一步行动

1. **前端团队**: 使用提供的文档和测试工具验证新接口
2. **前端团队**: 逐步迁移现有功能到新接口
3. **后端团队**: 根据前端反馈调整接口实现
4. **双方协作**: 完成接口测试后进行数据迁移

---

*本文档将根据实施过程中的实际情况进行更新和调整*
