# 水系管理 API 文档

## 概述

水系管理模块提供水系数据的完整管理功能，包括河流、湖泊、水库等水体要素的创建、更新、删除、查询等操作，支持按区域筛选和批量导入。

## 相关文档

- [水系导入 API 文档](./water-system-import.md) - Excel批量导入功能详细说明

---

## 统一响应格式

### 成功响应
所有接口成功时返回统一格式：
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应
所有接口失败时返回统一格式：
```json
{
  "errCode": 400,  // 错误码
  "msg": "错误信息"  // 错误描述
}
```

---

## 创建水系

### 接口信息

- **URL**: `/admin/water-system`
- **方法**: `POST`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 水系名称，最大255字符 |
| code | string | 是 | 水系编号，最大50字符 |
| longitude | number | 是 | 经度，范围-180到180 |
| latitude | number | 是 | 纬度，范围-90到90 |
| lengthArea | string | 是 | 长度/面积，最大50字符 |
| historicalRecords | string | 否 | 历史文献记载 |
| regionDictId | number | 是 | 所属区域ID |

### 请求示例

```bash
curl -X POST "http://localhost:7001/admin/water-system" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "渭河",
    "code": "WH001",
    "longitude": 108.9633,
    "latitude": 34.2658,
    "lengthArea": "818公里",
    "historicalRecords": "渭河是黄河的最大支流，流经关中平原，是关中地区的母亲河。",
    "regionDictId": 1
  }'
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "name": "渭河",
    "code": "WH001",
    "longitude": 108.9633,
    "latitude": 34.2658,
    "lengthArea": "818公里",
    "historicalRecords": "渭河是黄河的最大支流，流经关中平原，是关中地区的母亲河。",
    "regionDictId": 1,
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:00:00.000Z"
  }
}
```

---

## 更新水系

### 接口信息

- **URL**: `/admin/water-system/{id}`
- **方法**: `PUT`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 水系ID |

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 否 | 水系名称 |
| code | string | 否 | 水系编号 |
| longitude | number | 否 | 经度 |
| latitude | number | 否 | 纬度 |
| lengthArea | string | 否 | 长度/面积 |
| historicalRecords | string | 否 | 历史文献记载 |
| regionDictId | number | 否 | 所属区域ID |

### 请求示例

```bash
curl -X PUT "http://localhost:7001/admin/water-system/1" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "lengthArea": "818公里，流域面积134766平方公里",
    "historicalRecords": "渭河是黄河的最大支流，全长818公里，流经关中平原，是关中地区的母亲河，孕育了灿烂的关中文化。"
  }'
```

---

## 删除水系

### 接口信息

- **URL**: `/admin/water-system/{id}`
- **方法**: `DELETE`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 水系ID |

### 请求示例

```bash
curl -X DELETE "http://localhost:7001/admin/water-system/1" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "message": "删除成功"
  }
}
```

---

## 获取水系列表

### 接口信息

- **URL**: `/admin/water-system`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，从1开始，默认1 |
| pageSize | number | 否 | 每页数量，1-100，默认10 |
| keyword | string | 否 | 搜索关键词，按名称模糊搜索 |
| regionId | number | 否 | 区域ID筛选 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/water-system?page=1&pageSize=10&keyword=渭河" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "渭河",
        "code": "WH001",
        "longitude": 108.9633,
        "latitude": 34.2658,
        "lengthArea": "818公里",
        "historicalRecords": "渭河是黄河的最大支流，流经关中平原，是关中地区的母亲河。",
        "regionDictId": 1,
        "regionDict": {
          "id": 1,
          "regionName": "关中地区",
          "regionCode": "GUANZHONG"
        }
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  }
}
```

---

## 获取水系详情

### 接口信息

- **URL**: `/admin/water-system/{id}`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 水系ID |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/water-system/1" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "name": "渭河",
    "code": "WH001",
    "longitude": 108.9633,
    "latitude": 34.2658,
    "lengthArea": "818公里",
    "historicalRecords": "渭河是黄河的最大支流，流经关中平原，是关中地区的母亲河。",
    "regionDictId": 1,
    "regionDict": {
      "id": 1,
      "regionName": "关中地区",
      "regionCode": "GUANZHONG"
    },
    "photos": [
      {
        "id": 1,
        "name": "渭河风光",
        "url": "/public/uploads/2024/01/15/weihe_1642234567890.jpg"
      }
    ]
  }
}
```

---

## 批量导入水系

### 接口信息

- **URL**: `/admin/water-system/batch-import`
- **方法**: `POST`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| waterSystems | array | 是 | 水系数据数组，每个元素包含创建水系所需的所有字段 |

### 请求示例

```bash
curl -X POST "http://localhost:7001/admin/water-system/batch-import" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "waterSystems": [
      {
        "name": "渭河",
        "code": "WH001",
        "longitude": 108.9633,
        "latitude": 34.2658,
        "lengthArea": "818公里",
        "regionDictId": 1
      },
      {
        "name": "泾河",
        "code": "JH001",
        "longitude": 108.8062,
        "latitude": 34.5269,
        "lengthArea": "455公里",
        "regionDictId": 1
      }
    ]
  }'
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "message": "批量导入成功"
  }
}
```

---

## 获取水系统计

### 接口信息

- **URL**: `/admin/water-system/statistics/overview`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID，筛选特定区域的统计数据 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/water-system/statistics/overview?regionId=1" \
  -H "Authorization: Bearer {token}"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "total": 15,
    "byRegion": [
      {
        "regionId": 1,
        "regionName": "关中地区",
        "count": 10
      },
      {
        "regionId": 2,
        "regionName": "陕北地区",
        "count": 5
      }
    ],
    "byLengthArea": [
      {
        "category": "河流",
        "count": 8
      },
      {
        "category": "湖泊",
        "count": 4
      },
      {
        "category": "水库",
        "count": 2
      },
      {
        "category": "其他",
        "count": 1
      }
    ]
  }
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| total | number | 水系总数 |
| byRegion | array | 按区域统计，包含regionId、regionName、count |
| byLengthArea | array | 按长度/面积类型统计，包含category、count |

---

## 根据区域获取水系

### 接口信息

- **URL**: `/admin/water-system/by-region/{regionId}`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 是 | 区域ID |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/water-system/by-region/1" \
  -H "Authorization: Bearer {token}"
```

---

## 根据长度/面积范围查询

### 接口信息

- **URL**: `/admin/water-system/by-length-area`
- **方法**: `GET`
- **认证**: 需要认证（管理员权限）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| minLength | number | 否 | 最小长度（公里） |
| maxLength | number | 否 | 最大长度（公里） |

### 请求示例

```bash
curl -X GET "http://localhost:7001/admin/water-system/by-length-area?minLength=100&maxLength=1000" \
  -H "Authorization: Bearer {token}"
```

---

## 数据字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 水系唯一标识 |
| name | string | 水系名称 |
| code | string | 水系编号 |
| longitude | number | 经度坐标（WGS84） |
| latitude | number | 纬度坐标（WGS84） |
| lengthArea | string | 长度/面积描述 |
| historicalRecords | string | 历史文献记载 |
| regionDictId | number | 所属区域ID |
| regionDict | object | 区域详细信息 |
| photos | array | 关联的照片列表 |
| createdAt | string | 创建时间 |
| updatedAt | string | 更新时间 |

---

## 注意事项

1. **坐标系统**: 使用WGS84坐标系统
2. **长度面积**: lengthArea字段支持灵活的描述格式，如"818公里"、"134766平方公里"等
3. **编号规则**: 建议使用有意义的编号，如"WH001"表示渭河001
4. **区域关联**: 必须关联到已存在的区域字典项
5. **删除影响**: 删除水系时会同时删除相关的照片和关系数据
6. **批量导入**: 支持批量导入，但需要确保数据格式正确
7. **权限控制**: 所有管理接口都需要管理员权限
8. **范围查询**: 长度/面积范围查询功能需要解析lengthArea字段中的数值
