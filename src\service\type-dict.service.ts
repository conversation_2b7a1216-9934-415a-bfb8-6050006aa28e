import { Provide, Inject } from '@midwayjs/core';
import { Op } from 'sequelize';
import { TypeDict } from '../entity/type-dict.entity';
import { CacheService } from './cache.service';
import { PageResponseDTO } from '../dto/common.dto';
import {
  CreateTypeDictDTO,
  UpdateTypeDictDTO,
  TypeDictQueryDTO,
  TypeDictResponseDTO,
} from '../dto/dictionary.dto';
import { buildTree } from '../utils/tree.util';

@Provide()
export class TypeDictService {
  @Inject()
  cacheService: CacheService;

  /**
   * 创建类型字典
   */
  async createTypeDict(
    createDto: CreateTypeDictDTO
  ): Promise<TypeDictResponseDTO> {
    // 验证数据
    await this.validateTypeDictData(createDto);

    // 检查编码唯一性
    await this.checkTypeCodeUnique(createDto.typeCode);

    const typeDict = await TypeDict.create(createDto as any);

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();

    return new TypeDictResponseDTO(typeDict.toJSON());
  }

  /**
   * 更新类型字典
   */
  async updateTypeDict(
    id: number,
    updateDto: UpdateTypeDictDTO
  ): Promise<TypeDictResponseDTO> {
    // 验证数据
    await this.validateTypeDictData(updateDto);

    const typeDict = await TypeDict.findByPk(id);
    if (!typeDict) {
      throw new Error('类型字典不存在');
    }

    // 检查编码唯一性（如果更新了编码）
    if (updateDto.typeCode && updateDto.typeCode !== typeDict.typeCode) {
      await this.checkTypeCodeUnique(updateDto.typeCode, id);
    }

    await typeDict.update(updateDto);

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();

    return new TypeDictResponseDTO(typeDict.toJSON());
  }

  /**
   * 删除类型字典（级联删除子级）
   */
  async deleteTypeDict(id: number): Promise<void> {
    const typeDict = await TypeDict.findByPk(id);
    if (!typeDict) {
      throw new Error('类型字典不存在');
    }

    // TODO: 检查是否有关联的历史要素等数据

    // 使用级联删除，会自动删除所有子级类型
    await typeDict.destroy();

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();
  }

  /**
   * 根据ID获取类型字典
   */
  async getTypeDictById(id: number): Promise<TypeDictResponseDTO | null> {
    const typeDict = await TypeDict.findByPk(id, {
      include: [
        {
          model: TypeDict,
          as: 'parent',
          attributes: ['id', 'typeName', 'typeCode'],
        },
        {
          model: TypeDict,
          as: 'children',
          attributes: ['id', 'typeName', 'typeCode', 'status'],
          where: { status: 1 },
          required: false,
        },
      ],
    });

    if (!typeDict) {
      return null;
    }

    return new TypeDictResponseDTO(typeDict.toJSON());
  }

  /**
   * 获取类型字典列表（缓存）
   */
  async getTypeDictList(): Promise<TypeDictResponseDTO[]> {
    const types = await this.cacheService.getTypeDictCache();
    return types.map(type => new TypeDictResponseDTO(type));
  }

  /**
   * 分页查询类型字典
   */
  async getTypeDictPage(
    queryDto: TypeDictQueryDTO
  ): Promise<PageResponseDTO<TypeDictResponseDTO>> {
    const { page = 1, pageSize = 10, keyword, status, parentId } = queryDto;
    const offset = (page - 1) * pageSize;

    const whereCondition: any = {};

    if (keyword) {
      whereCondition[Op.or] = [
        { typeCode: { [Op.like]: `%${keyword}%` } },
        { typeName: { [Op.like]: `%${keyword}%` } },
      ];
    }

    if (status !== undefined) {
      whereCondition.status = status;
    }

    if (parentId !== undefined) {
      whereCondition.parentId = parentId;
    }

    const { count, rows } = await TypeDict.findAndCountAll({
      where: whereCondition,
      limit: pageSize,
      offset,
      order: [
        ['sort', 'ASC'],
        ['id', 'ASC'],
      ],
      include: [
        {
          model: TypeDict,
          as: 'parent',
          attributes: ['id', 'typeName', 'typeCode'],
        },
      ],
    });

    const list = rows.map(row => new TypeDictResponseDTO(row.toJSON()));

    return new PageResponseDTO(list, count, page, pageSize);
  }

  /**
   * 获取类型字典树形结构
   */
  async getTypeDictTree(): Promise<TypeDictResponseDTO[]> {
    const types = await this.getTypeDictList();
    return buildTree(types);
  }

  /**
   * 根据类型名称查找类型字典
   */
  async getTypeDictByName(
    typeName: string
  ): Promise<TypeDictResponseDTO | null> {
    if (!typeName || typeName.trim().length === 0) {
      return null;
    }

    const typeDict = await TypeDict.findOne({
      where: {
        typeName: typeName.trim(),
        status: 1, // 只查找启用的类型
      },
      include: [
        {
          model: TypeDict,
          as: 'parent',
          attributes: ['id', 'typeName', 'typeCode'],
        },
      ],
    });

    if (!typeDict) {
      return null;
    }

    return new TypeDictResponseDTO(typeDict.toJSON());
  }

  /**
   * 批量更新状态
   */
  async batchUpdateStatus(ids: number[], status: number): Promise<void> {
    await TypeDict.update({ status }, { where: { id: { [Op.in]: ids } } });

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();
  }

  /**
   * 启用/禁用类型字典
   */
  async toggleStatus(id: number): Promise<{ status: number; message: string }> {
    const typeDict = await TypeDict.findByPk(id);
    if (!typeDict) {
      throw new Error('类型字典不存在');
    }

    const newStatus = typeDict.status === 1 ? 0 : 1;
    await typeDict.update({ status: newStatus });

    // 刷新缓存
    await this.cacheService.refreshDictionaryCache();

    return {
      status: newStatus,
      message: newStatus === 1 ? '类型字典已启用' : '类型字典已禁用',
    };
  }

  // ==================== 私有方法 ====================

  /**
   * 验证类型字典数据
   */
  private async validateTypeDictData(
    data: CreateTypeDictDTO | UpdateTypeDictDTO
  ): Promise<void> {
    if (data.typeCode && data.typeCode.trim().length === 0) {
      throw new Error('类型编码不能为空');
    }

    if (data.typeName && data.typeName.trim().length === 0) {
      throw new Error('类型名称不能为空');
    }

    // 验证父级类型是否存在
    if (data.parentId) {
      const parent = await TypeDict.findByPk(data.parentId);
      if (!parent) {
        throw new Error('父级类型不存在');
      }
    }
  }

  /**
   * 检查类型编码唯一性
   */
  private async checkTypeCodeUnique(
    typeCode: string,
    excludeId?: number
  ): Promise<void> {
    const whereCondition: any = { typeCode };
    if (excludeId) {
      whereCondition.id = { [Op.ne]: excludeId };
    }

    const existing = await TypeDict.findOne({ where: whereCondition });
    if (existing) {
      throw new Error('类型编码已存在');
    }
  }
}
