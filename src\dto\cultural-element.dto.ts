import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 创建文化要素DTO
 */
export class CreateCulturalElementDTO {
  @Rule(RuleType.string().required().max(255))
  name: string;

  @Rule(RuleType.string().optional().allow(null).max(50))
  code?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  typeDictId?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  ancientCityId?: number;

  @Rule(RuleType.number().integer().required())
  regionDictId: number;

  @Rule(RuleType.number().optional().allow(null).min(-180).max(180))
  longitude?: number;

  @Rule(RuleType.number().optional().allow(null).min(-90).max(90))
  latitude?: number;

  @Rule(RuleType.number().optional().allow(null).min(0))
  height?: number;

  @Rule(RuleType.string().optional().allow(null).max(100))
  lengthArea?: string;

  @Rule(RuleType.string().optional().allow(null).max(500))
  locationDescription?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  constructionYear?: number;

  @Rule(RuleType.string().optional().allow(null))
  historicalRecords?: string;
}

/**
 * 更新文化要素DTO
 */
export class UpdateCulturalElementDTO {
  @Rule(RuleType.string().optional().max(255))
  name?: string;

  @Rule(RuleType.string().optional().allow(null).max(50))
  code?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  typeDictId?: number;

  @Rule(RuleType.number().integer().optional().allow(null))
  ancientCityId?: number;

  @Rule(RuleType.number().integer().optional())
  regionDictId?: number;

  @Rule(RuleType.number().optional().allow(null).min(-180).max(180))
  longitude?: number;

  @Rule(RuleType.number().optional().allow(null).min(-90).max(90))
  latitude?: number;

  @Rule(RuleType.number().optional().allow(null).min(0))
  height?: number;

  @Rule(RuleType.string().optional().allow(null).max(100))
  lengthArea?: string;

  @Rule(RuleType.string().optional().allow(null).max(500))
  locationDescription?: string;

  @Rule(RuleType.number().integer().optional().allow(null))
  constructionYear?: number;

  @Rule(RuleType.string().optional().allow(null))
  historicalRecords?: string;
}

/**
 * 文化要素查询DTO
 */
export class CulturalElementQueryDTO {
  @Rule(RuleType.number().integer().optional().min(1))
  page?: number = 1;

  @Rule(RuleType.number().integer().optional().min(1).max(100))
  pageSize?: number = 10;

  @Rule(RuleType.string().optional().max(255))
  name?: string;

  @Rule(RuleType.string().optional().max(50))
  code?: string;

  @Rule(RuleType.number().integer().optional())
  typeDictId?: number;

  @Rule(RuleType.number().integer().optional())
  ancientCityId?: number;

  @Rule(RuleType.number().integer().optional())
  regionDictId?: number;

  @Rule(RuleType.number().integer().optional())
  constructionYear?: number;
}

/**
 * 文化要素响应DTO
 */
export class CulturalElementResponseDTO {
  id: number;
  name: string;
  code?: string;
  typeDictId?: number;
  ancientCityId?: number;
  regionDictId: number;
  longitude?: number;
  latitude?: number;
  height?: number;
  lengthArea?: string;
  locationDescription?: string;
  constructionYear?: number;
  historicalRecords?: string;
  createdAt: Date;
  updatedAt: Date;

  // 关联信息
  typeName?: string;
  regionName?: string;
  cityName?: string;

  constructor(
    id: number,
    name: string,
    code: string | undefined,
    typeDictId: number | undefined,
    ancientCityId: number | undefined,
    regionDictId: number,
    longitude: number | undefined,
    latitude: number | undefined,
    height: number | undefined,
    lengthArea: string | undefined,
    locationDescription: string | undefined,
    constructionYear: number | undefined,
    historicalRecords: string | undefined,
    createdAt: Date,
    updatedAt: Date,
    typeName?: string,
    regionName?: string,
    cityName?: string
  ) {
    this.id = id;
    this.name = name;
    this.code = code;
    this.typeDictId = typeDictId;
    this.ancientCityId = ancientCityId;
    this.regionDictId = regionDictId;
    this.longitude = longitude;
    this.latitude = latitude;
    this.height = height;
    this.lengthArea = lengthArea;
    this.locationDescription = locationDescription;
    this.constructionYear = constructionYear;
    this.historicalRecords = historicalRecords;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    this.typeName = typeName;
    this.regionName = regionName;
    this.cityName = cityName;
  }
}

/**
 * 文化要素统计DTO
 */
export class CulturalElementStatisticsDTO {
  total: number;
  byType: Array<{ typeName: string; count: number }>;
  byRegion: Array<{ regionName: string; count: number }>;
  byAncientCity: Array<{ cityName: string; count: number }>;
  byConstructionYear: Array<{ year: number; count: number }>;

  constructor(
    total: number,
    byType: Array<{ typeName: string; count: number }>,
    byRegion: Array<{ regionName: string; count: number }>,
    byAncientCity: Array<{ cityName: string; count: number }>,
    byConstructionYear: Array<{ year: number; count: number }>
  ) {
    this.total = total;
    this.byType = byType;
    this.byRegion = byRegion;
    this.byAncientCity = byAncientCity;
    this.byConstructionYear = byConstructionYear;
  }
}

/**
 * 批量创建文化要素DTO
 */
export class BatchCreateCulturalElementDTO {
  @Rule(RuleType.array().items(RuleType.object()).required())
  elements: CreateCulturalElementDTO[];
}

/**
 * 文化要素导入结果DTO
 */
export class CulturalElementImportResultDTO {
  successCount: number;
  failureCount: number;
  errors: Array<{
    row: number;
    name: string;
    error: string;
  }>;
  successElements: CulturalElementResponseDTO[];

  constructor(
    successCount: number,
    failureCount: number,
    errors: Array<{ row: number; name: string; error: string }>,
    successElements: CulturalElementResponseDTO[]
  ) {
    this.successCount = successCount;
    this.failureCount = failureCount;
    this.errors = errors;
    this.successElements = successElements;
  }
}
