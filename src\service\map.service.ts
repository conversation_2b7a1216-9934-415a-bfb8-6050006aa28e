import { Provide, Inject } from '@midwayjs/core';
import { MapDataQueryDTO } from '../dto/common.dto';
import { MountainService } from './mountain.service';
import { WaterSystemService } from './water-system.service';
import { HistoricalElementService } from './historical-element.service';
import { CulturalElementService } from './cultural-element.service';

@Provide()
export class MapService {
  @Inject()
  mountainService: MountainService;

  @Inject()
  waterSystemService: WaterSystemService;

  @Inject()
  historicalElementService: HistoricalElementService;

  @Inject()
  culturalElementService: CulturalElementService;

  /**
   * 获取统一的文化要素地图数据（新版本）
   */
  async getCulturalElementMapData(query: MapDataQueryDTO) {
    const { regionId, typeId } = query;
    const ancientCityId = (query as any).ancientCityId; // 临时处理

    const whereConditions: any = {};

    if (regionId) {
      whereConditions.regionDictId = regionId;
    }

    if (typeId) {
      whereConditions.typeDictId = typeId;
    }

    if (ancientCityId) {
      whereConditions.ancientCityId = ancientCityId;
    }

    try {
      const culturalElements = await this.culturalElementService.findByPage({
        ...whereConditions,
        page: 1,
        pageSize: 10000, // 获取所有数据用于地图显示
      });

      // 转换为地图所需的格式
      const mapData = culturalElements.list.map((element: any) => ({
        id: element.id,
        name: element.name,
        code: element.code,
        longitude: element.longitude,
        latitude: element.latitude,
        height: element.height,
        lengthArea: element.lengthArea,
        locationDescription: element.locationDescription,
        constructionYear: element.constructionYear,
        historicalRecords: element.historicalRecords,
        typeName: element.typeName || '未分类',
        regionName: element.regionName || '未知区域',
        ancientCityName: element.ancientCityName || '未知古城',
        photos: [], // 暂时不包含照片数据
      }));

      return {
        total: culturalElements.total,
        data: mapData,
      };
    } catch (error) {
      console.error('获取文化要素地图数据失败:', error);
      throw new Error('获取地图数据失败');
    }
  }

  /**
   * 获取地图数据（兼容旧版本）
   */
  async getMapData(query: MapDataQueryDTO) {
    // 直接返回简单的测试数据
    return {
      mountains: [],
      waterSystems: [],
      historicalElements: [],
      message: '地图数据接口正常工作',
    };
  }

  /**
   * 获取详情数据
   */
  async getDetailData(type: string, id: number) {
    try {
      // 暂时返回简单的测试数据
      return {
        id,
        type,
        name: `测试${type}`,
        photos: [],
        relationships: [],
      };
    } catch (error) {
      console.error('获取详情数据失败:', error);
      throw new Error('获取详情数据失败');
    }
  }

  /**
   * 获取地图统计数据
   */
  async getMapStatistics(regionId?: number) {
    // 暂时返回简单的测试数据
    return {
      mountains: { total: 0 },
      waterSystems: { total: 0 },
      historicalElements: { total: 0 },
      total: {
        mountain: 0,
        waterSystem: 0,
        historicalElement: 0,
      },
    };
  }
}
