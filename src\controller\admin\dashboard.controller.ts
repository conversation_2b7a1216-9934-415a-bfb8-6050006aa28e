import { Controller, Get, Query, Inject } from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { StatisticService } from '../../service/statistic.service';
import { MountainService } from '../../service/mountain.service';
import { WaterSystemService } from '../../service/water-system.service';
import { HistoricalElementService } from '../../service/historical-element.service';
import { StatisticQueryDTO } from '../../dto/common.dto';

// 数据质量报告相关接口定义
interface MissingFieldItem {
  field: string;
  count: number;
}

interface DistributionItem {
  region?: string;
  type?: string;
  count: number;
  percentage: number;
}

interface DataQualityReport {
  completeness: {
    mountain: number;
    waterSystem: number;
    historicalElement: number;
  };
  missingFields: {
    mountain: MissingFieldItem[];
    waterSystem: MissingFieldItem[];
    historicalElement: MissingFieldItem[];
  };
  dataDistribution: {
    byRegion: DistributionItem[];
    byType: DistributionItem[];
  };
}

/**
 * 管理端仪表盘控制器
 */
@Controller('/admin/dashboard')
export class AdminDashboardController {
  @Inject()
  statisticService: StatisticService;

  @Inject()
  mountainService: MountainService;

  @Inject()
  waterSystemService: WaterSystemService;

  @Inject()
  historicalElementService: HistoricalElementService;

  /**
   * 获取仪表盘概览数据
   */
  @Get('/overview')
  async getOverview(@Query('regionId') regionId?: number): Promise<{
    statistics: {
      mountain: number;
      waterSystem: number;
      historicalElement: number;
      user: number;
      typeDict: number;
      regionDict: number;
      relationshipDict: number;
    };
    regionDistribution: Array<{
      regionId: number;
      regionName: string;
      count: number;
      percentage: number;
    }>;
    recentData: {
      mountains: Array<Record<string, any>>;
      waterSystems: Array<Record<string, any>>;
      historicalElements: Array<Record<string, any>>;
    };
  }> {
    // 验证regionId参数
    const validRegionId =
      regionId && !isNaN(Number(regionId)) ? Number(regionId) : undefined;

    const basicStats = await this.statisticService.getStatisticData({
      regionId: validRegionId,
    });

    // 获取最近添加的数据
    const recentMountains = await this.mountainService.findAll({
      query: {},
      offset: 0,
      limit: 5,
      order: [['createdAt', 'DESC']],
    });

    const recentWaterSystems = await this.waterSystemService.findAll({
      query: {},
      offset: 0,
      limit: 5,
      order: [['createdAt', 'DESC']],
    });

    const recentHistoricalElements =
      await this.historicalElementService.findAll({
        query: {},
        offset: 0,
        limit: 5,
        order: [['createdAt', 'DESC']],
      });

    return {
      statistics: basicStats.counts,
      regionDistribution: basicStats.regionStats,
      recentData: {
        mountains: recentMountains.list,
        waterSystems: recentWaterSystems.list,
        historicalElements: recentHistoricalElements.list,
      },
    };
  }

  /**
   * 获取详细统计数据
   */
  @Get('/statistics')
  @Validate()
  async getDetailedStatistics(@Query() query: StatisticQueryDTO): Promise<{
    basic: {
      counts: {
        mountain: number;
        waterSystem: number;
        historicalElement: number;
      };
      regionStats: Array<{
        regionId: number;
        regionName: string;
        count: number;
        percentage: number;
      }>;
      timelineData: any[];
    };
    detailed: {
      mountains: any;
      waterSystems: any;
      historicalElements: any;
    };
    summary: {
      totalEntities: number;
      regionCoverage: number;
      timeSpan: {
        earliest: number | null;
        latest: number | null;
        span: number;
      };
    };
  }> {
    // 使用统计服务获取综合报告数据
    const data = await this.statisticService.getComprehensiveReport(
      query.regionId
    );
    return data;
  }

  /**
   * 获取数据增长趋势
   */
  @Get('/growth-trend')
  async getGrowthTrend(@Query('period') period = 'month'): Promise<{
    mountains: Array<{ date: string; count: number }>;
    waterSystems: Array<{ date: string; count: number }>;
    historicalElements: Array<{ date: string; count: number }>;
  }> {
    // 获取当前日期
    const now = new Date();
    // 定义趋势数据的接口
    interface TrendItem {
      date: string;
      count: number;
    }

    const trends: {
      mountains: TrendItem[];
      waterSystems: TrendItem[];
      historicalElements: TrendItem[];
    } = {
      mountains: [],
      waterSystems: [],
      historicalElements: [],
    };

    // 根据周期计算数据
    if (period === 'week') {
      // 获取过去7天的数据
      for (let i = 6; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        const startOfDay = new Date(date.setHours(0, 0, 0, 0));

        // 这里应该是实际查询数据库获取每天新增的数据量
        // 为了演示，这里使用模拟数据
        trends.mountains.push({
          date: startOfDay.toISOString().split('T')[0],
          count: Math.floor(Math.random() * 5),
        });

        trends.waterSystems.push({
          date: startOfDay.toISOString().split('T')[0],
          count: Math.floor(Math.random() * 3),
        });

        trends.historicalElements.push({
          date: startOfDay.toISOString().split('T')[0],
          count: Math.floor(Math.random() * 8),
        });
      }
    } else if (period === 'month') {
      // 获取过去30天的数据
      for (let i = 29; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        const startOfDay = new Date(date.setHours(0, 0, 0, 0));

        // 这里应该是实际查询数据库获取每天新增的数据量
        // 为了演示，这里使用模拟数据
        trends.mountains.push({
          date: startOfDay.toISOString().split('T')[0],
          count: Math.floor(Math.random() * 5),
        });

        trends.waterSystems.push({
          date: startOfDay.toISOString().split('T')[0],
          count: Math.floor(Math.random() * 3),
        });

        trends.historicalElements.push({
          date: startOfDay.toISOString().split('T')[0],
          count: Math.floor(Math.random() * 8),
        });
      }
    } else if (period === 'year') {
      // 获取过去12个月的数据
      for (let i = 11; i >= 0; i--) {
        const date = new Date(now);
        date.setMonth(date.getMonth() - i);
        const monthStr = `${date.getFullYear()}-${String(
          date.getMonth() + 1
        ).padStart(2, '0')}`;

        // 这里应该是实际查询数据库获取每月新增的数据量
        // 为了演示，这里使用模拟数据
        trends.mountains.push({
          date: monthStr,
          count: Math.floor(Math.random() * 15),
        });

        trends.waterSystems.push({
          date: monthStr,
          count: Math.floor(Math.random() * 10),
        });

        trends.historicalElements.push({
          date: monthStr,
          count: Math.floor(Math.random() * 25),
        });
      }
    }

    return trends;
  }

  /**
   * 获取数据质量报告
   */
  @Get('/data-quality')
  async getDataQualityReport(): Promise<DataQualityReport> {
    // 这里应该是实际查询数据库获取数据质量信息
    // 为了演示，这里使用模拟数据

    const report: DataQualityReport = {
      completeness: {
        mountain: 95, // 95% 的山塬数据完整
        waterSystem: 90, // 90% 的水系数据完整
        historicalElement: 85, // 85% 的历史要素数据完整
      },
      missingFields: {
        mountain: [
          { field: 'height', count: 5 },
          { field: 'historicalRecords', count: 10 },
        ],
        waterSystem: [
          { field: 'lengthArea', count: 8 },
          { field: 'historicalRecords', count: 12 },
        ],
        historicalElement: [
          { field: 'constructionTime', count: 15 },
          { field: 'description', count: 20 },
        ],
      },
      dataDistribution: {
        byRegion: [
          { region: '关中地区', count: 120, percentage: 40 },
          { region: '陕北地区', count: 90, percentage: 30 },
          { region: '陕南地区', count: 90, percentage: 30 },
        ],
        byType: [
          { type: '山塬', count: 100, percentage: 33.3 },
          { type: '水系', count: 80, percentage: 26.7 },
          { type: '历史要素', count: 120, percentage: 40 },
        ],
      },
    };

    return report;
  }
}
