# 接口映射对照表

## ⚠️ 重要提醒：统计接口权限区分

**前端门户网站**应该使用：
```
GET /api/cultural-element/statistics  (无需认证)
```

**后台管理系统**应该使用：
```
GET /admin/cultural-element/statistics  (需要管理员认证)
```

## 快速对照表

### 管理端接口映射

| 功能 | 旧接口 | 新接口 | 状态 |
|------|--------|--------|------|
| **历史要素管理** |
| 创建历史要素 | `POST /admin/historical-element` | `POST /admin/cultural-element` | ✅ 可用 |
| 更新历史要素 | `PUT /admin/historical-element/:id` | `PUT /admin/cultural-element/:id` | ✅ 可用 |
| 删除历史要素 | `DELETE /admin/historical-element/:id` | `DELETE /admin/cultural-element/:id` | ✅ 可用 |
| 获取历史要素详情 | `GET /admin/historical-element/:id` | `GET /admin/cultural-element/:id` | ✅ 可用 |
| 分页查询历史要素 | `GET /admin/historical-element` | `GET /admin/cultural-element?typeDictId=1,2,3,4,5` | ✅ 可用 |
| 历史要素统计 | `GET /admin/historical-element/statistics` | `GET /admin/cultural-element/statistics` | ✅ 可用 |
| 导出历史要素 | `POST /admin/historical-element/export` | `POST /admin/cultural-element/export` | ✅ 可用 |
| 导入历史要素 | `POST /admin/historical-element/import` | `POST /admin/cultural-element/import/execute` | ✅ 可用 |
| **山塬管理** |
| 创建山塬 | `POST /admin/mountain` | `POST /admin/cultural-element` | ✅ 可用 |
| 更新山塬 | `PUT /admin/mountain/:id` | `PUT /admin/cultural-element/:id` | ✅ 可用 |
| 删除山塬 | `DELETE /admin/mountain/:id` | `DELETE /admin/cultural-element/:id` | ✅ 可用 |
| 获取山塬详情 | `GET /admin/mountain/:id` | `GET /admin/cultural-element/:id` | ✅ 可用 |
| 分页查询山塬 | `GET /admin/mountain` | `GET /admin/cultural-element?typeDictId=6,7,8` | ✅ 可用 |
| 山塬统计 | `GET /admin/mountain/statistics` | `GET /admin/cultural-element/statistics` | ✅ 可用 |
| 导出山塬 | `POST /admin/mountain/export` | `POST /admin/cultural-element/export` | ✅ 可用 |
| 导入山塬 | `POST /admin/mountain/import` | `POST /admin/cultural-element/import/execute` | ✅ 可用 |
| **水系管理** |
| 创建水系 | `POST /admin/water-system` | `POST /admin/cultural-element` | ✅ 可用 |
| 更新水系 | `PUT /admin/water-system/:id` | `PUT /admin/cultural-element/:id` | ✅ 可用 |
| 删除水系 | `DELETE /admin/water-system/:id` | `DELETE /admin/cultural-element/:id` | ✅ 可用 |
| 获取水系详情 | `GET /admin/water-system/:id` | `GET /admin/cultural-element/:id` | ✅ 可用 |
| 分页查询水系 | `GET /admin/water-system` | `GET /admin/cultural-element?typeDictId=9,10,11` | ✅ 可用 |
| 水系统计 | `GET /admin/water-system/statistics` | `GET /admin/cultural-element/statistics` | ✅ 可用 |
| 导出水系 | `POST /admin/water-system/export` | `POST /admin/cultural-element/export` | ✅ 可用 |
| 导入水系 | `POST /admin/water-system/import` | `POST /admin/cultural-element/import/execute` | ✅ 可用 |

### 公开接口映射

| 功能 | 旧接口 | 新接口 | 状态 |
|------|--------|--------|------|
| **历史要素查询** |
| 获取历史要素详情 | `GET /openapi/historical-element/:id` | `GET /api/cultural-element/:id` | ✅ 可用 |
| 分页查询历史要素 | `GET /openapi/historical-element` | `GET /api/cultural-element?typeDictId=1,2,3,4,5` | ✅ 可用 |
| 按区域查询历史要素 | `GET /openapi/historical-element/region/:id` | `GET /api/cultural-element/region/:id` | ✅ 可用 |
| 历史要素统计 | `GET /openapi/historical-element/statistics` | `GET /api/cultural-element/statistics` | ✅ 可用 |
| **山塬查询** |
| 获取山塬详情 | `GET /openapi/mountain/:id` | `GET /api/cultural-element/:id` | ✅ 可用 |
| 分页查询山塬 | `GET /openapi/mountain` | `GET /api/cultural-element?typeDictId=6,7,8` | ✅ 可用 |
| 按区域查询山塬 | `GET /openapi/mountain/region/:id` | `GET /api/cultural-element/region/:id` | ✅ 可用 |
| 山塬统计 | `GET /openapi/mountain/statistics` | `GET /api/cultural-element/statistics` | ✅ 可用 |
| **水系查询** |
| 获取水系详情 | `GET /openapi/water-system/:id` | `GET /api/cultural-element/:id` | ✅ 可用 |
| 分页查询水系 | `GET /openapi/water-system` | `GET /api/cultural-element?typeDictId=9,10,11` | ✅ 可用 |
| 按区域查询水系 | `GET /openapi/water-system/region/:id` | `GET /api/cultural-element/region/:id` | ✅ 可用 |
| 水系统计 | `GET /openapi/water-system/statistics` | `GET /api/cultural-element/statistics` | ✅ 可用 |

### 接口使用场景说明

| 接口类型 | 使用场景 | 权限要求 | 示例 |
|----------|----------|----------|------|
| **管理端接口** (`/admin/*`) | 后台管理系统 | 需要认证 | 数据管理、统计报表 |
| **公开接口** (`/api/*`) | 前端门户网站 | 无需认证 | 数据展示、搜索查询 |
| **兼容接口** (`/openapi/*`) | 地图服务等 | 无需认证 | 地图数据、详情展示 |

### 地图接口映射

| 功能 | 旧接口 | 新接口 | 状态 |
|------|--------|--------|------|
| 获取地图数据 | `GET /openapi/map/data` | `GET /openapi/map/cultural-elements` | ✅ 可用 |
| 获取详情数据 | `GET /openapi/map/detail` | `GET /openapi/map/detail` | ✅ 兼容 |
| 获取统计数据 | `GET /openapi/map/statistics` | `GET /openapi/map/statistics` | ✅ 兼容 |

## 类型ID映射

```typescript
// 根据实际数据库数据确定具体的类型ID
const TYPE_ID_MAPPING = {
  // 历史要素类型
  HISTORICAL_ELEMENTS: {
    '古建筑': 1,
    '古遗址': 2, 
    '古墓葬': 3,
    '石窟寺': 4,
    '近现代重要史迹': 5,
  },
  
  // 自然要素类型  
  NATURAL_ELEMENTS: {
    '山塬': 6,
    '河流': 7,
    '湖泊': 8,
    '森林': 9,
    '草原': 10,
    '湿地': 11,
  },
  
  // 历史人物类型
  HISTORICAL_FIGURES: {
    '政治人物': 12,
    '军事人物': 13,
    '文化人物': 14,
    '宗教人物': 15,
    '科技人物': 16,
  }
};
```

## 查询参数对照

### 旧接口查询参数
```typescript
// 历史要素查询
interface HistoricalElementQuery {
  page?: number;
  pageSize?: number;
  name?: string;
  regionId?: number;
  type?: string;
}

// 山塬查询
interface MountainQuery {
  page?: number;
  pageSize?: number;
  name?: string;
  regionId?: number;
  height?: number;
}

// 水系查询  
interface WaterSystemQuery {
  page?: number;
  pageSize?: number;
  name?: string;
  regionId?: number;
  length?: number;
}
```

### 新接口统一查询参数
```typescript
interface CulturalElementQuery {
  page?: number;
  pageSize?: number;
  name?: string;
  code?: string;
  typeDictId?: number | number[]; // 支持多个类型ID
  regionDictId?: number;
  ancientCityId?: number;
  constructionYear?: number;
  // 兼容旧参数
  regionId?: number; // 映射到 regionDictId
  type?: string; // 映射到 typeDictId
  height?: number; // 保留
  length?: number; // 映射到 lengthArea
}
```

## 响应数据对照

### 旧接口响应格式
```typescript
// 直接返回数据
{
  data: T[],
  total: number,
  page: number,
  pageSize: number
}
```

### 新接口响应格式
```typescript
// 统一响应格式
{
  success: true,
  message: "操作成功",
  data: {
    list: T[],
    total: number,
    page: number,
    pageSize: number
  },
  timestamp: "2025-10-08T12:00:00.000Z"
}
```

## 迁移优先级

### 高优先级（建议立即迁移）
1. 新功能开发直接使用新接口
2. 地图相关功能
3. 统计报表功能

### 中优先级（可逐步迁移）
1. 列表页面
2. 详情页面
3. 搜索功能

### 低优先级（可延后迁移）
1. 导入导出功能
2. 批量操作功能

---

**说明**: 
- ✅ 表示新接口已实现并可用
- 🔄 表示正在开发中
- ⏳ 表示计划中
- ❌ 表示已废弃
