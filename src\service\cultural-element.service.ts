import { Provide } from '@midwayjs/core';
import { Op } from 'sequelize';
import { CulturalElement } from '../entity/cultural-element.entity';
import { TypeDict } from '../entity/type-dict.entity';
import { RegionDict } from '../entity/region-dict.entity';
import { AncientCityDict } from '../entity/ancient-city-dict.entity';
import { Photo } from '../entity/photo.entity';
import {
  CreateCulturalElementDTO,
  UpdateCulturalElementDTO,
  CulturalElementQueryDTO,
  CulturalElementResponseDTO,
} from '../dto/cultural-element.dto';
import { PageResponseDTO } from '../dto/common.dto';
import { BaseService } from '../common/BaseService';

@Provide()
export class CulturalElementService extends BaseService<CulturalElement> {

  getModel() {
    return CulturalElement;
  }

  /**
   * 创建文化要素
   */
  async createCulturalElement(
    dto: CreateCulturalElementDTO
  ): Promise<CulturalElementResponseDTO> {
    // 验证类型字典是否存在
    if (dto.typeDictId) {
      const typeDict = await TypeDict.findOne({
        where: { id: dto.typeDictId, status: 1 },
      });
      if (!typeDict) {
        throw new Error('指定的类型不存在或已禁用');
      }
    }

    // 验证区域字典是否存在
    const regionDict = await RegionDict.findOne({
      where: { id: dto.regionDictId, status: 1 },
    });
    if (!regionDict) {
      throw new Error('指定的区域不存在或已禁用');
    }

    // 验证古城字典是否存在
    if (dto.ancientCityId) {
      const ancientCity = await AncientCityDict.findOne({
        where: { id: dto.ancientCityId, status: 1 },
      });
      if (!ancientCity) {
        throw new Error('指定的古城不存在或已禁用');
      }
    }

    // 检查编号是否重复
    if (dto.code) {
      const existingElement = await CulturalElement.findOne({
        where: { code: dto.code },
      });
      if (existingElement) {
        throw new Error('编号已存在');
      }
    }

    const culturalElement = await CulturalElement.create(dto);

    return this.buildResponseDTO(culturalElement);
  }

  /**
   * 更新文化要素
   */
  async updateCulturalElement(
    id: number,
    dto: UpdateCulturalElementDTO
  ): Promise<CulturalElementResponseDTO> {
    const existingElement = await CulturalElement.findByPk(id);
    if (!existingElement) {
      throw new Error('文化要素不存在');
    }

    // 验证类型字典是否存在
    if (dto.typeDictId) {
      const typeDict = await TypeDict.findOne({
        where: { id: dto.typeDictId, status: 1 },
      });
      if (!typeDict) {
        throw new Error('指定的类型不存在或已禁用');
      }
    }

    // 验证区域字典是否存在
    if (dto.regionDictId) {
      const regionDict = await RegionDict.findOne({
        where: { id: dto.regionDictId, status: 1 },
      });
      if (!regionDict) {
        throw new Error('指定的区域不存在或已禁用');
      }
    }

    // 验证古城字典是否存在
    if (dto.ancientCityId) {
      const ancientCity = await AncientCityDict.findOne({
        where: { id: dto.ancientCityId, status: 1 },
      });
      if (!ancientCity) {
        throw new Error('指定的古城不存在或已禁用');
      }
    }

    // 检查编号是否重复（排除当前记录）
    if (dto.code && dto.code !== existingElement.code) {
      const duplicateElement = await CulturalElement.findOne({
        where: { code: dto.code },
      });
      if (duplicateElement) {
        throw new Error('编号已存在');
      }
    }

    // 更新字段
    await existingElement.update(dto);

    return this.buildResponseDTO(existingElement);
  }

  /**
   * 删除文化要素
   */
  async deleteCulturalElement(id: number): Promise<void> {
    const existingElement = await CulturalElement.findByPk(id);
    if (!existingElement) {
      throw new Error('文化要素不存在');
    }

    // 检查是否有关联的照片
    const photoCount = await Photo.count({
      where: { culturalElementId: id },
    });
    if (photoCount > 0) {
      throw new Error('该文化要素下还有照片，请先删除相关照片');
    }

    await existingElement.destroy();
  }

  /**
   * 根据ID获取文化要素
   */
  async findCulturalElementById(
    id: number
  ): Promise<CulturalElementResponseDTO | null> {
    const element = await CulturalElement.findByPk(id, {
      include: [
        { model: TypeDict, as: 'typeDict' },
        { model: RegionDict, as: 'regionDict' },
        { model: AncientCityDict, as: 'ancientCity' },
        { model: Photo, as: 'photos' },
      ],
    });

    if (!element) {
      return null;
    }

    return this.buildResponseDTO(element);
  }

  /**
   * 分页查询文化要素
   */
  async findByPage(
    query: CulturalElementQueryDTO
  ): Promise<PageResponseDTO<CulturalElementResponseDTO>> {
    const { page = 1, pageSize = 10, ...filters } = query;
    const offset = (page - 1) * pageSize;

    const whereConditions: any = {};

    // 构建查询条件
    if (filters.name) {
      whereConditions.name = { [Op.like]: `%${filters.name}%` };
    }
    if (filters.code) {
      whereConditions.code = { [Op.like]: `%${filters.code}%` };
    }
    if (filters.typeDictId) {
      whereConditions.typeDictId = filters.typeDictId;
    }
    if (filters.regionDictId) {
      whereConditions.regionDictId = filters.regionDictId;
    }
    if (filters.ancientCityId) {
      whereConditions.ancientCityId = filters.ancientCityId;
    }
    if (filters.constructionYear) {
      whereConditions.constructionYear = filters.constructionYear;
    }

    const { rows: elements, count: total } =
      await CulturalElement.findAndCountAll({
        where: whereConditions,
        include: [
          { model: TypeDict, as: 'typeDict' },
          { model: RegionDict, as: 'regionDict' },
          { model: AncientCityDict, as: 'ancientCity' },
        ],
        offset,
        limit: pageSize,
        order: [['id', 'DESC']],
      });

    const data = elements.map(element => this.buildResponseDTO(element));

    return new PageResponseDTO(data, total, page, pageSize);
  }

  /**
   * 按类型查询文化要素
   */
  async findByType(typeDictId: number): Promise<CulturalElementResponseDTO[]> {
    const elements = await CulturalElement.findAll({
      where: { typeDictId },
      include: [
        { model: TypeDict, as: 'typeDict' },
        { model: RegionDict, as: 'regionDict' },
        { model: AncientCityDict, as: 'ancientCity' },
      ],
      order: [['id', 'DESC']],
    });

    return elements.map(element => this.buildResponseDTO(element));
  }

  /**
   * 按区域查询文化要素
   */
  async findByRegion(
    regionDictId: number
  ): Promise<CulturalElementResponseDTO[]> {
    const elements = await CulturalElement.findAll({
      where: { regionDictId },
      include: [
        { model: TypeDict, as: 'typeDict' },
        { model: RegionDict, as: 'regionDict' },
        { model: AncientCityDict, as: 'ancientCity' },
      ],
      order: [['id', 'DESC']],
    });

    return elements.map(element => this.buildResponseDTO(element));
  }

  /**
   * 按古城查询文化要素
   */
  async findByAncientCity(
    ancientCityId: number
  ): Promise<CulturalElementResponseDTO[]> {
    const elements = await CulturalElement.findAll({
      where: { ancientCityId },
      include: [
        { model: TypeDict, as: 'typeDict' },
        { model: RegionDict, as: 'regionDict' },
        { model: AncientCityDict, as: 'ancientCity' },
      ],
      order: [['id', 'DESC']],
    });

    return elements.map(element => this.buildResponseDTO(element));
  }

  /**
   * 获取统计信息
   */
  async getStatistics(): Promise<{
    total: number;
    byType: Array<{ typeName: string; count: number }>;
    byRegion: Array<{ regionName: string; count: number }>;
    byAncientCity: Array<{ cityName: string; count: number }>;
    byConstructionYear: Array<{ year: number; count: number }>;
  }> {
    // 总数统计
    const total = await CulturalElement.count();

    // 暂时返回简化的统计数据
    return {
      total,
      byType: [
        { typeName: '古建筑', count: 0 },
        { typeName: '古遗址', count: 0 },
        { typeName: '山塬', count: 0 },
        { typeName: '河流', count: 0 },
      ],
      byRegion: [
        { regionName: '西安市', count: 0 },
        { regionName: '咸阳市', count: 0 },
      ],
      byAncientCity: [
        { cityName: '长安', count: 0 },
        { cityName: '咸阳', count: 0 },
      ],
      byConstructionYear: [
        { year: 2000, count: 0 },
        { year: 2001, count: 0 },
      ],
    };
  }

  /**
   * 批量创建文化要素
   */
  async batchCreateCulturalElements(
    dtos: CreateCulturalElementDTO[]
  ): Promise<CulturalElementResponseDTO[]> {
    const results: CulturalElementResponseDTO[] = [];

    for (const dto of dtos) {
      try {
        const result = await this.createCulturalElement(dto);
        results.push(result);
      } catch (error) {
        // 记录错误但继续处理其他记录
        console.error(`创建文化要素失败: ${dto.name}`, error);
        throw error; // 重新抛出错误，让调用方处理
      }
    }

    return results;
  }

  /**
   * 根据编号查询文化要素
   */
  async findByCode(code: string): Promise<CulturalElementResponseDTO | null> {
    const element = await CulturalElement.findOne({
      where: { code },
      include: [
        { model: TypeDict, as: 'typeDict' },
        { model: RegionDict, as: 'regionDict' },
        { model: AncientCityDict, as: 'ancientCity' },
        { model: Photo, as: 'photos' },
      ],
    });

    if (!element) {
      return null;
    }

    return this.buildResponseDTO(element);
  }

  /**
   * 获取所有文化要素（不分页）
   */
  async findAllCulturalElements(): Promise<CulturalElementResponseDTO[]> {
    const elements = await CulturalElement.findAll({
      include: [
        { model: TypeDict, as: 'typeDict' },
        { model: RegionDict, as: 'regionDict' },
        { model: AncientCityDict, as: 'ancientCity' },
      ],
      order: [['id', 'DESC']],
    });

    return elements.map(element => this.buildResponseDTO(element));
  }

  /**
   * 构建响应DTO
   */
  private buildResponseDTO(
    element: CulturalElement
  ): CulturalElementResponseDTO {
    return new CulturalElementResponseDTO(
      element.id,
      element.name,
      element.code,
      element.typeDictId,
      element.ancientCityId,
      element.regionDictId,
      element.longitude,
      element.latitude,
      element.height,
      element.lengthArea,
      element.locationDescription,
      element.constructionYear,
      element.historicalRecords,
      element.createdAt,
      element.updatedAt,
      element.typeDict?.typeName,
      element.regionDict?.regionName,
      element.ancientCity?.cityName
    );
  }
}
