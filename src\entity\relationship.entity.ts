import {
  Table,
  Column,
  DataType,
  BelongsTo,
  HasMany,
  ForeignKey,
  Model,
} from 'sequelize-typescript';
import { RelationshipDict } from './relationship-dict.entity';

export interface RelationshipAttributes {
  /** ID */
  id: number;
  /** 关系类型ID（引用关系字典） */
  relationDictId: number;
  /** 父级关系ID */
  parentRelationshipId?: number;

  // 关系主体（源）
  /** 源实体类型 (ancient_city, cultural_element) */
  sourceEntityType: string;
  /** 源实体ID */
  sourceEntityId: number;

  // 关系客体（目标）
  /** 目标实体类型 (ancient_city, cultural_element) */
  targetEntityType: string;
  /** 目标实体ID */
  targetEntityId: number;

  /** 关系方向/位置描述 (前有, 后有, 上有, 下有, 东连, 西连, 南为, 北为, 等) */
  direction?: string;
  /** 关系描述 */
  description?: string;
  /** 历史记载内容 */
  historicalRecord?: string;
  /** 排序号 */
  sort?: number;
  /** 状态 (1启用, 0禁用) */
  status?: number;
}

/**
 * 关系表模型
 */
@Table({
  tableName: 'relationship',
  comment: '关系表',
})
export class Relationship
  extends Model<RelationshipAttributes>
  implements RelationshipAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => RelationshipDict)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关系类型ID（引用关系字典）',
    field: 'relation_dict_id',
  })
  relationDictId: number;

  @ForeignKey(() => Relationship)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '父级关系ID',
    field: 'parent_relationship_id',
  })
  parentRelationshipId: number;

  @Column({
    type: DataType.ENUM('ancient_city', 'cultural_element'),
    allowNull: false,
    comment: '源实体类型（ancient_city=古城, cultural_element=文化要素）',
    field: 'source_entity_type',
  })
  sourceEntityType: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '源实体ID',
    field: 'source_entity_id',
  })
  sourceEntityId: number;

  @Column({
    type: DataType.ENUM('ancient_city', 'cultural_element'),
    allowNull: false,
    comment: '目标实体类型（ancient_city=古城, cultural_element=文化要素）',
    field: 'target_entity_type',
  })
  targetEntityType: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '目标实体ID',
    field: 'target_entity_id',
  })
  targetEntityId: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment:
      '关系方向/位置描述（前有, 后有, 上有, 下有, 东连, 西连, 南为, 北为等）',
  })
  direction: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '关系描述',
  })
  description: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '历史记载内容',
    field: 'historical_record',
  })
  historicalRecord: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '排序号',
  })
  sort: number;

  @Column({
    type: DataType.TINYINT,
    allowNull: false,
    defaultValue: 1,
    comment: '状态（1启用，0禁用）',
  })
  status: number;

  // 关联关系
  @BelongsTo(() => RelationshipDict, 'relationDictId')
  relationDict: RelationshipDict;

  // 自关联关系
  @BelongsTo(() => Relationship, 'parentRelationshipId')
  parent: Relationship;

  @HasMany(() => Relationship, 'parentRelationshipId')
  children: Relationship[];
}
