# 关系网络接口详细说明

## 接口概述
关系网络接口提供实体间关联关系的查询和可视化数据，支持构建知识图谱和关系网络图。

## 1. 获取网络图数据

### 接口地址
`GET /openapi/relationship/network-graph`

### 功能说明
获取关系网络的图形化数据，包含节点、连接和分类信息，用于构建可视化的关系网络图。

### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| sourceType | string | 否 | - | 源实体类型：mountain/waterSystem/historicalElement |
| targetType | string | 否 | - | 目标实体类型：mountain/waterSystem/historicalElement |
| relationDictId | number | 否 | - | 关系类型ID |
| status | number | 否 | 1 | 状态筛选（1=启用，0=禁用） |

### 响应示例
```json
{
  "errCode": 0,
  "data": {
    "nodes": [
      {
        "id": "mountain_1",
        "name": "华山",
        "type": "mountain",
        "category": "山塬",
        "size": 10,
        "color": "#ff6b6b",
        "x": 100,
        "y": 100
      },
      {
        "id": "water_1",
        "name": "渭河",
        "type": "waterSystem",
        "category": "水系",
        "size": 8,
        "color": "#4ecdc4",
        "x": 200,
        "y": 150
      },
      {
        "id": "historical_1",
        "name": "华清池",
        "type": "historicalElement",
        "category": "历史要素",
        "size": 12,
        "color": "#45b7d1",
        "x": 150,
        "y": 200
      }
    ],
    "links": [
      {
        "source": "mountain_1",
        "target": "water_1",
        "relation": "临近",
        "direction": "bidirectional",
        "term": "地理位置",
        "weight": 1,
        "color": "#95a5a6"
      },
      {
        "source": "historical_1",
        "target": "mountain_1",
        "relation": "依托",
        "direction": "unidirectional",
        "term": "地理依托",
        "weight": 2,
        "color": "#e74c3c"
      }
    ],
    "categories": [
      {
        "name": "山塬",
        "color": "#ff6b6b"
      },
      {
        "name": "水系",
        "color": "#4ecdc4"
      },
      {
        "name": "历史要素",
        "color": "#45b7d1"
      }
    ]
  },
  "msg": "OK"
}
```

### 数据结构说明

#### nodes 节点数据
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | string | 节点唯一标识，格式：{type}_{id} |
| name | string | 节点名称 |
| type | string | 节点类型：mountain/waterSystem/historicalElement |
| category | string | 节点分类名称 |
| size | number | 节点大小（用于可视化） |
| color | string | 节点颜色（十六进制） |
| x | number | 节点X坐标（可选） |
| y | number | 节点Y坐标（可选） |

#### links 连接数据
| 字段名 | 类型 | 说明 |
|--------|------|------|
| source | string | 源节点ID |
| target | string | 目标节点ID |
| relation | string | 关系名称 |
| direction | string | 关系方向：unidirectional/bidirectional |
| term | string | 关系术语 |
| weight | number | 关系权重 |
| color | string | 连接颜色（十六进制） |

#### categories 分类数据
| 字段名 | 类型 | 说明 |
|--------|------|------|
| name | string | 分类名称 |
| color | string | 分类颜色（十六进制） |

## 2. 根据要素获取关联关系

### 接口地址
`GET /openapi/relationship/by-element/{elementType}/{elementId}`

### 功能说明
根据指定的实体类型和ID获取其所有关联的关系列表。

### 路径参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| elementType | string | 是 | 实体类型：mountain/waterSystem/historicalElement |
| elementId | number | 是 | 实体ID |

### 响应示例
```json
{
  "errCode": 0,
  "data": [
    {
      "id": 1,
      "sourceType": "mountain",
      "sourceElement": {
        "id": 1,
        "name": "华山",
        "code": "HS001"
      },
      "targetType": "waterSystem",
      "targetElement": {
        "id": 1,
        "name": "渭河",
        "code": "WH001"
      },
      "relationDict": {
        "id": 1,
        "relationName": "临近",
        "relationCode": "ADJACENT"
      },
      "direction": "bidirectional",
      "term": "地理位置",
      "record": "华山与渭河地理位置相邻"
    }
  ],
  "msg": "OK"
}
```

## 3. 获取关系统计

### 接口地址
`GET /openapi/relationship/statistics`

### 功能说明
获取关系数据的统计信息，包括各类关系的数量分布。

### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| sourceType | string | 否 | - | 源实体类型筛选 |
| targetType | string | 否 | - | 目标实体类型筛选 |
| relationDictId | number | 否 | - | 关系类型ID筛选 |

### 响应示例
```json
{
  "errCode": 0,
  "data": {
    "totalRelations": 150,
    "byRelationType": [
      {
        "relationName": "临近",
        "relationCode": "ADJACENT",
        "count": 50
      },
      {
        "relationName": "依托",
        "relationCode": "DEPEND",
        "count": 30
      }
    ],
    "bySourceType": [
      {
        "type": "mountain",
        "count": 60
      },
      {
        "type": "waterSystem",
        "count": 40
      },
      {
        "type": "historicalElement",
        "count": 50
      }
    ],
    "byTargetType": [
      {
        "type": "mountain",
        "count": 45
      },
      {
        "type": "waterSystem",
        "count": 55
      },
      {
        "type": "historicalElement",
        "count": 50
      }
    ]
  },
  "msg": "OK"
}
```

## 4. 搜索关系

### 接口地址
`GET /openapi/relationship/search`

### 功能说明
根据关键词搜索关系数据，支持在关系名称、实体名称等字段中搜索。

### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| keyword | string | 是 | - | 搜索关键词 |
| sourceType | string | 否 | - | 源实体类型筛选 |
| targetType | string | 否 | - | 目标实体类型筛选 |
| relationDictId | number | 否 | - | 关系类型ID筛选 |

### 响应示例
```json
{
  "errCode": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "sourceType": "mountain",
        "sourceElement": {
          "id": 1,
          "name": "华山",
          "code": "HS001"
        },
        "targetType": "historicalElement",
        "targetElement": {
          "id": 1,
          "name": "华清池",
          "code": "HQC001"
        },
        "relationDict": {
          "id": 2,
          "relationName": "依托",
          "relationCode": "DEPEND"
        },
        "direction": "unidirectional",
        "term": "地理依托",
        "record": "华清池依托华山而建"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 50
  },
  "msg": "OK"
}
```

## 使用示例

### 构建关系网络图
```javascript
// 获取网络图数据
fetch('/openapi/relationship/network-graph')
  .then(response => response.json())
  .then(data => {
    if (data.errCode === 0) {
      const { nodes, links, categories } = data.data;
      
      // 使用 ECharts 构建关系图
      const option = {
        title: {
          text: '文化要素关系网络图'
        },
        tooltip: {},
        legend: {
          data: categories.map(cat => cat.name)
        },
        series: [{
          type: 'graph',
          layout: 'force',
          data: nodes,
          links: links,
          categories: categories,
          roam: true,
          force: {
            repulsion: 100
          }
        }]
      };
      
      // 渲染图表
      echarts.init(document.getElementById('network-chart')).setOption(option);
    }
  });
```

### 查询特定实体的关联关系
```javascript
// 查询华山的所有关联关系
const elementType = 'mountain';
const elementId = 1;

fetch(`/openapi/relationship/by-element/${elementType}/${elementId}`)
  .then(response => response.json())
  .then(data => {
    if (data.errCode === 0) {
      const relations = data.data;
      
      console.log(`华山共有 ${relations.length} 个关联关系:`);
      relations.forEach(relation => {
        const target = relation.targetElement;
        const relationType = relation.relationDict.relationName;
        console.log(`- 与 ${target.name} 的关系: ${relationType}`);
      });
    }
  });
```

### 搜索包含特定关键词的关系
```javascript
// 搜索包含"华"字的关系
fetch('/openapi/relationship/search?keyword=华')
  .then(response => response.json())
  .then(data => {
    if (data.errCode === 0) {
      const { list, total } = data.data;
      
      console.log(`找到 ${total} 个相关关系:`);
      list.forEach(relation => {
        const source = relation.sourceElement.name;
        const target = relation.targetElement.name;
        const relationType = relation.relationDict.relationName;
        console.log(`${source} ${relationType} ${target}`);
      });
    }
  });
```

### 获取关系统计用于数据分析
```javascript
fetch('/openapi/relationship/statistics')
  .then(response => response.json())
  .then(data => {
    if (data.errCode === 0) {
      const stats = data.data;
      
      console.log(`总关系数: ${stats.totalRelations}`);
      
      // 分析关系类型分布
      console.log('关系类型分布:');
      stats.byRelationType.forEach(type => {
        console.log(`- ${type.relationName}: ${type.count}个`);
      });
      
      // 分析实体类型分布
      console.log('源实体类型分布:');
      stats.bySourceType.forEach(type => {
        console.log(`- ${type.type}: ${type.count}个`);
      });
    }
  });
```

## 可视化建议

### 1. 网络图布局
- 使用力导向布局算法自动排列节点
- 根据关系权重调整连接线的粗细
- 使用不同颜色区分不同类型的实体

### 2. 交互功能
- 支持节点拖拽和缩放
- 点击节点显示详细信息
- 支持筛选特定类型的关系

### 3. 数据分析
- 计算节点的度中心性，识别重要节点
- 分析关系的密度和聚类特征
- 识别关系网络中的社区结构

### 4. 性能优化
- 对于大型网络，支持分层显示
- 实现节点和关系的懒加载
- 提供简化视图和详细视图切换
