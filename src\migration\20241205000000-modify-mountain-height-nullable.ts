import { QueryInterface, DataTypes } from 'sequelize';

export default {
  async up(queryInterface: QueryInterface): Promise<void> {
    // 修改山塬表的height字段为可空
    await queryInterface.changeColumn('mountains', 'height', {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '山塬高度',
    });
  },

  async down(queryInterface: QueryInterface): Promise<void> {
    // 回滚：将height字段改回非空（注意：这可能会失败如果有null值）
    await queryInterface.changeColumn('mountains', 'height', {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '山塬高度',
    });
  },
};
