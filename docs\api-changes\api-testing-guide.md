# API接口测试指南

## 测试环境

- **服务地址**: http://127.0.0.1:7001
- **API前缀**:
  - 管理端: `/admin` (需要认证)
  - 公开接口: `/api` (无需认证，用于前端门户)
  - 兼容接口: `/openapi` (无需认证，用于地图服务)

## ⚠️ 重要：接口权限说明

| 接口类型 | 示例 | 权限要求 | 使用场景 |
|----------|------|----------|----------|
| **公开接口** | `/api/cultural-element/statistics` | 无需认证 | 前端门户网站 |
| **管理端接口** | `/admin/cultural-element/statistics` | 需要管理员认证 | 后台管理系统 |
| **兼容接口** | `/openapi/map/cultural-elements` | 无需认证 | 地图服务等 |

## 1. 基础接口测试

### 1.1 获取文化要素列表

```bash
# 获取所有文化要素
curl -X GET "http://127.0.0.1:7001/api/cultural-element" \
  -H "Content-Type: application/json"

# 分页查询
curl -X GET "http://127.0.0.1:7001/api/cultural-element?page=1&pageSize=10" \
  -H "Content-Type: application/json"

# 按类型筛选（历史要素）
curl -X GET "http://127.0.0.1:7001/api/cultural-element?typeDictId=1,2,3,4,5" \
  -H "Content-Type: application/json"

# 按区域筛选
curl -X GET "http://127.0.0.1:7001/api/cultural-element?regionDictId=1" \
  -H "Content-Type: application/json"

# 按古城筛选
curl -X GET "http://127.0.0.1:7001/api/cultural-element?ancientCityId=1" \
  -H "Content-Type: application/json"
```

### 1.2 获取文化要素详情

```bash
# 获取指定ID的文化要素详情
curl -X GET "http://127.0.0.1:7001/api/cultural-element/1" \
  -H "Content-Type: application/json"
```

### 1.3 统计接口测试

```bash
# 获取公开统计信息（前端门户使用，无需认证）
curl -X GET "http://127.0.0.1:7001/api/cultural-element/statistics" \
  -H "Content-Type: application/json"

# 按类型分组
curl -X GET "http://127.0.0.1:7001/api/cultural-element/grouped-by-type" \
  -H "Content-Type: application/json"

# 按区域分组
curl -X GET "http://127.0.0.1:7001/api/cultural-element/grouped-by-region" \
  -H "Content-Type: application/json"

# 按古城分组
curl -X GET "http://127.0.0.1:7001/api/cultural-element/grouped-by-ancient-city" \
  -H "Content-Type: application/json"
```

### 1.4 搜索接口测试

```bash
# 关键词搜索
curl -X GET "http://127.0.0.1:7001/api/cultural-element/search?keyword=古城" \
  -H "Content-Type: application/json"

# 根据编号查询
curl -X GET "http://127.0.0.1:7001/api/cultural-element/code/CE001" \
  -H "Content-Type: application/json"

# 获取热门文化要素
curl -X GET "http://127.0.0.1:7001/api/cultural-element/popular?limit=5" \
  -H "Content-Type: application/json"

# 获取推荐文化要素
curl -X GET "http://127.0.0.1:7001/api/cultural-element/recommended?limit=5" \
  -H "Content-Type: application/json"
```

## 2. 地图接口测试

### 2.1 新版统一地图接口

```bash
# 获取统一的文化要素地图数据
curl -X GET "http://127.0.0.1:7001/openapi/map/cultural-elements" \
  -H "Content-Type: application/json"

# 带筛选条件的地图数据
curl -X GET "http://127.0.0.1:7001/openapi/map/cultural-elements?regionId=1&typeId=1" \
  -H "Content-Type: application/json"
```

### 2.2 兼容性地图接口

```bash
# 传统分类地图数据（兼容旧版本）
curl -X GET "http://127.0.0.1:7001/openapi/map/data" \
  -H "Content-Type: application/json"

# 获取详情数据
curl -X GET "http://127.0.0.1:7001/openapi/map/detail?type=cultural-element&id=1" \
  -H "Content-Type: application/json"

# 获取统计数据
curl -X GET "http://127.0.0.1:7001/openapi/map/statistics?regionId=1" \
  -H "Content-Type: application/json"
```

## 3. 管理端接口测试

### 3.1 创建文化要素

```bash
curl -X POST "http://127.0.0.1:7001/admin/cultural-element" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "测试文化要素",
    "code": "TEST001",
    "typeDictId": 1,
    "regionDictId": 1,
    "ancientCityId": 1,
    "longitude": 108.9398,
    "latitude": 34.3416,
    "height": 100,
    "locationDescription": "测试位置描述",
    "constructionYear": 2000,
    "historicalRecords": "测试历史记录"
  }'
```

### 3.2 更新文化要素

```bash
curl -X PUT "http://127.0.0.1:7001/admin/cultural-element/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "更新后的文化要素",
    "locationDescription": "更新后的位置描述"
  }'
```

### 3.3 删除文化要素

```bash
curl -X DELETE "http://127.0.0.1:7001/admin/cultural-element/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3.4 管理端统计接口

```bash
# 获取管理端统计信息（需要认证）
curl -X GET "http://127.0.0.1:7001/admin/cultural-element/statistics" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3.5 批量操作

```bash
# 批量创建
curl -X POST "http://127.0.0.1:7001/admin/cultural-element/batch" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "elements": [
      {
        "name": "批量测试1",
        "code": "BATCH001",
        "typeDictId": 1,
        "regionDictId": 1
      },
      {
        "name": "批量测试2",
        "code": "BATCH002",
        "typeDictId": 2,
        "regionDictId": 1
      }
    ]
  }'

# 导出Excel
curl -X POST "http://127.0.0.1:7001/admin/cultural-element/export" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "typeDictId": [1, 2, 3],
    "regionDictId": 1
  }' \
  --output cultural-elements.xlsx

# 下载导入模板
curl -X GET "http://127.0.0.1:7001/admin/cultural-element/template" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  --output template.xlsx
```

## 4. 响应格式示例

### 4.1 成功响应

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "测试文化要素",
        "code": "TEST001",
        "typeDictId": 1,
        "ancientCityId": 1,
        "regionDictId": 1,
        "longitude": 108.9398,
        "latitude": 34.3416,
        "height": 100,
        "locationDescription": "测试位置描述",
        "constructionYear": 2000,
        "historicalRecords": "测试历史记录",
        "createdAt": "2025-10-08T12:00:00.000Z",
        "updatedAt": "2025-10-08T12:00:00.000Z",
        "typeName": "古建筑",
        "regionName": "西安市",
        "ancientCityName": "长安"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "timestamp": "2025-10-08T12:00:00.000Z"
}
```

### 4.2 错误响应

```json
{
  "success": false,
  "message": "参数验证失败",
  "code": 400,
  "data": {
    "errors": [
      {
        "field": "name",
        "message": "名称不能为空"
      }
    ]
  },
  "timestamp": "2025-10-08T12:00:00.000Z"
}
```

### 4.3 统计数据响应

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "total": 100,
    "byType": [
      { "typeName": "古建筑", "count": 30 },
      { "typeName": "古遗址", "count": 25 },
      { "typeName": "山塬", "count": 20 },
      { "typeName": "河流", "count": 15 },
      { "typeName": "湖泊", "count": 10 }
    ],
    "byRegion": [
      { "regionName": "西安市", "count": 50 },
      { "regionName": "咸阳市", "count": 30 },
      { "regionName": "宝鸡市", "count": 20 }
    ],
    "byAncientCity": [
      { "cityName": "长安", "count": 40 },
      { "cityName": "咸阳", "count": 35 },
      { "cityName": "宝鸡", "count": 25 }
    ]
  },
  "timestamp": "2025-10-08T12:00:00.000Z"
}
```

## 5. 测试工具推荐

### 5.1 Postman集合

可以导入以下Postman集合进行测试：

```json
{
  "info": {
    "name": "文化要素API测试",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://127.0.0.1:7001"
    }
  ],
  "item": [
    {
      "name": "获取文化要素列表",
      "request": {
        "method": "GET",
        "url": "{{baseUrl}}/api/cultural-element"
      }
    }
  ]
}
```

### 5.2 前端测试代码

```javascript
// 简单的前端测试代码
const testApi = async () => {
  const baseUrl = 'http://127.0.0.1:7001';
  
  try {
    // 测试获取列表
    const listResponse = await fetch(`${baseUrl}/api/cultural-element`);
    const listData = await listResponse.json();
    console.log('列表数据:', listData);
    
    // 测试获取统计
    const statsResponse = await fetch(`${baseUrl}/api/cultural-element/statistics`);
    const statsData = await statsResponse.json();
    console.log('统计数据:', statsData);
    
    // 测试地图数据
    const mapResponse = await fetch(`${baseUrl}/openapi/map/cultural-elements`);
    const mapData = await mapResponse.json();
    console.log('地图数据:', mapData);
    
  } catch (error) {
    console.error('测试失败:', error);
  }
};

// 在浏览器控制台中运行
testApi();
```

## 6. 常见问题

### 6.1 CORS问题
如果遇到跨域问题，请确保后端已配置CORS：

```javascript
// 前端请求时添加模式
fetch(url, {
  mode: 'cors',
  credentials: 'include'
})
```

### 6.2 认证问题
管理端接口需要认证，请在请求头中添加token：

```javascript
fetch(url, {
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN',
    'Content-Type': 'application/json'
  }
})
```

### 6.3 数据格式问题
确保请求和响应的数据格式正确：
- 请求Content-Type: application/json
- 响应格式遵循统一的ApiResponse结构

---

通过以上测试用例，可以验证新接口的功能是否正常，为前端迁移提供可靠的基础。
