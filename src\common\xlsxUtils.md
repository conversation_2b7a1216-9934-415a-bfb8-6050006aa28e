# XlsxUtils 工具类使用说明

## 概述

XlsxUtils 是一个通用的 Excel 操作工具类，提供 Excel 文件生成、解析、样式美化等功能。该工具类完全无业务逻辑，可在任何项目中复用。

## 核心功能

### 1. Excel 模板生成
- 支持丰富的样式定制
- 自动表头美化（必填项红色标识）
- 示例数据填充
- 多种布局支持

### 2. Excel 文件解析
- 支持 .xlsx 和 .xls 格式
- 异步数据验证
- 详细错误定位
- 数据类型转换

### 3. 样式美化
- 表头样式定制
- 必填项标识
- 边框和对齐
- 列宽自适应

## 接口定义

### ExcelTemplateConfig
```typescript
interface ExcelTemplateConfig {
  title: string;           // 模板标题
  instructions: string[];  // 填写说明
  headers: HeaderConfig[]; // 字段配置
  exampleData?: any[];     // 示例数据
  sheetName?: string;      // 工作表名称
}

interface HeaderConfig {
  label: string;           // 表头显示名称
  key: string;            // 对应的数据字段
  required?: boolean;     // 是否必填
  description?: string;   // 字段说明
  width?: number;         // 列宽
}
```

### ExcelParseResult
```typescript
interface ExcelParseResult<T = any> {
  success: boolean;           // 是否解析成功
  data?: T[];                // 解析后的数据
  errors?: ExcelValidationError[]; // 错误信息
  totalRows?: number;        // 总行数
  validRows?: number;        // 有效行数
}
```

### ExcelValidationError
```typescript
interface ExcelValidationError {
  row: number;               // 错误所在行号
  field: string;             // 错误字段
  value: any;                // 错误值
  message: string;           // 错误描述
}
```

## 使用方法

### 1. 生成 Excel 模板
```typescript
import { XlsxUtils, ExcelTemplateConfig } from './xlsxUtils';

const config: ExcelTemplateConfig = {
  title: '数据导入模板',
  instructions: [
    '请按照以下要求填写数据：',
    '1. 带*号的字段为必填项',
    '2. 请严格按照示例格式填写'
  ],
  headers: [
    {
      label: '姓名',
      key: 'name',
      required: true,
      description: '用户姓名，必填',
      width: 20
    },
    {
      label: '邮箱',
      key: 'email',
      required: true,
      description: '邮箱地址，必填',
      width: 30
    }
  ],
  exampleData: [
    { name: '张三', email: '<EMAIL>' },
    { name: '李四', email: '<EMAIL>' }
  ],
  sheetName: '用户数据'
};

const buffer = XlsxUtils.generateTemplate(config);
```

### 2. 解析 Excel 文件
```typescript
// 定义验证函数
const validator = (data: any, rowIndex: number): ExcelValidationError[] => {
  const errors: ExcelValidationError[] = [];
  
  if (!data.name) {
    errors.push({
      row: rowIndex,
      field: '姓名',
      value: data.name,
      message: '姓名不能为空'
    });
  }
  
  return errors;
};

// 解析文件
const result = await XlsxUtils.parseExcel(
  filePath,
  config,
  validator
);

if (result.success) {
  console.log('解析成功:', result.data);
} else {
  console.log('验证错误:', result.errors);
}
```

### 3. 异步验证支持
```typescript
const asyncValidator = async (data: any, rowIndex: number): Promise<ExcelValidationError[]> => {
  const errors: ExcelValidationError[] = [];
  
  // 异步数据库查询验证
  const exists = await checkUserExists(data.email);
  if (exists) {
    errors.push({
      row: rowIndex,
      field: '邮箱',
      value: data.email,
      message: '邮箱已存在'
    });
  }
  
  return errors;
};

const result = await XlsxUtils.parseExcel(filePath, config, asyncValidator);
```

## 样式特性

### 1. 自动样式美化
- 标题行：加粗、居中、背景色
- 表头行：加粗、居中、边框
- 必填项：红色字体标识
- 说明行：灰色字体、左对齐

### 2. 布局结构
```
第1行：模板标题（加粗、居中）
第2行：空行
第3-N行：填写说明（每行一条）
第N+1行：空行
第N+2行：表头（字段名称）
第N+3行：字段说明
第N+4行：空行
第N+5行开始：示例数据
```

## 技术特性

### 1. 依赖库
- `xlsx`: Excel 文件读写
- `xlsx-style`: 样式支持

### 2. 支持格式
- 读取：.xlsx, .xls
- 生成：.xlsx

### 3. 性能优化
- 流式处理大文件
- 内存优化
- 异步验证

### 4. 错误处理
- 详细错误定位
- 友好错误描述
- 批量错误收集

## 注意事项

1. **文件编码**：确保 Excel 文件使用 UTF-8 编码
2. **内存限制**：大文件处理时注意内存使用
3. **异步验证**：验证函数支持同步和异步两种模式
4. **样式兼容**：生成的文件在不同 Excel 版本中保持兼容

## 扩展示例

### 自定义样式
```typescript
// 工具类内部已实现标准样式
// 如需自定义，可修改 generateTemplate 方法中的样式定义
```

### 多工作表支持
```typescript
// 当前版本支持单工作表
// 多工作表功能可通过扩展 ExcelTemplateConfig 实现
```

### 数据转换
```typescript
// 解析后的数据可通过转换函数处理
const convertedData = result.data?.map(item => ({
  ...item,
  createdAt: new Date()
}));
```
