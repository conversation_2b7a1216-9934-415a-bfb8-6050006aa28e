import { Controller, Get, Param, Query, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Validate } from '@midwayjs/validate';
import { CulturalElementService } from '../../service/cultural-element.service';
import { CulturalElementQueryDTO } from '../../dto/cultural-element.dto';
import { ApiResponse } from '../../common/api-response';

@Controller('/api/cultural-element')
export class PublicCulturalElementController {
  @Inject()
  ctx: Context;

  @Inject()
  culturalElementService: CulturalElementService;

  /**
   * 根据ID获取文化要素详情
   */
  @Get('/:id')
  async getById(@Param('id') id: number) {
    try {
      const result = await this.culturalElementService.findCulturalElementById(
        id
      );
      if (!result) {
        return ApiResponse.error('文化要素不存在');
      }
      return ApiResponse.success(result);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 分页查询文化要素
   */
  @Get('/')
  @Validate()
  async getByPage(@Query() query: CulturalElementQueryDTO) {
    try {
      const result = await this.culturalElementService.findByPage(query);
      return ApiResponse.success(result);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 按类型查询文化要素
   */
  @Get('/type/:typeDictId')
  async getByType(@Param('typeDictId') typeDictId: number) {
    try {
      const result = await this.culturalElementService.findByType(typeDictId);
      return ApiResponse.success(result);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 按区域查询文化要素
   */
  @Get('/region/:regionDictId')
  async getByRegion(@Param('regionDictId') regionDictId: number) {
    try {
      const result = await this.culturalElementService.findByRegion(
        regionDictId
      );
      return ApiResponse.success(result);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 按古城查询文化要素
   */
  @Get('/ancient-city/:ancientCityId')
  async getByAncientCity(@Param('ancientCityId') ancientCityId: number) {
    try {
      const result = await this.culturalElementService.findByAncientCity(
        ancientCityId
      );
      return ApiResponse.success(result);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 获取统计信息
   */
  @Get('/statistics')
  async getStatistics() {
    try {
      const result = await this.culturalElementService.getStatistics();
      return ApiResponse.success(result);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 根据编号查询文化要素
   */
  @Get('/code/:code')
  async getByCode(@Param('code') code: string) {
    try {
      const result = await this.culturalElementService.findByCode(code);
      if (!result) {
        return ApiResponse.error('文化要素不存在');
      }
      return ApiResponse.success(result);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 获取所有文化要素（用于地图显示等）
   */
  @Get('/all')
  async getAll() {
    try {
      const result =
        await this.culturalElementService.findAllCulturalElements();
      return ApiResponse.success(result);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 按类型分组获取文化要素
   */
  @Get('/grouped-by-type')
  async getGroupedByType() {
    try {
      const statistics = await this.culturalElementService.getStatistics();
      const groupedData = [];

      for (const typeInfo of statistics.byType) {
        // 这里需要根据类型名称获取类型ID，然后查询该类型下的文化要素
        // 为了简化，我们先返回统计信息
        groupedData.push({
          typeName: typeInfo.typeName,
          count: typeInfo.count,
          // elements: await this.culturalElementService.findByType(typeId)
        });
      }

      return ApiResponse.success(groupedData);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 按区域分组获取文化要素
   */
  @Get('/grouped-by-region')
  async getGroupedByRegion() {
    try {
      const statistics = await this.culturalElementService.getStatistics();
      const groupedData = [];

      for (const regionInfo of statistics.byRegion) {
        // 这里需要根据区域名称获取区域ID，然后查询该区域下的文化要素
        // 为了简化，我们先返回统计信息
        groupedData.push({
          regionName: regionInfo.regionName,
          count: regionInfo.count,
          // elements: await this.culturalElementService.findByRegion(regionId)
        });
      }

      return ApiResponse.success(groupedData);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 按古城分组获取文化要素
   */
  @Get('/grouped-by-ancient-city')
  async getGroupedByAncientCity() {
    try {
      const statistics = await this.culturalElementService.getStatistics();
      const groupedData = [];

      for (const cityInfo of statistics.byAncientCity) {
        // 这里需要根据古城名称获取古城ID，然后查询该古城下的文化要素
        // 为了简化，我们先返回统计信息
        groupedData.push({
          cityName: cityInfo.cityName,
          count: cityInfo.count,
          // elements: await this.culturalElementService.findByAncientCity(cityId)
        });
      }

      return ApiResponse.success(groupedData);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 搜索文化要素（支持名称和编号模糊搜索）
   */
  @Get('/search')
  @Validate()
  async search(
    @Query() query: { keyword?: string; page?: number; pageSize?: number }
  ) {
    try {
      const searchQuery: CulturalElementQueryDTO = {
        page: query.page || 1,
        pageSize: query.pageSize || 10,
      };

      if (query.keyword) {
        // 同时搜索名称和编号
        searchQuery.name = query.keyword;
        // 注意：这里简化处理，实际应该支持OR查询
      }

      const result = await this.culturalElementService.findByPage(searchQuery);
      return ApiResponse.success(result);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 获取热门文化要素（按某种规则排序，比如访问量、创建时间等）
   */
  @Get('/popular')
  async getPopular(@Query() query: { limit?: number }) {
    try {
      const limit = query.limit || 10;
      // 这里简化处理，返回最新创建的文化要素
      const result = await this.culturalElementService.findByPage({
        page: 1,
        pageSize: limit,
      });

      return ApiResponse.success(result);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }

  /**
   * 获取推荐的文化要素（基于某种推荐算法）
   */
  @Get('/recommended')
  async getRecommended(@Query() query: { limit?: number }) {
    try {
      const limit = query.limit || 5;
      // 这里简化处理，返回随机的文化要素
      const result = await this.culturalElementService.findByPage({
        page: 1,
        pageSize: limit,
      });

      return ApiResponse.success(result);
    } catch (error) {
      return ApiResponse.error(error.message);
    }
  }
}
